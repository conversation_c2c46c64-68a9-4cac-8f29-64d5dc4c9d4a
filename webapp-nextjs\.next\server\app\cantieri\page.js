(()=>{var e={};e.id=222,e.ids=[222],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,r,t)=>{"use strict";t.d(r,{A0:()=>o,BF:()=>i,Hj:()=>l,XI:()=>n,nA:()=>c,nd:()=>d});var a=t(60687);t(43210);var s=t(4780);function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",e),...r})})}function o({className:e,...r}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...r})}function i({className:e,...r}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...r})}function l({className:e,...r}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...r})}function d({className:e,...r}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function c({className:e,...r}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,r,t)=>{"use strict";t.d(r,{UC:()=>et,VY:()=>es,ZL:()=>ee,bL:()=>Y,bm:()=>en,hE:()=>ea,hJ:()=>er,l9:()=>Q});var a=t(43210),s=t(70569),n=t(98599),o=t(11273),i=t(96963),l=t(65551),d=t(31355),c=t(32547),u=t(25028),m=t(46059),p=t(14163),h=t(1359),x=t(42247),f=t(63376),g=t(8730),b=t(60687),v="Dialog",[w,j]=(0,o.A)(v),[N,y]=w(v),C=e=>{let{__scopeDialog:r,children:t,open:s,defaultOpen:n,onOpenChange:o,modal:d=!0}=e,c=a.useRef(null),u=a.useRef(null),[m,p]=(0,l.i)({prop:s,defaultProp:n??!1,onChange:o,caller:v});return(0,b.jsx)(N,{scope:r,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:m,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:d,children:t})};C.displayName=v;var _="DialogTrigger",z=a.forwardRef((e,r)=>{let{__scopeDialog:t,...a}=e,o=y(_,t),i=(0,n.s)(r,o.triggerRef);return(0,b.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":V(o.open),...a,ref:i,onClick:(0,s.m)(e.onClick,o.onOpenToggle)})});z.displayName=_;var P="DialogPortal",[A,k]=w(P,{forceMount:void 0}),E=e=>{let{__scopeDialog:r,forceMount:t,children:s,container:n}=e,o=y(P,r);return(0,b.jsx)(A,{scope:r,forceMount:t,children:a.Children.map(s,e=>(0,b.jsx)(m.C,{present:t||o.open,children:(0,b.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=P;var I="DialogOverlay",D=a.forwardRef((e,r)=>{let t=k(I,e.__scopeDialog),{forceMount:a=t.forceMount,...s}=e,n=y(I,e.__scopeDialog);return n.modal?(0,b.jsx)(m.C,{present:a||n.open,children:(0,b.jsx)(R,{...s,ref:r})}):null});D.displayName=I;var S=(0,g.TL)("DialogOverlay.RemoveScroll"),R=a.forwardRef((e,r)=>{let{__scopeDialog:t,...a}=e,s=y(I,t);return(0,b.jsx)(x.A,{as:S,allowPinchZoom:!0,shards:[s.contentRef],children:(0,b.jsx)(p.sG.div,{"data-state":V(s.open),...a,ref:r,style:{pointerEvents:"auto",...a.style}})})}),F="DialogContent",q=a.forwardRef((e,r)=>{let t=k(F,e.__scopeDialog),{forceMount:a=t.forceMount,...s}=e,n=y(F,e.__scopeDialog);return(0,b.jsx)(m.C,{present:a||n.open,children:n.modal?(0,b.jsx)(M,{...s,ref:r}):(0,b.jsx)($,{...s,ref:r})})});q.displayName=F;var M=a.forwardRef((e,r)=>{let t=y(F,e.__scopeDialog),o=a.useRef(null),i=(0,n.s)(r,t.contentRef,o);return a.useEffect(()=>{let e=o.current;if(e)return(0,f.Eq)(e)},[]),(0,b.jsx)(O,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;(2===r.button||t)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),$=a.forwardRef((e,r)=>{let t=y(F,e.__scopeDialog),s=a.useRef(!1),n=a.useRef(!1);return(0,b.jsx)(O,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{e.onCloseAutoFocus?.(r),r.defaultPrevented||(s.current||t.triggerRef.current?.focus(),r.preventDefault()),s.current=!1,n.current=!1},onInteractOutside:r=>{e.onInteractOutside?.(r),r.defaultPrevented||(s.current=!0,"pointerdown"===r.detail.originalEvent.type&&(n.current=!0));let a=r.target;t.triggerRef.current?.contains(a)&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&n.current&&r.preventDefault()}})}),O=a.forwardRef((e,r)=>{let{__scopeDialog:t,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=y(F,t),m=a.useRef(null),p=(0,n.s)(r,m);return(0,h.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(c.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,b.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":V(u.open),...l,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(X,{titleId:u.titleId}),(0,b.jsx)(K,{contentRef:m,descriptionId:u.descriptionId})]})]})}),G="DialogTitle",L=a.forwardRef((e,r)=>{let{__scopeDialog:t,...a}=e,s=y(G,t);return(0,b.jsx)(p.sG.h2,{id:s.titleId,...a,ref:r})});L.displayName=G;var J="DialogDescription",T=a.forwardRef((e,r)=>{let{__scopeDialog:t,...a}=e,s=y(J,t);return(0,b.jsx)(p.sG.p,{id:s.descriptionId,...a,ref:r})});T.displayName=J;var B="DialogClose",Z=a.forwardRef((e,r)=>{let{__scopeDialog:t,...a}=e,n=y(B,t);return(0,b.jsx)(p.sG.button,{type:"button",...a,ref:r,onClick:(0,s.m)(e.onClick,()=>n.onOpenChange(!1))})});function V(e){return e?"open":"closed"}Z.displayName=B;var W="DialogTitleWarning",[H,U]=(0,o.q)(W,{contentName:F,titleName:G,docsSlug:"dialog"}),X=({titleId:e})=>{let r=U(W),t=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(t))},[t,e]),null},K=({contentRef:e,descriptionId:r})=>{let t=U("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return a.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");r&&t&&(document.getElementById(r)||console.warn(s))},[s,e,r]),null},Y=C,Q=z,ee=E,er=D,et=q,ea=L,es=T,en=Z},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o});var a=t(60687);t(43210);var s=t(4780);function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}},47386:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=t(65239),s=t(48088),n=t(88170),o=t.n(n),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["cantieri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90910)),"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cantieri/page",pathname:"/cantieri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,r,t)=>{"use strict";t.d(r,{Cf:()=>u,Es:()=>p,L3:()=>h,c7:()=>m,lG:()=>i,rr:()=>x,zM:()=>l});var a=t(60687);t(43210);var s=t(26134),n=t(11860),o=t(4780);function i({...e}){return(0,a.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,a.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,a.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...r}){return(0,a.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...r})}function u({className:e,children:r,showCloseButton:t=!0,...i}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(c,{}),(0,a.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...i,children:[r,t&&(0,a.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...r})}function p({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...r})}function h({className:e,...r}){return(0,a.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...r})}function x({className:e,...r}){return(0,a.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...r})}},64021:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},65087:(e,r,t)=>{Promise.resolve().then(t.bind(t,90910))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75863:(e,r,t)=>{Promise.resolve().then(t.bind(t,91106))},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var a=t(60687);t(43210);var s=t(78148),n=t(4780);function o({className:e,...r}){return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(60687);t(43210);var s=t(4780);function n({className:e,type:r,...t}){return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},90910:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx","default")},91106:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>_});var a=t(60687),s=t(43210),n=t(16189),o=t(44493),i=t(29523),l=t(89667),d=t(80013),c=t(6211),u=t(63503),m=t(63213),p=t(62185),h=t(41862),x=t(99270),f=t(96474),g=t(93613),b=t(17313),v=t(64021);let w=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var j=t(13861),N=t(41550),y=t(5336),C=t(99891);function _(){let{user:e,isAuthenticated:r,isLoading:t}=(0,m.A)(),_=(0,n.useRouter)(),[z,P]=(0,s.useState)([]),[A,k]=(0,s.useState)(!0),[E,I]=(0,s.useState)(""),[D,S]=(0,s.useState)(""),[R,F]=(0,s.useState)(!1),[q,M]=(0,s.useState)(!1),[$,O]=(0,s.useState)(!1),[G,L]=(0,s.useState)(null),[J,T]=(0,s.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[B,Z]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[V,W]=(0,s.useState)("change"),[H,U]=(0,s.useState)(""),[X,K]=(0,s.useState)(!1),Y=async()=>{try{k(!0);let e=await p._I.getCantieri();P(e)}catch(e){I("Errore nel caricamento dei cantieri")}finally{k(!1)}},Q=async()=>{try{await p._I.createCantiere(J),F(!1),T({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),Y()}catch(e){I("Errore nella creazione del cantiere")}},ee=async()=>{if(G)try{await p._I.updateCantiere(G.id_cantiere,J),M(!1),L(null),Y()}catch(e){I("Errore nella modifica del cantiere")}},er=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),_.push(`/cantieri/${e.id_cantiere}`)},et=e=>{L(e),T({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),M(!0)},ea=async()=>{if(G)try{k(!0),I("");let e=await fetch(`http://localhost:8001/api/cantieri/${G.id_cantiere}/view-password`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok){let r=await e.json();throw Error(r.detail||"Errore nel recupero password")}let r=await e.json();U(r.password_cantiere),K(!0),I("")}catch(e){I(e instanceof Error?e.message:"Errore nel recupero password"),K(!1)}finally{k(!1)}},es=async()=>{if(G)try{k(!0),I("");let e=await fetch(`http://localhost:8001/api/cantieri/${G.id_cantiere}/send-password-email`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok){let r=await e.json();throw Error(r.detail||"Errore nell'invio email")}let r=await e.json();alert(r.message||"Password inviata via email con successo"),I("")}catch(e){I(e instanceof Error?e.message:"Errore nell'invio email")}finally{k(!1)}},en=async()=>{if(G){if(B.newPassword!==B.confirmPassword)return void I("Le password non coincidono");if(!B.currentPassword)return void I("Inserisci la password attuale per confermare il cambio");if(!B.newPassword||B.newPassword.length<6)return void I("La nuova password deve essere di almeno 6 caratteri");try{k(!0),I("");let e=await fetch(`http://localhost:8001/api/cantieri/${G.id_cantiere}/change-password`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({password_attuale:B.currentPassword,password_nuova:B.newPassword,conferma_password:B.confirmPassword})});if(!e.ok){let r=await e.json();throw Error(r.detail||"Errore nel cambio password")}let r=await e.json();if(r.success)Z({currentPassword:"",newPassword:"",confirmPassword:""}),O(!1),I(""),alert(r.message||"Password cambiata con successo");else throw Error(r.message||"Errore nel cambio password")}catch(e){I(e instanceof Error?e.message:"Errore nel cambio password")}finally{k(!1)}}},eo=z.filter(e=>e.commessa.toLowerCase().includes(D.toLowerCase())||e.descrizione?.toLowerCase().includes(D.toLowerCase())||e.nome_cliente?.toLowerCase().includes(D.toLowerCase()));return t?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):(0,a.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"relative w-80",children:[(0,a.jsx)(x.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(l.p,{placeholder:"Cerca cantieri...",value:D,onChange:e=>S(e.target.value),className:"pl-8 w-full"})]})}),(0,a.jsxs)(u.lG,{open:R,onOpenChange:F,children:[(0,a.jsx)(u.zM,{asChild:!0,children:(0,a.jsxs)(i.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,a.jsxs)(u.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(u.c7,{children:[(0,a.jsx)(u.L3,{children:"Crea Nuovo Cantiere"}),(0,a.jsx)(u.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)(l.p,{id:"commessa",value:J.commessa,onChange:e=>T({...J,commessa:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(l.p,{id:"descrizione",value:J.descrizione,onChange:e=>T({...J,descrizione:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(l.p,{id:"nome_cliente",value:J.nome_cliente,onChange:e=>T({...J,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,a.jsx)(l.p,{id:"password_cantiere",type:"password",value:J.password_cantiere,onChange:e=>T({...J,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,a.jsx)(u.Es,{children:(0,a.jsx)(i.$,{onClick:Q,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),E&&(0,a.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,a.jsx)("span",{className:"text-red-800",children:E})]})}),A?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):0===eo.length?(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(b.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:D?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!D&&(0,a.jsxs)(i.$,{onClick:()=>F(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(c.XI,{children:[(0,a.jsx)(c.A0,{children:(0,a.jsxs)(c.Hj,{children:[(0,a.jsx)(c.nd,{children:"Commessa"}),(0,a.jsx)(c.nd,{children:"Descrizione"}),(0,a.jsx)(c.nd,{children:"Cliente"}),(0,a.jsx)(c.nd,{children:"Data Creazione"}),(0,a.jsx)(c.nd,{children:"Codice"}),(0,a.jsx)(c.nd,{children:"Password"}),(0,a.jsx)(c.nd,{className:"text-right",children:"Azioni"})]})}),(0,a.jsx)(c.BF,{children:eo.map(e=>(0,a.jsxs)(c.Hj,{children:[(0,a.jsx)(c.nA,{className:"font-medium",children:e.commessa}),(0,a.jsx)(c.nA,{children:e.descrizione}),(0,a.jsx)(c.nA,{children:e.nome_cliente}),(0,a.jsx)(c.nA,{children:new Date(e.data_creazione).toLocaleDateString()}),(0,a.jsx)(c.nA,{children:(0,a.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:e.codice_univoco})}),(0,a.jsx)(c.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("code",{className:"text-sm bg-green-100 text-green-800 px-2 py-1 rounded",children:e.password_cantiere?"••••••••":"Non impostata"}),(0,a.jsx)(i.$,{size:"sm",variant:"ghost",className:"text-blue-600 hover:bg-blue-50 p-1",title:"Gestisci password cantiere",onClick:()=>{L(e),O(!0)},children:(0,a.jsx)(v.A,{className:"h-3 w-3"})})]})}),(0,a.jsx)(c.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)(i.$,{size:"sm",onClick:()=>et(e),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",title:"Modifica cantiere",children:(0,a.jsx)(w,{className:"h-3 w-3"})}),(0,a.jsxs)(i.$,{size:"sm",onClick:()=>er(e),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:["Gestisci",(0,a.jsx)(j.A,{className:"ml-2 h-3 w-3"})]})]})})]},e.id_cantiere))})]})}),(0,a.jsx)(u.lG,{open:q,onOpenChange:M,children:(0,a.jsxs)(u.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(u.c7,{children:[(0,a.jsx)(u.L3,{children:"Modifica Cantiere"}),(0,a.jsx)(u.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)(l.p,{id:"edit-commessa",value:J.commessa,onChange:e=>T({...J,commessa:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(l.p,{id:"edit-descrizione",value:J.descrizione,onChange:e=>T({...J,descrizione:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(l.p,{id:"edit-nome_cliente",value:J.nome_cliente,onChange:e=>T({...J,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,a.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:J.indirizzo_cantiere,onChange:e=>T({...J,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,a.jsx)(l.p,{id:"edit-citta_cantiere",value:J.citta_cantiere,onChange:e=>T({...J,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,a.jsx)(l.p,{id:"edit-nazione_cantiere",value:J.nazione_cantiere,onChange:e=>T({...J,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"edit-password_cantiere",className:"text-right",children:"Password"}),(0,a.jsx)(l.p,{id:"edit-password_cantiere",type:"password",value:J.password_cantiere,onChange:e=>T({...J,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,a.jsxs)(u.Es,{children:[(0,a.jsx)(i.$,{onClick:()=>M(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,a.jsx)(i.$,{onClick:ee,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,a.jsx)(u.lG,{open:$,onOpenChange:e=>{O(e),e||(W("change"),K(!1),U(""),Z({currentPassword:"",newPassword:"",confirmPassword:""}))},children:(0,a.jsxs)(u.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsxs)(u.c7,{children:[(0,a.jsxs)(u.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Gestione Password - ",G?.commessa]}),(0,a.jsx)(u.rr,{children:"Scegli come gestire la password del cantiere"})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:[(0,a.jsxs)("button",{onClick:()=>W("change"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"change"===V?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(w,{className:"inline mr-2 h-4 w-4"}),"Cambia"]}),(0,a.jsxs)("button",{onClick:()=>W("recover"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"recover"===V?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(N.A,{className:"inline mr-2 h-4 w-4"}),"Recupera"]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:["change"===V&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,a.jsx)(w,{className:"h-5 w-5"}),"Cambia Password"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,a.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:B.currentPassword,onChange:e=>Z({...B,currentPassword:e.target.value})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,a.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:B.newPassword,onChange:e=>Z({...B,newPassword:e.target.value})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,a.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:B.confirmPassword,onChange:e=>Z({...B,confirmPassword:e.target.value})})]}),(0,a.jsxs)(i.$,{onClick:en,disabled:A||!B.currentPassword||!B.newPassword||!B.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[A?(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(w,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),"recover"===V&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Recupera Password"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Opzioni per recuperare una password dimenticata"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Recupero Diretto"}),(0,a.jsx)("p",{className:"text-sm text-blue-700 mb-3",children:"Tenta di recuperare la password dal sistema (funziona solo se la password \xe8 stata salvata in formato recuperabile)"}),X&&(0,a.jsxs)("div",{className:"mb-3 p-3 bg-green-50 border border-green-200 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"font-medium text-green-800",children:"Password Recuperata"})]}),(0,a.jsx)("code",{className:"text-lg font-mono bg-white p-2 rounded border block",children:H})]}),(0,a.jsxs)(i.$,{onClick:ea,disabled:A,className:"w-full relative overflow-hidden bg-orange-600 hover:bg-orange-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-orange-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[A?(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Recupera Password"]})]}),(0,a.jsxs)("div",{className:"p-4 border border-green-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"Invio via Email"}),(0,a.jsx)("p",{className:"text-sm text-green-700 mb-3",children:"Invia la password all'indirizzo email dell'amministratore del cantiere"}),(0,a.jsxs)(i.$,{onClick:es,disabled:A,className:"w-full relative overflow-hidden bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-green-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[A?(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Invia Password via Email"]})]})]})]}),E&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:E})]})]}),(0,a.jsx)(u.Es,{children:(0,a.jsx)(i.$,{variant:"outline",onClick:()=>O(!1),children:"Chiudi"})})]})})]})}},94735:e=>{"use strict";e.exports=require("events")},96474:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,538,658,952,615],()=>t(47386));module.exports=a})();