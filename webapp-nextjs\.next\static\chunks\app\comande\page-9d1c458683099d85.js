(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[484],{4229:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},13717:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},21815:(e,a,s)=>{Promise.resolve().then(s.bind(s,29020))},24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>l});var t=s(95155);s(12115);var i=s(55863),n=s(59434);function l(e){let{className:a,value:s,...l}=e;return(0,t.jsx)(i.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...l,children:(0,t.jsx)(i.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},28883:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},29020:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Y});var t=s(95155),i=s(12115),n=s(66695),l=s(30285),r=s(26126),o=s(62523),c=s(63743),d=s(85127),m=s(40283),u=s(25731),h=s(54165),x=s(85057),p=s(88539),v=s(59409),g=s(55365),j=s(25273),f=s(85339),N=s(51154),b=s(71007);let _={POSA:"POSA",COLLEGAMENTO_PARTENZA:"COLLEGAMENTO_PARTENZA",COLLEGAMENTO_ARRIVO:"COLLEGAMENTO_ARRIVO",CERTIFICAZIONE:"CERTIFICAZIONE"},A={INSTALLATO:"Installato"};function C(e,a,s){let t={isValid:!0,errors:[],warnings:[],info:[],caviValidi:[],caviProblematici:[]};return e&&0!==e.length?s&&""!==s.trim()?(e.forEach(e=>{let i=function(e,a,s){let t={id_cavo:e.id_cavo,isValid:!0,errors:[],warnings:[],info:[]},i=function(e,a){let s={errors:[],warnings:[],info:[]},t=e.stato_installazione===A.INSTALLATO,i=e.metratura_reale&&parseFloat(e.metratura_reale)>0,n=e.collegamenti&&parseInt(e.collegamenti)>0,l="CERTIFICATO"===e.stato_certificazione;switch(a){case _.POSA:t&&s.errors.push("Cavo ".concat(e.id_cavo," \xe8 gi\xe0 installato e non pu\xf2 essere assegnato a comanda POSA")),i&&s.warnings.push("Cavo ".concat(e.id_cavo," ha gi\xe0 metratura reale registrata"));break;case _.COLLEGAMENTO_PARTENZA:case _.COLLEGAMENTO_ARRIVO:t||i||s.warnings.push("Cavo ".concat(e.id_cavo," non risulta installato. Verificare prerequisiti.")),n&&s.warnings.push("Cavo ".concat(e.id_cavo," risulta gi\xe0 collegato"));break;case _.CERTIFICAZIONE:t||s.errors.push("Cavo ".concat(e.id_cavo," deve essere installato per la certificazione")),n||s.warnings.push("Cavo ".concat(e.id_cavo," non risulta collegato. Verificare prerequisiti.")),l&&s.warnings.push("Cavo ".concat(e.id_cavo," \xe8 gi\xe0 certificato"))}return s}(e,a);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info);let n=function(e,a){let s={errors:[],warnings:[],info:[]};switch(a){case _.POSA:e.comanda_posa&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda POSA assegnata: ").concat(e.comanda_posa));break;case _.COLLEGAMENTO_PARTENZA:e.comanda_partenza&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda COLLEGAMENTO_PARTENZA assegnata: ").concat(e.comanda_partenza));break;case _.COLLEGAMENTO_ARRIVO:e.comanda_arrivo&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda COLLEGAMENTO_ARRIVO assegnata: ").concat(e.comanda_arrivo));break;case _.CERTIFICAZIONE:e.comanda_certificazione&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda CERTIFICAZIONE assegnata: ").concat(e.comanda_certificazione))}return s}(e,a);t.errors.push(...n.errors),t.warnings.push(...n.warnings),t.info.push(...n.info);let l=function(e,a){let s={warnings:[],info:[]};switch(a){case _.COLLEGAMENTO_PARTENZA:case _.COLLEGAMENTO_ARRIVO:!e.comanda_posa&&(!e.metratura_reale||0>=parseFloat(e.metratura_reale))&&s.warnings.push("Cavo ".concat(e.id_cavo," non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti."));break;case _.CERTIFICAZIONE:e.comanda_partenza||e.comanda_arrivo||s.warnings.push("Cavo ".concat(e.id_cavo," non ha comande di collegamento assegnate. Verificare prerequisiti."))}return s}(e,a);t.warnings.push(...l.warnings),t.info.push(...l.info);let r=function(e,a,s){let t={warnings:[],info:[]},i=[...new Set(Object.values({posa:e.responsabile_posa||"",partenza:e.responsabile_partenza||"",arrivo:e.responsabile_arrivo||"",certificazione:e.responsabile_certificazione||""}).filter(e=>e&&""!==e.trim()))];return i.length>1&&!i.includes(s)&&t.warnings.push("Cavo ".concat(e.id_cavo," ha gi\xe0 responsabili diversi (").concat(i.join(", "),"). Nuovo responsabile: ").concat(s)),t}(e,0,s);return t.warnings.push(...r.warnings),t.info.push(...r.info),t.isValid=0===t.errors.length,t}(e,a,s);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info),i.isValid?t.caviValidi.push(e):t.caviProblematici.push({cavo:e,issues:i.errors})}),t.isValid=0===t.errors.length):(t.errors.push("Responsabile non specificato"),t.isValid=!1):(t.errors.push("Nessun cavo selezionato per la comanda"),t.isValid=!1),t}function y(e){let a=[];return e.errors.length>0&&(a.push("❌ Errori (".concat(e.errors.length,"):")),e.errors.forEach(e=>a.push("  • ".concat(e)))),e.warnings.length>0&&(a.push("⚠️ Avvisi (".concat(e.warnings.length,"):")),e.warnings.forEach(e=>a.push("  • ".concat(e)))),e.info.length>0&&(a.push("ℹ️ Informazioni (".concat(e.info.length,"):")),e.info.forEach(e=>a.push("  • ".concat(e)))),e.caviValidi.length>0&&a.push("✅ Cavi validi: ".concat(e.caviValidi.length)),e.caviProblematici.length>0&&a.push("❌ Cavi problematici: ".concat(e.caviProblematici.length)),a.join("\n")}function E(e){let{open:a,onClose:s,caviSelezionati:n=[],tipoComanda:r,onSuccess:c,onError:d,onComandaCreated:_}=e,[A,E]=(0,i.useState)(!1),[w,O]=(0,i.useState)(""),[S,z]=(0,i.useState)([]),[I,T]=(0,i.useState)(!1),[R,k]=(0,i.useState)(""),[L,M]=(0,i.useState)(!1),{cantiere:V}=(0,m.A)(),[P,F]=(0,i.useState)(0);(0,i.useEffect)(()=>{F((null==V?void 0:V.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[V]);let[G,Z]=(0,i.useState)({tipo_comanda:r||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1});(0,i.useEffect)(()=>{a&&P>0&&D()},[a,P]),(0,i.useEffect)(()=>{a||(Z({tipo_comanda:r||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1}),O(""))},[a,r]);let D=async()=>{try{T(!0);let e=await u.AR.getResponsabili(P),a=(null==e?void 0:e.data)||e||[];z(Array.isArray(a)?a:[])}catch(e){z([])}finally{T(!1)}},B=async()=>{var e,a,t;try{let a;if(E(!0),O(""),!G.tipo_comanda)return void O("Seleziona il tipo di comanda");if(!G.responsabile)return void O("Seleziona un responsabile");if(!P||P<=0)return void O("Cantiere non selezionato");if(n.length>0){let e=C(n,G.tipo_comanda,G.responsabile);if(!e.isValid){O("Validazione cavi fallita. Controllare i dettagli nella sezione validazione."),k(y(e)),M(!0);return}e.warnings.length>0&&(k(y(e)),M(!0))}let t={tipo_comanda:G.tipo_comanda,responsabile:G.responsabile,descrizione:G.descrizione||null,data_scadenza:G.data_scadenza||null,numero_componenti_squadra:G.numero_componenti_squadra},i=(null==(a=n&&n.length>0?await u.CV.createComandaWithCavi(P,t,n):await u.CV.createComanda(P,t))||null==(e=a.data)?void 0:e.codice_comanda)||(null==a?void 0:a.codice_comanda),l=n&&n.length>0?"Comanda ".concat(i," creata con successo per ").concat(n.length," cavi"):"Comanda ".concat(i," creata con successo");c(l),null==_||_(),s()}catch(e){d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante la creazione della comanda")}finally{E(!1)}};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsx)(h.rr,{children:n&&n.length>0?"Crea una comanda per ".concat(n.length," cavi selezionati"):"Crea una nuova comanda di lavoro"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[w&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:w})]}),n.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"text-sm font-medium",children:["Validazione Cavi (",n.length," selezionati)"]}),(0,t.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(0===n.length){k("Nessun cavo selezionato per la validazione"),M(!0);return}k(y(C(n,G.tipo_comanda,G.responsabile))),M(!0)},disabled:!G.tipo_comanda||!G.responsabile,children:"Valida Cavi"})]}),L&&R&&(0,t.jsxs)(g.Fc,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap text-xs font-mono",children:R})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(v.l6,{value:G.tipo_comanda,onValueChange:e=>Z(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsx)(v.gC,{children:[{value:"POSA",label:"\uD83D\uDD27 Posa Cavi",description:"Installazione e posa dei cavi"},{value:"COLLEGAMENTO_PARTENZA",label:"\uD83D\uDD0C Collegamento Partenza",description:"Collegamento lato partenza"},{value:"COLLEGAMENTO_ARRIVO",label:"⚡ Collegamento Arrivo",description:"Collegamento lato arrivo"},{value:"CERTIFICAZIONE",label:"\uD83D\uDCCB Certificazione",description:"Test e certificazione"}].map(e=>(0,t.jsx)(v.eb,{value:e.value,children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.label}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.description})]})},e.value))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"responsabile",children:"Responsabile *"}),I?(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 text-sm text-slate-500",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]}):(0,t.jsxs)(v.l6,{value:G.responsabile,onValueChange:e=>Z(a=>({...a,responsabile:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile"})}),(0,t.jsx)(v.gC,{children:S.map(e=>(0,t.jsx)(v.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.numero_telefono})]})]})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"descrizione",children:"Descrizione"}),(0,t.jsx)(p.T,{id:"descrizione",placeholder:"Descrizione opzionale della comanda...",value:G.descrizione,onChange:e=>Z(a=>({...a,descrizione:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,t.jsx)(o.p,{id:"data_scadenza",type:"date",value:G.data_scadenza,onChange:e=>Z(a=>({...a,data_scadenza:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"squadra",children:"Componenti Squadra"}),(0,t.jsx)(o.p,{id:"squadra",type:"number",min:"1",max:"20",value:G.numero_componenti_squadra,onChange:e=>Z(a=>({...a,numero_componenti_squadra:parseInt(e.target.value)||1}))})]})]}),n&&n.length>0&&(0,t.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"font-medium",children:["Cavi da assegnare: ",n.length]})]}),(0,t.jsx)("div",{className:"text-sm text-blue-600 mt-1",children:"I cavi selezionati verranno automaticamente assegnati a questa comanda"})]})]}),(0,t.jsxs)(h.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,disabled:A,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:B,disabled:A,children:[A&&(0,t.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Crea Comanda"]})]})]})})}var w=s(17580),O=s(84616),S=s(54416),z=s(4229),I=s(19946);let T=(0,I.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var R=s(28883),k=s(13717),L=s(62525);function M(e){let{open:a,onClose:s,onSuccess:r,onError:c}=e,[d,p]=(0,i.useState)(!1),[v,j]=(0,i.useState)(""),[_,A]=(0,i.useState)([]),[C,y]=(0,i.useState)(null),[E,I]=(0,i.useState)(!1),{cantiere:M}=(0,m.A)(),[V,P]=(0,i.useState)(0);(0,i.useEffect)(()=>{P((null==M?void 0:M.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[M]);let[F,G]=(0,i.useState)({nome_responsabile:"",numero_telefono:"",mail:""});(0,i.useEffect)(()=>{a&&V>0&&Z()},[a,V]),(0,i.useEffect)(()=>{a||(G({nome_responsabile:"",numero_telefono:"",mail:""}),j(""),y(null),I(!1))},[a]);let Z=async()=>{try{p(!0);let e=await u.AR.getResponsabili(V),a=(null==e?void 0:e.data)||e||[];A(Array.isArray(a)?a:[])}catch(e){j("Errore durante il caricamento dei responsabili")}finally{p(!1)}},D=async()=>{try{p(!0),j("");let e={nome_responsabile:F.nome_responsabile.trim(),numero_telefono:F.numero_telefono.trim()||null,mail:F.mail.trim()||null},a=function(e){var a,s;let t=[];return e.nome_responsabile&&e.nome_responsabile.trim()||t.push("Il nome del responsabile \xe8 obbligatorio"),e.mail||e.numero_telefono||t.push("Almeno uno tra email e telefono deve essere specificato"),e.mail&&(a=e.mail,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))&&t.push("Formato email non valido"),e.numero_telefono&&(s=e.numero_telefono,!/^[\+]?[0-9\s\-\(\)]{8,15}$/.test(s.replace(/\s/g,"")))&&t.push("Formato telefono non valido"),{isValid:0===t.length,errors:t}}(e);if(!a.isValid)return void j("Errori di validazione: ".concat(a.errors.join(", ")));await u.AR.createResponsabile(V,e),r("Responsabile aggiunto con successo"),G({nome_responsabile:"",numero_telefono:"",mail:""}),I(!1),Z()}catch(s){var e,a;j((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante la creazione del responsabile")}finally{p(!1)}},B=e=>{G({nome_responsabile:e.nome_responsabile,numero_telefono:e.numero_telefono||"",mail:e.mail||""}),y(e.id_responsabile),I(!1)},$=async()=>{if(C)try{if(p(!0),j(""),!F.nome_responsabile.trim())return void j("Il nome del responsabile \xe8 obbligatorio");if(F.mail&&!F.mail.includes("@"))return void j("Inserisci un indirizzo email valido");let e={nome_responsabile:F.nome_responsabile.trim(),numero_telefono:F.numero_telefono.trim()||null,mail:F.mail.trim()||null};await u.AR.updateResponsabile(V,C,e),r("Responsabile aggiornato con successo"),G({nome_responsabile:"",numero_telefono:"",mail:""}),y(null),Z()}catch(s){var e,a;j((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante l'aggiornamento del responsabile")}finally{p(!1)}},q=async(e,a)=>{if(confirm('Sei sicuro di voler eliminare il responsabile "'.concat(a,'"?')))try{p(!0),await u.AR.deleteResponsabile(V,e),r("Responsabile eliminato con successo"),Z()}catch(e){var s,t;c((null==(t=e.response)||null==(s=t.data)?void 0:s.detail)||"Errore durante l'eliminazione del responsabile")}finally{p(!1)}},J=()=>{y(null),I(!1),G({nome_responsabile:"",numero_telefono:"",mail:""}),j("")};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"sm:max-w-[700px] max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5"}),"Gestisci Responsabili"]}),(0,t.jsxs)(h.rr,{children:["Gestisci i responsabili per il cantiere ",localStorage.getItem("selectedCantiereName")||V]})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[v&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:v})]}),!E&&!C&&(0,t.jsxs)(l.$,{onClick:()=>I(!0),className:"w-full",variant:"outline",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Aggiungi Nuovo Responsabile"]}),(E||C)&&(0,t.jsx)(n.Zp,{className:"border-2 border-blue-200",children:(0,t.jsxs)(n.Wu,{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:C?"Modifica Responsabile":"Nuovo Responsabile"}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:J,children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"nome",children:"Nome Responsabile *"}),(0,t.jsx)(o.p,{id:"nome",placeholder:"Nome e cognome",value:F.nome_responsabile,onChange:e=>G(a=>({...a,nome_responsabile:e.target.value}))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"telefono",children:"Numero Telefono"}),(0,t.jsx)(o.p,{id:"telefono",placeholder:"+39 ************",value:F.numero_telefono,onChange:e=>G(a=>({...a,numero_telefono:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:F.mail,onChange:e=>G(a=>({...a,mail:e.target.value}))})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,t.jsxs)(l.$,{onClick:C?$:D,disabled:d,className:"flex-1",children:[d&&(0,t.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(z.A,{className:"mr-2 h-4 w-4"}),C?"Aggiorna":"Aggiungi"]}),(0,t.jsx)(l.$,{variant:"outline",onClick:J,children:"Annulla"})]})]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-4 w-4"}),"Responsabili Esistenti (",_.length,")"]}),d&&0===_.length?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]})}):0===_.length?(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun responsabile trovato"}):(0,t.jsx)("div",{className:"space-y-2",children:_.map(e=>(0,t.jsx)(n.Zp,{className:C===e.id_responsabile?"border-blue-300":"",children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.nome_responsabile})]}),(0,t.jsxs)("div",{className:"space-y-1 text-sm text-slate-600",children:[e.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T,{className:"h-3 w-3"}),e.numero_telefono]}),e.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),e.mail]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>B(e),disabled:d||C===e.id_responsabile,children:(0,t.jsx)(k.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>q(e.id_responsabile,e.nome_responsabile),disabled:d,className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]})})},e.id_responsabile))})]})]}),(0,t.jsx)(h.Es,{children:(0,t.jsx)(l.$,{variant:"outline",onClick:s,children:"Chiudi"})})]})})}var V=s(24944),P=s(79397),F=s(69074),G=s(14186),Z=s(40646),D=s(3493);function B(e){let{open:a,onClose:s,codiceComanda:o,onSuccess:d,onError:x}=e,[p,v]=(0,i.useState)(!1),[_,A]=(0,i.useState)(""),[C,y]=(0,i.useState)(null),{cantiere:E}=(0,m.A)(),[O,S]=(0,i.useState)(0);(0,i.useEffect)(()=>{S((null==E?void 0:E.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[E]),(0,i.useEffect)(()=>{a&&o&&O>0&&z()},[a,o,O]),(0,i.useEffect)(()=>{a||(y(null),A(""))},[a]);let z=async()=>{if(o)try{v(!0),A("");let e=await u.CV.getComanda(O,o),a=(null==e?void 0:e.data)||e;y(a)}catch(e){A("Errore durante il caricamento dei dettagli della comanda")}finally{v(!1)}},I=e=>new Date(e).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return o?(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Dettagli Comanda ",o]}),(0,t.jsx)(h.rr,{children:"Visualizza tutti i dettagli e lo stato della comanda"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[_&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:_})]}),p?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5 animate-spin"}),"Caricamento dettagli comanda..."]})}):C?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-5 w-5"}),"Informazioni Generali"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(e=>{let a={POSA:{label:"Posa Cavi",icon:"\uD83D\uDD27"},COLLEGAMENTO_PARTENZA:{label:"Collegamento Partenza",icon:"\uD83D\uDD0C"},COLLEGAMENTO_ARRIVO:{label:"Collegamento Arrivo",icon:"⚡"},CERTIFICAZIONE:{label:"Certificazione",icon:"\uD83D\uDCCB"}}[e]||{label:e,icon:"❓"};return(0,t.jsxs)(r.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200",children:[a.icon," ",a.label]})})(C.tipo_comanda),(e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(r.E,{className:a.badge,children:e})})(C.stato)]})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Data Creazione"}),(0,t.jsx)("p",{className:"font-medium",children:I(C.data_creazione)})]})]}),C.data_scadenza&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Scadenza"}),(0,t.jsx)("p",{className:"font-medium",children:I(C.data_scadenza)})]})]}),C.data_completamento&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Completamento"}),(0,t.jsx)("p",{className:"font-medium text-green-700",children:I(C.data_completamento)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Componenti Squadra"}),(0,t.jsxs)("p",{className:"font-medium",children:[C.numero_componenti_squadra," persone"]})]})]}),C.descrizione&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Descrizione"}),(0,t.jsx)("p",{className:"font-medium",children:C.descrizione})]})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),"Responsabile"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"flex items-start gap-4",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-lg",children:C.responsabile||"Non assegnato"}),C.responsabile_dettagli&&(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-slate-600",children:[C.responsabile_dettagli.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T,{className:"h-3 w-3"}),C.responsabile_dettagli.numero_telefono]}),C.responsabile_dettagli.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),C.responsabile_dettagli.mail]})]})]})})})]}),C.progresso&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-5 w-5"}),"Progresso Lavori"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[C.progresso.percentuale,"%"]})]}),(0,t.jsx)(V.k,{value:C.progresso.percentuale,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-slate-600",children:[(0,t.jsxs)("span",{children:[C.progresso.completati," completati"]}),(0,t.jsxs)("span",{children:[C.progresso.totale," totali"]})]})]})})]}),C.cavi_assegnati&&C.cavi_assegnati.length>0&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(D.A,{className:"h-5 w-5"}),"Cavi Assegnati (",C.cavi_assegnati.length,")"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:C.cavi_assegnati.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-slate-50 rounded",children:[(0,t.jsx)("span",{className:"font-mono text-sm",children:e.id_cavo||e}),e.stato&&(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:e.stato})]},a))})})]})]}):(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun dettaglio disponibile"})]}),(0,t.jsxs)(h.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,children:"Chiudi"}),C&&(0,t.jsx)(l.$,{onClick:()=>{d("Funzione di modifica in sviluppo")},children:"Modifica Comanda"})]})]})}):null}var $=s(1243),q=s(87481);function J(e){let{open:a,onClose:s,codiceComanda:n,tipoComanda:c,onSuccess:d,onError:m}=e,[p,v]=(0,i.useState)([]),[g,j]=(0,i.useState)({}),[f,b]=(0,i.useState)(!1),[_,A]=(0,i.useState)(!1),[C,y]=(0,i.useState)(null),{toast:E}=(0,q.dj)();(0,i.useEffect)(()=>{a&&n&&w()},[a,n]);let w=async()=>{var e,a,s;try{if(b(!0),y(null),!localStorage.getItem("selectedCantiereId"))throw Error("Nessun cantiere selezionato");let a=await u.CV.getCaviComanda(n);v(a.data.cavi||[]);let s={};null==(e=a.data.cavi)||e.forEach(e=>{s[e.id_cavo]={metratura_reale:e.metratura_reale||0,numero_persone_impiegate:1,sistemazione:"",fascettatura:""}}),j(s)}catch(t){let e=(null==(s=t.response)||null==(a=s.data)?void 0:a.detail)||t.message||"Errore nel caricamento dei cavi";y(e),null==m||m(e)}finally{b(!1)}},O=(e,a,s)=>{j(t=>({...t,[e]:{...t[e],[a]:s}}))},S=async()=>{try{A(!0),y(null);let e="",a={};"POSA"===c?(e="dati-posa",a={dati_posa:g}):("COLLEGAMENTO_PARTENZA"===c||"COLLEGAMENTO_ARRIVO"===c)&&(e="dati-collegamento",a={dati_collegamento:g}),await u.CV.updateDatiComanda(n,e,a);let t="POSA"===c?"Metri posati inseriti con successo":"Metri collegati inseriti con successo";null==d||d(t),E({title:"Successo",description:t}),s()}catch(t){var e,a;let s=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore nel salvataggio";y(s),null==m||m(s),E({title:"Errore",description:s,variant:"destructive"})}finally{A(!1)}};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 text-blue-600"}),(()=>{switch(c){case"POSA":return"Inserisci Metri Posati";case"COLLEGAMENTO_PARTENZA":return"Inserisci Metri Collegati - Partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci Metri Collegati - Arrivo";default:return"Inserisci Metri"}})()]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(()=>{switch(c){case"POSA":return"Inserisci i metri realmente posati per ogni cavo";case"COLLEGAMENTO_PARTENZA":return"Inserisci i metri collegati lato partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci i metri collegati lato arrivo";default:return"Inserisci i metri"}})()," - Comanda: ",n]})]}),f?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(N.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):C?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8 text-red-600",children:[(0,t.jsx)($.A,{className:"h-5 w-5 mr-2"}),C]}):0===p.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nessun cavo trovato per questa comanda"}):(0,t.jsx)("div",{className:"space-y-4",children:p.map(e=>{var a,s,i,n;return(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-600",children:e.id_cavo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsx)(r.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"metri-".concat(e.id_cavo),children:"POSA"===c?"Metri Posati":"Metri Collegati"}),(0,t.jsx)(o.p,{id:"metri-".concat(e.id_cavo),type:"number",min:"0",step:"0.1",value:(null==(a=g[e.id_cavo])?void 0:a.metratura_reale)||0,onChange:a=>O(e.id_cavo,"metratura_reale",parseFloat(a.target.value)||0),className:"mt-1"})]}),"POSA"===c&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"persone-".concat(e.id_cavo),children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:"persone-".concat(e.id_cavo),type:"number",min:"1",value:(null==(s=g[e.id_cavo])?void 0:s.numero_persone_impiegate)||1,onChange:a=>O(e.id_cavo,"numero_persone_impiegate",parseInt(a.target.value)||1),className:"mt-1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"sistemazione-".concat(e.id_cavo),children:"Sistemazione"}),(0,t.jsx)(o.p,{id:"sistemazione-".concat(e.id_cavo),value:(null==(i=g[e.id_cavo])?void 0:i.sistemazione)||"",onChange:a=>O(e.id_cavo,"sistemazione",a.target.value),className:"mt-1",placeholder:"Es: Interrato, Aereo..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"fascettatura-".concat(e.id_cavo),children:"Fascettatura"}),(0,t.jsx)(o.p,{id:"fascettatura-".concat(e.id_cavo),value:(null==(n=g[e.id_cavo])?void 0:n.fascettatura)||"",onChange:a=>O(e.id_cavo,"fascettatura",a.target.value),className:"mt-1",placeholder:"Es: Standard, Rinforzata..."})]})]})]})]},e.id_cavo)})}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,disabled:_,children:"Annulla"}),(0,t.jsx)(l.$,{onClick:S,disabled:_||0===p.length,className:"bg-blue-600 hover:bg-blue-700",children:_?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):"Salva Metri"})]})]})})}var U=s(47262);let H=(0,I.A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);function W(e){let{open:a,onClose:s,codiceComanda:n,onSuccess:c,onError:d}=e,[p,j]=(0,i.useState)([]),[b,_]=(0,i.useState)([]),[A,C]=(0,i.useState)({}),[y,E]=(0,i.useState)({}),[w,O]=(0,i.useState)(!1),[S,z]=(0,i.useState)(!1),[I,T]=(0,i.useState)(null),[R,k]=(0,i.useState)(""),[L,M]=(0,i.useState)(0),[V,P]=(0,i.useState)([]),[F,G]=(0,i.useState)(null),{toast:D}=(0,q.dj)(),{cantiere:B}=(0,m.A)();(0,i.useEffect)(()=>{a&&n&&J()},[a,n]),(0,i.useEffect)(()=>{a&&(null==B?void 0:B.id_cantiere)&&W()},[a,null==B?void 0:B.id_cantiere]),(0,i.useEffect)(()=>{a||$()},[a]);let $=()=>{j([]),_([]),C({}),E({}),T(null),k(""),M(0),P([]),G(null)},J=async()=>{try{O(!0),T(null);let e=await u.CV.getCaviComanda(n),a=((null==e?void 0:e.data)||e||[]).filter(e=>"Installato"!==e.stato_installazione);j(a);let s={};a.forEach(e=>{s[e.id_cavo]={metratura_reale:0,numero_persone_impiegate:1,sistemazione:!1,fascettatura:!1,id_bobina:"BOBINA_VUOTA"}}),C(s)}catch(e){T("Errore durante il caricamento dei cavi della comanda")}finally{O(!1)}},W=async()=>{try{if(!(null==B?void 0:B.id_cantiere))return;let e=await u.Fw.getBobine(B.id_cantiere),a=((null==e?void 0:e.data)||e||[]).filter(e=>"disponibile"===e.stato&&e.metri_residui>0);_(a)}catch(e){T("Errore durante il caricamento delle bobine")}},X=(e,a)=>{let s=parseFloat(a)||0;C(a=>{let t={...a,[e]:{...a[e],metratura_reale:s}};if(R&&"BOBINA_VUOTA"!==R){let a=b.find(e=>e.id_bobina===R);if(a){let s=0;Object.keys(t).forEach(e=>{var a;let i=(null==(a=t[e])?void 0:a.metratura_reale)||0;i>0&&(s+=i)});let i=a.metri_residui-s;if(M(i),i<0&&!F){G(e);let a=[];Object.keys(t).forEach(s=>{var i;0===((null==(i=t[s])?void 0:i.metratura_reale)||0)&&s!==e&&a.push(s)}),P(a)}else i>=0&&F===e&&(G(null),P([]))}}return t}),Q(e,s)},Q=(e,a)=>{let s=p.find(a=>a.id_cavo===e);s&&E(t=>{let i={...t};return delete i[e],a>0&&s.metratura_teorica,i})},Y=(e,a)=>{let s=parseInt(a)||1;C(a=>({...a,[e]:{...a[e],numero_persone_impiegate:s}}))},K=(e,a)=>{C(s=>({...s,[e]:{...s[e],sistemazione:a}}))},ee=(e,a)=>{C(s=>({...s,[e]:{...s[e],fascettatura:a}}))},ea=async()=>{try{if(z(!0),T(null),Object.keys(y).length>0)return void T("Correggere gli errori di validazione prima di salvare");let e={};if(Object.keys(A).forEach(a=>{let s=A[a];if(((null==s?void 0:s.metratura_reale)||0)>0){let t=F===a||L<0;e[a]={...s,id_bobina:R||"BOBINA_VUOTA",force_over:t}}}),0===Object.keys(e).length)return void T("Inserire almeno un metro per almeno un cavo");console.log({codiceComanda:n,caviDaSalvare:Object.keys(e).length,datiPosaFiltrati:e,selectedBobina:R,metriResiduiSimulati:L,cavoCheCausaOver:F});let a=await fetch("/api/comande/".concat(n,"/dati-posa-bobine"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Errore durante il salvataggio")}let t="Metri posati inseriti con successo per ".concat(Object.keys(e).length," cavi");null==c||c(t),D({title:"Successo",description:t}),s()}catch(t){var e,a;let s=(null==t||null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il salvataggio dei metri posati";T(s),null==d||d(s)}finally{z(!1)}};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(H,{className:"h-5 w-5 text-blue-600"}),"Inserisci Metri Posati - Comanda ",n]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci i metri posati per ogni cavo della comanda POSA"})]}),I&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:I})]}),w?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(N.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-3",children:"Selezione Bobina Principale"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"bobina-principale",children:"Bobina da Utilizzare"}),(0,t.jsxs)(v.l6,{value:R,onValueChange:e=>{if(k(e),P([]),G(null),C(a=>{let s={...a};return Object.keys(s).forEach(a=>{s[a]={...s[a],id_bobina:e}}),s}),e&&"BOBINA_VUOTA"!==e){let a=b.find(a=>a.id_bobina===e);a&&M(a.metri_residui)}else M(0)},children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona bobina principale..."})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"BOBINA_VUOTA",children:"\uD83D\uDD04 BOBINA_VUOTA (Assegna dopo)"}),b.map(e=>(0,t.jsxs)(v.eb,{value:e.id_bobina,children:["✅ ",e.id_bobina," - ",e.tipologia," ",e.sezione," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]}),R&&"BOBINA_VUOTA"!==R&&(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Metri Residui: "}),(0,t.jsxs)("span",{className:L<0?"text-red-600 font-bold":"text-green-600",children:[L.toFixed(1),"m"]})]}),L<0&&(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"font-medium",children:["Cavi da Installare (",p.length,")"]}),p.map(e=>{let a=A[e.id_cavo],s=y[e.id_cavo],i=V.includes(e.id_cavo),n=F===e.id_cavo,l=L<0&&"BOBINA_VUOTA"!==R;return(0,t.jsxs)("div",{className:"border rounded-lg p-4 space-y-4 ".concat(i?"bg-red-50 border-red-200":n?"bg-orange-50 border-orange-200":""),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[e.id_cavo,i&&(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"BLOCCATO"}),n&&(0,t.jsx)(r.E,{variant:"outline",className:"text-xs border-orange-500 text-orange-700",children:"CAUSA OVER"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione}),l&&(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"metri-".concat(e.id_cavo),children:"Metri Posati *"}),(0,t.jsx)(o.p,{id:"metri-".concat(e.id_cavo),type:"number",min:"0",step:"0.1",value:(null==a?void 0:a.metratura_reale)||"",onChange:a=>X(e.id_cavo,a.target.value),className:s?"border-red-500":i?"border-red-300 bg-red-50":"",placeholder:i?"Bloccato (OVER)":"0.0",disabled:i}),s&&(0,t.jsx)("p",{className:"text-xs text-red-500 mt-1",children:s}),i&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"⚠️ Cavo bloccato: bobina in stato OVER"}),n&&!i&&(0,t.jsx)("p",{className:"text-xs text-orange-600 mt-1",children:"⚠️ Questo cavo causa lo stato OVER della bobina"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"persone-".concat(e.id_cavo),children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:"persone-".concat(e.id_cavo),type:"number",min:"1",value:(null==a?void 0:a.numero_persone_impiegate)||1,onChange:a=>Y(e.id_cavo,a.target.value)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(U.S,{id:"sistemazione-".concat(e.id_cavo),checked:(null==a?void 0:a.sistemazione)||!1,onCheckedChange:a=>K(e.id_cavo,!!a)}),(0,t.jsx)(x.J,{htmlFor:"sistemazione-".concat(e.id_cavo),children:"Sistemazione"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(U.S,{id:"fascettatura-".concat(e.id_cavo),checked:(null==a?void 0:a.fascettatura)||!1,onCheckedChange:a=>ee(e.id_cavo,!!a)}),(0,t.jsx)(x.J,{htmlFor:"fascettatura-".concat(e.id_cavo),children:"Fascettatura"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Bobina assegnata: "}),(0,t.jsx)("span",{className:"BOBINA_VUOTA"===R?"text-orange-600":"text-blue-600",children:R||"Nessuna"})]})]},e.id_cavo)})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,children:"Annulla"}),(0,t.jsx)(l.$,{onClick:ea,disabled:S||w||0===p.length,children:S?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Salva Metri Posati"]})})]})]})})}var X=s(47924),Q=s(92657);function Y(){let[e,a]=(0,i.useState)("active"),[s,h]=(0,i.useState)([]),[x,p]=(0,i.useState)([]),[v,g]=(0,i.useState)(!0),[f,b]=(0,i.useState)(""),[_,A]=(0,i.useState)(""),[C,y]=(0,i.useState)("all"),[S,z]=(0,i.useState)("all"),[I,T]=(0,i.useState)(!1),[R,V]=(0,i.useState)(!1),[P,F]=(0,i.useState)(!1),[G,Z]=(0,i.useState)(!1),[D,U]=(0,i.useState)(!1),[H,Y]=(0,i.useState)(null),[K,ee]=(0,i.useState)(null),{user:ea,cantiere:es}=(0,m.A)(),{toast:et}=(0,q.dj)(),[ei,en]=(0,i.useState)(0);(0,i.useEffect)(()=>{en((null==es?void 0:es.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[es]),(0,i.useEffect)(()=>{ei&&ei>0&&el()},[ei]);let el=async()=>{var e,a,s;try{if(g(!0),b(""),!ei||ei<=0)return void b("Cantiere non selezionato");let[a,s]=await Promise.all([u.CV.getComande(ei),u.AR.getResponsabili(ei)]),t=(null==a||null==(e=a.data)?void 0:e.comande)||(null==a?void 0:a.comande)||(null==a?void 0:a.data)||a||[],i=(null==s?void 0:s.data)||s||[];h(Array.isArray(t)?t:[]),p(Array.isArray(i)?i:[])}catch(e){b((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante il caricamento dei dati")}finally{g(!1)}},er=e=>{et({title:"Successo",description:e}),el()},eo=e=>{et({title:"Errore",description:e,variant:"destructive"})},ec=async e=>{if(confirm("Sei sicuro di voler eliminare la comanda ".concat(e,"?")))try{g(!0),await u.CV.deleteComanda(ei,e),er("Comanda ".concat(e," eliminata con successo"))}catch(e){eo("Errore durante l'eliminazione della comanda")}finally{g(!1)}},ed=e=>{switch(e){case"COMPLETATA":return(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"IN_CORSO":return(0,t.jsx)(r.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"ASSEGNATA":return(0,t.jsx)(r.E,{className:"bg-yellow-100 text-yellow-800",children:"Assegnata"});case"CREATA":return(0,t.jsx)(r.E,{className:"bg-gray-100 text-gray-800",children:"Creata"});case"ANNULLATA":return(0,t.jsx)(r.E,{className:"bg-red-100 text-red-800",children:"Annullata"});default:return(0,t.jsx)(r.E,{variant:"secondary",children:e})}},em=e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(r.E,{className:a.badge,children:{POSA:"\uD83D\uDD27 Posa",COLLEGAMENTO_PARTENZA:"\uD83D\uDD0C Coll. Partenza",COLLEGAMENTO_ARRIVO:"⚡ Coll. Arrivo",CERTIFICAZIONE:"\uD83D\uDCCB Certificazione"}[e]||e.replace(/_/g," ")})},eu=Array.isArray(s)?s.filter(a=>{let s=!0;switch(e){case"active":s="IN_CORSO"===a.stato||"ASSEGNATA"===a.stato||"CREATA"===a.stato;break;case"completed":s="COMPLETATA"===a.stato;break;default:s=!0}let t=""===_||a.codice_comanda.toLowerCase().includes(_.toLowerCase())||a.descrizione&&a.descrizione.toLowerCase().includes(_.toLowerCase())||a.responsabile&&a.responsabile.toLowerCase().includes(_.toLowerCase()),i="all"===C||a.responsabile===C,n="all"===S||a.tipo_comanda===S;return s&&t&&i&&n}):[],eh={totali:Array.isArray(s)?s.length:0,in_corso:Array.isArray(s)?s.filter(e=>"IN_CORSO"===e.stato).length:0,completate:Array.isArray(s)?s.filter(e=>"COMPLETATA"===e.stato).length:0,pianificate:Array.isArray(s)?s.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length:0,filtrate:eu.length};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Gestione Comande"}),(0,t.jsx)("p",{className:"text-slate-600",children:ei>0?"Cantiere ".concat(localStorage.getItem("selectedCantiereName")||ei):"Nessun cantiere selezionato"})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(X.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(o.p,{placeholder:"Cerca per codice, responsabile, tipo, stato o descrizione...",value:_,onChange:e=>A(e.target.value),className:"pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,t.jsxs)(l.$,{onClick:()=>T(!0),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>{et({title:"Funzione in sviluppo",description:"L'assegnazione cavi sar\xe0 disponibile presto"})},disabled:0===eu.length,children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Assegna Cavi"]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>V(!0),children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Gestisci Responsabili"]})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Elenco Comande (",eu.length," di ",eh.totali,")"]})}),(0,t.jsx)(n.Zp,{className:"border border-gray-200 rounded-lg",children:(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{className:"bg-gray-50",children:[(0,t.jsx)(d.nd,{className:"font-semibold",children:"Codice"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Tipo"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Responsabile"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Contatti"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Stato"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Data Creazione"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Cavi"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Completamento"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Azioni"})]})}),(0,t.jsx)(d.BF,{children:v?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})})}):f?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)($.A,{className:"h-4 w-4"}),f]})})}):0===eu.length?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"})}):eu.map(e=>(0,t.jsxs)(d.Hj,{className:"hover:bg-gray-50",children:[(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.codice_comanda})}),(0,t.jsx)(d.nA,{children:em(e.tipo_comanda)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-medium",children:e.responsabile||"Non assegnato"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[e.responsabile_telefono&&(0,t.jsxs)("div",{children:["\uD83D\uDCDE ",e.responsabile_telefono]}),e.responsabile_email&&(0,t.jsxs)("div",{children:["✉️ ",e.responsabile_email]})]})}),(0,t.jsx)(d.nA,{children:ed(e.stato)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"text-sm",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.numero_cavi_assegnati||0})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"font-semibold",children:[(e.percentuale_completamento||0).toFixed(1),"%"]})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex gap-1 justify-center",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{Y(e.codice_comanda),F(!0)},title:"Visualizza",children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{et({title:"Funzione in sviluppo",description:"La modifica comande sar\xe0 disponibile presto"})},title:"Modifica",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})}),["POSA","COLLEGAMENTO_PARTENZA","COLLEGAMENTO_ARRIVO"].includes(e.tipo_comanda)&&(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{Y(e.codice_comanda),ee(e.tipo_comanda),"POSA"===e.tipo_comanda?U(!0):Z(!0)},title:"POSA"===e.tipo_comanda?"Inserisci Metri Posati":"Inserisci Metri Collegati",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ec(e.codice_comanda),disabled:v,className:"text-red-600 hover:text-red-700",title:"Elimina",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})})]},e.codice_comanda))})]})})})]}),(0,t.jsx)(E,{open:I,onClose:()=>T(!1),onSuccess:er,onError:eo,onComandaCreated:()=>el()}),(0,t.jsx)(M,{open:R,onClose:()=>V(!1),onSuccess:er,onError:eo}),(0,t.jsx)(B,{open:P,onClose:()=>{F(!1),Y(null)},codiceComanda:H,onSuccess:er,onError:eo}),(0,t.jsx)(J,{open:G,onClose:()=>{Z(!1),Y(null),ee(null)},codiceComanda:H||"",tipoComanda:K||"POSA",onSuccess:e=>{er(e),loadComande()},onError:eo}),(0,t.jsx)(W,{open:D,onClose:()=>{U(!1),Y(null),ee(null)},codiceComanda:H||"",onSuccess:e=>{er(e),loadComande()},onError:eo})]})}},47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>r});var t=s(95155);s(12115);var i=s(76981),n=s(5196),l=s(59434);function r(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>h,L3:()=>x,c7:()=>u,lG:()=>r,rr:()=>p,zM:()=>o});var t=s(95155);s(12115);var i=s(15452),n=s(54416),l=s(59434);function r(e){let{...a}=e;return(0,t.jsx)(i.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(i.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(i.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,showCloseButton:r=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[s,r&&(0,t.jsxs)(i.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function h(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function x(e){let{className:a,...s}=e;return(0,t.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...s})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...s})}},55365:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>o,TN:()=>c});var t=s(95155),i=s(12115),n=s(74466),l=s(59434);let r=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=i.forwardRef((e,a)=>{let{className:s,variant:i,...n}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,l.cn)(r({variant:i}),s),...n})});o.displayName="Alert",i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("h5",{ref:a,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",s),...i})}).displayName="AlertTitle";let c=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",s),...i})});c.displayName="AlertDescription"},55863:(e,a,s)=>{"use strict";s.d(a,{C1:()=>b,bL:()=>N});var t=s(12115),i=s(46081),n=s(63655),l=s(95155),r="Progress",[o,c]=(0,i.A)(r),[d,m]=o(r),u=t.forwardRef((e,a)=>{var s,t,i,r;let{__scopeProgress:o,value:c=null,max:m,getValueLabel:u=p,...h}=e;(m||0===m)&&!j(m)&&console.error((s="".concat(m),t="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=j(m)?m:100;null===c||f(c,x)||console.error((i="".concat(c),r="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(r,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let N=f(c,x)?c:null,b=g(N)?u(N,x):void 0;return(0,l.jsx)(d,{scope:o,value:N,max:x,children:(0,l.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":g(N)?N:void 0,"aria-valuetext":b,role:"progressbar","data-state":v(N,x),"data-value":null!=N?N:void 0,"data-max":x,...h,ref:a})})});u.displayName=r;var h="ProgressIndicator",x=t.forwardRef((e,a)=>{var s;let{__scopeProgress:t,...i}=e,r=m(h,t);return(0,l.jsx)(n.sG.div,{"data-state":v(r.value,r.max),"data-value":null!=(s=r.value)?s:void 0,"data-max":r.max,...i,ref:a})});function p(e,a){return"".concat(Math.round(e/a*100),"%")}function v(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function g(e){return"number"==typeof e}function j(e){return g(e)&&!isNaN(e)&&e>0}function f(e,a){return g(e)&&!isNaN(e)&&e<=a&&e>=0}x.displayName=h;var N=u,b=x},62525:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},79397:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},84616:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},87481:(e,a,s)=>{"use strict";s.d(a,{dj:()=>u});var t=s(12115);let i=0,n=new Map,l=e=>{if(n.has(e))return;let a=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,a)},r=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=a;return s?l(s):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=r(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,s=(i=(i+1)%Number.MAX_VALUE).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...a,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,a]=(0,t.useState)(c);return(0,t.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(95155),i=s(12115),n=s(59434);let l=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...i})});l.displayName="Textarea"},92657:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[455,464,346,587,807,112,283,642,441,684,358],()=>a(21815)),_N_E=e.O()}]);