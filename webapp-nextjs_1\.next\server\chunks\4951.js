exports.id=4951,exports.ids=[4951],exports.modules={1229:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var i=a(49384),s=a(82348);function r(...e){return(0,s.QP)((0,i.$)(e))}},13782:(e,t,a)=>{Promise.resolve().then(a.bind(a,54679)),Promise.resolve().then(a.bind(a,93319)),Promise.resolve().then(a.bind(a,79737)),Promise.resolve().then(a.bind(a,29131))},24176:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>U});var i=a(60687),s=a(43210),r=a(85814),n=a.n(r),o=a(16189),l=a(29523),c=a(63213),d=a(97895),p=a(4780);let m=d.bL;d.l9;let u=d.ZL,h=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(d.hJ,{className:(0,p.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:a}));h.displayName=d.hJ.displayName;let x=s.forwardRef(({className:e,...t},a)=>(0,i.jsxs)(u,{children:[(0,i.jsx)(h,{}),(0,i.jsx)(d.UC,{ref:a,className:(0,p.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));x.displayName=d.UC.displayName;let v=({className:e,...t})=>(0,i.jsx)("div",{className:(0,p.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});v.displayName="AlertDialogHeader";let f=({className:e,...t})=>(0,i.jsx)("div",{className:(0,p.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="AlertDialogFooter";let g=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(d.hE,{ref:a,className:(0,p.cn)("text-lg font-semibold",e),...t}));g.displayName=d.hE.displayName;let b=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(d.VY,{ref:a,className:(0,p.cn)("text-sm text-muted-foreground",e),...t}));b.displayName=d.VY.displayName;let j=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(d.rc,{ref:a,className:(0,p.cn)((0,l.r)(),e),...t}));j.displayName=d.rc.displayName;let C=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)(d.ZD,{ref:a,className:(0,p.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));C.displayName=d.ZD.displayName;var $=a(40083),N=a(41862);function w({isOpen:e,onClose:t,onConfirm:a}){let{logout:r,user:n,cantiere:o,isImpersonating:l,impersonatedUser:d}=(0,c.A)(),[p,u]=(0,s.useState)(!1),h=async()=>{u(!0);try{a&&a(),await new Promise(e=>setTimeout(e,500)),r()}catch(e){console.error("Errore durante logout:",e),u(!1)}};return(0,i.jsx)(m,{open:e,onOpenChange:t,children:(0,i.jsxs)(x,{children:[(0,i.jsxs)(v,{children:[(0,i.jsxs)(g,{className:"flex items-center space-x-2",children:[(0,i.jsx)($.A,{className:"w-5 h-5 text-red-600"}),(0,i.jsx)("span",{children:"Conferma logout"})]}),(0,i.jsx)(b,{className:"text-base",children:l&&d?`Vuoi tornare al menu amministratore? Stai attualmente impersonando l'utente "${d.username}".`:n?`Sei sicuro di voler uscire? Sarai disconnesso dall'account "${n.username}".`:o?`Sei sicuro di voler uscire? Sarai disconnesso dal cantiere "${o.commessa}".`:"Sei sicuro di voler uscire?"})]}),(0,i.jsxs)(f,{children:[(0,i.jsx)(C,{onClick:t,disabled:p,children:"Annulla"}),(0,i.jsx)(j,{onClick:h,disabled:p,className:"bg-red-600 hover:bg-red-700 focus:ring-red-500",children:p?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(N.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Disconnessione..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)($.A,{className:"w-4 h-4 mr-2"}),l?"Torna al menu admin":"Esci"]})})]})]})})}var A=a(32192),y=a(17313),S=a(23361),_=a(19080),z=a(10022),k=a(53411),P=a(6727),T=a(58559),R=a(58869),I=a(11860),E=a(12941),D=a(41312);let B=(e,t,a,i)=>{let s={name:"owner"===e?"Menu Admin":"user"===e?"Lista Cantieri":"cantieri_user"===e?"Gestione Cavi":"Home",href:"owner"===e?"/admin":"user"===e?"/cantieri":"cantieri_user"===e?"/cavi":"/",icon:A.A};if("owner"===e&&!t)return[s];if("user"===e||t&&a?.role==="user"){let e=[s];return t&&e.push({name:"Cantieri",href:"/cantieri",icon:y.A}),i&&e.push({name:"Visualizza Cavi",href:"/cavi",icon:S.A},{name:"Parco Cavi",href:"/parco-cavi",icon:_.A},{name:"Gestione Excel",href:"/excel",icon:z.A},{name:"Report",href:"/reports",icon:k.A},{name:"Gestione Comande",href:"/comande",icon:P.A},{name:"Produttivit\xe0",href:"/productivity",icon:T.A}),e}if("cantieri_user"===e||t&&a?.role==="cantieri_user"){let t=[s];return i&&("cantieri_user"!==e&&t.push({name:"Visualizza Cavi",href:"/cavi",icon:S.A}),t.push({name:"Parco Cavi",href:"/parco-cavi",icon:_.A},{name:"Gestione Excel",href:"/excel",icon:z.A},{name:"Report",href:"/reports",icon:k.A},{name:"Gestione Comande",href:"/comande",icon:P.A},{name:"Produttivit\xe0",href:"/productivity",icon:T.A})),t}return[s]};function U(){let[e,t]=(0,s.useState)(!1),[a,r]=(0,s.useState)(!1),d=(0,o.usePathname)(),{user:p,cantiere:m,isAuthenticated:u,isImpersonating:h,impersonatedUser:x,logout:v}=(0,c.A)(),f=m?.id_cantiere||0,g=m?.commessa||`Cantiere ${f}`,b=B(p?.ruolo,h,x,f);return"/login"!==d&&u?(0,i.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,i.jsx)("div",{className:"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between h-16",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 cursor-default",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center",children:(0,i.jsx)(S.A,{className:"w-5 h-5 text-white"})}),(0,i.jsxs)("div",{className:"hidden sm:block",children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-slate-900",children:"CABLYS"}),(0,i.jsx)("p",{className:"text-xs text-slate-500 -mt-1",children:"Cable Installation System"})]})]}),(0,i.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:b.map(e=>{let t=d===e.href||"/"!==e.href&&d.startsWith(e.href),a=e.icon;return(0,i.jsx)(n(),{href:e.href,children:(0,i.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${t?"text-blue-700 bg-blue-50 border border-blue-200 font-medium":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 border border-transparent"}`,children:[(0,i.jsx)(a,{className:"w-4 h-4"}),(0,i.jsx)("span",{className:"hidden lg:inline",children:e.name})]})},e.name)})})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4 ml-8",children:[f&&f>0&&(0,i.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md",children:[(0,i.jsx)(y.A,{className:"w-3 h-3 text-blue-600"}),(0,i.jsx)("div",{className:"text-xs",children:(0,i.jsx)("span",{className:"text-blue-900 font-medium",children:g})})]}),(0,i.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"text-right",children:(0,i.jsx)("p",{className:"text-sm font-medium text-slate-900",children:h&&x?x.username:p?.username})}),(0,i.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,i.jsx)(R.A,{className:"w-3 h-3 text-white"})}),(0,i.jsxs)(l.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:()=>r(!0),title:"Logout",children:[(0,i.jsx)($.A,{className:"w-4 h-4"}),(0,i.jsx)("span",{className:"hidden lg:inline",children:"Logout"})]})]}),(0,i.jsx)("div",{className:"md:hidden",children:(0,i.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md",children:e?(0,i.jsx)(I.A,{className:"w-5 h-5"}):(0,i.jsx)(E.A,{className:"w-5 h-5"})})})]})]})}),e&&(0,i.jsxs)("div",{className:"md:hidden border-t border-slate-200 bg-white",children:[(0,i.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:b.map(e=>{let a=d===e.href||"/"!==e.href&&d.startsWith(e.href),s=e.icon;return(0,i.jsx)(n(),{href:e.href,children:(0,i.jsxs)("div",{className:`w-full flex items-center justify-start space-x-3 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${a?"text-blue-700 bg-blue-50 border border-blue-200 font-medium":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 border border-transparent"}`,onClick:()=>t(!1),children:[(0,i.jsx)(s,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:e.name})]})},e.name)})}),(0,i.jsxs)("div",{className:"border-t border-slate-200 px-4 py-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:p?(0,i.jsx)(R.A,{className:"w-3 h-3 text-white"}):(0,i.jsx)(y.A,{className:"w-3 h-3 text-white"})}),(0,i.jsx)("div",{children:(0,i.jsx)("p",{className:"text-sm font-medium text-slate-900",children:h&&x?x.username:p?p.username:m?.commessa})})]}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>r(!0),title:"Logout",className:"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md",children:(0,i.jsx)($.A,{className:"w-4 h-4"})})]}),p?.ruolo==="owner"&&!h&&(0,i.jsxs)("div",{className:"mt-3 pt-3 border-t border-slate-200",children:[(0,i.jsx)("p",{className:"text-xs font-medium text-slate-500 mb-2",children:"AMMINISTRAZIONE"}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n(),{href:"/admin",className:"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150",onClick:()=>t(!1),children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(D.A,{className:"w-4 h-4 text-slate-500"}),(0,i.jsx)("span",{children:"Pannello Admin"})]})}),(0,i.jsx)(n(),{href:"/admin?tab=users",className:"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150",onClick:()=>t(!1),children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(D.A,{className:"w-4 h-4 text-slate-500"}),(0,i.jsx)("span",{children:"Gestione Utenti"})]})})]})]})]})]}),(0,i.jsx)(w,{isOpen:a,onClose:()=>r(!1)})]}):null}},25406:(e,t,a)=>{Promise.resolve().then(a.bind(a,69336)),Promise.resolve().then(a.bind(a,24176)),Promise.resolve().then(a.bind(a,91347)),Promise.resolve().then(a.bind(a,63213))},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>s});var i=a(12907);(0,i.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\contexts\\AuthContext.tsx","useAuth");let s=(0,i.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\contexts\\AuthContext.tsx","AuthProvider")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>o});var i=a(60687);a(43210);var s=a(8730),r=a(24224),n=a(4780);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:r=!1,...l}){let c=r?s.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:a,className:e})),...l})}},29867:(e,t,a)=>{"use strict";a.d(t,{dj:()=>m});var i=a(43210);let s=0,r=new Map,n=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=o(c,e),l.forEach(e=>{e(c)})}function p({...e}){let t=(s=(s+1)%Number.MAX_VALUE).toString(),a=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=(0,i.useState)(c);return{...e,toast:p,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},50346:()=>{},54679:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\components\\\\layout\\\\MainContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\components\\layout\\MainContent.tsx","default")},61135:()=>{},62185:(e,t,a)=>{"use strict";a.d(t,{AR:()=>u,At:()=>d,CV:()=>m,Fw:()=>p,ZQ:()=>c,_I:()=>j,dG:()=>C,km:()=>h,kw:()=>x,l9:()=>v,mg:()=>g,om:()=>f,ug:()=>b});var i=a(51060);let s=process.env.NEXT_PUBLIC_FRONTEND_URL||"http://localhost:3001",r=i.A.create({baseURL:s,timeout:3e4,headers:{"Content-Type":"application/json"}}),n=()=>null,o=()=>{};r.interceptors.request.use(e=>{let t=n();return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response?.status===401&&(console.log("\uD83D\uDEA8 API: Token non valido o scaduto, pulizia dati e reindirizzamento"),o()),Promise.reject(e)));let l={get:async(e,t)=>(await r.get(e,t)).data,post:async(e,t,a)=>(await r.post(e,t,a)).data,put:async(e,t,a)=>(await r.put(e,t,a)).data,delete:async(e,t)=>(await r.delete(e,t)).data},c={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await r.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>l.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>l.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},d={getCavi:(e,t)=>l.get(`/api/cavi/${e}`,{params:t}),getCavo:(e,t)=>l.get(`/api/cavi/${e}/${t}`),checkCavo:(e,t)=>l.get(`/api/cavi/${e}/check/${t}`),createCavo:(e,t)=>l.post(`/api/cavi/${e}`,t),updateCavo:(e,t,a)=>l.put(`/api/cavi/${e}/${t}`,a),deleteCavo:(e,t,a)=>l.delete(`/api/cavi/${e}/${t}`,{data:a}),updateMetriPosati:(e,t,a,i,s)=>l.post(`/api/cavi/${e}/${t}/metri-posati`,{metri_posati:a,id_bobina:i,force_over:s||!1}),updateBobina:(e,t,a,i)=>l.post(`/api/cavi/${e}/${t}/bobina`,{id_bobina:a,force_over:i||!1}),cancelInstallation:(e,t)=>l.post(`/api/cavi/${e}/${t}/cancel-installation`),collegaCavo:(e,t,a,i)=>l.post(`/api/cavi/${e}/${t}/collegamento`,{lato:a,responsabile:i}),scollegaCavo:(e,t,a)=>{let i={};return a&&(i.data={lato:a}),l.delete(`/api/cavi/${e}/${t}/collegamento`,i)},markAsSpare:(e,t,a,i=!0)=>a?l.post(`/api/cavi/${e}/${t}/mark-as-spare`,{force:i}):l.post(`/api/cavi/${e}/${t}/reactivate-spare`,{}),debugCavi:e=>l.get(`/api/cavi/debug/${e}`),debugCaviRaw:e=>l.get(`/api/cavi/debug/raw/${e}`)},p={getBobine:(e,t)=>l.get(`/api/parco-cavi/${e}`,{params:t}),getBobina:(e,t)=>l.get(`/api/parco-cavi/${e}/${t}`),getBobineCompatibili:(e,t)=>l.get(`/api/parco-cavi/${e}/compatibili`,{params:t}),createBobina:(e,t)=>l.post(`/api/parco-cavi/${e}`,t),updateBobina:(e,t,a)=>l.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>l.delete(`/api/parco-cavi/${e}/${t}`),isFirstBobinaInsertion:e=>l.get(`/api/parco-cavi/${e}/is-first-insertion`),updateBobina:(e,t,a)=>l.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>l.delete(`/api/parco-cavi/${e}/${t}`),checkDisponibilita:(e,t,a)=>l.get(`/api/parco-cavi/${e}/${t}/disponibilita`,{params:{metri_richiesti:a}})},m={getComande:e=>l.get(`/api/comande/cantiere/${e}`),getComanda:(e,t)=>l.get(`/api/comande/${t}`),getCaviComanda:e=>l.get(`/api/comande/${e}/cavi`),createComanda:(e,t)=>l.post(`/api/comande/cantiere/${e}`,t),createComandaWithCavi:(e,t,a)=>l.post(`/api/comande/cantiere/${e}/crea-con-cavi`,t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>l.put(`/api/comande/${e}/${t}`,a),updateComanda:(e,t,a)=>l.put(`/api/comande/cantiere/${e}/${t}`,a),deleteComanda:(e,t)=>l.delete(`/api/comande/cantiere/${e}/${t}`),assegnaCavi:(e,t,a)=>l.post(`/api/comande/cantiere/${e}/${t}/assegna-cavi`,{cavi_ids:a}),rimuoviCavi:(e,t,a)=>l.delete(`/api/comande/cantiere/${e}/${t}/rimuovi-cavi`,{data:{cavi_ids:a}}),getStatistiche:e=>l.get(`/api/comande/cantiere/${e}/statistiche`),cambiaStato:(e,t,a)=>l.put(`/api/comande/cantiere/${e}/${t}/stato`,{nuovo_stato:a})},u={getResponsabili:e=>l.get(`/api/responsabili/cantiere/${e}`),createResponsabile:(e,t)=>l.post(`/api/responsabili/${e}`,t),updateResponsabile:(e,t,a)=>l.put(`/api/responsabili/${e}/${t}`,a),deleteResponsabile:(e,t)=>l.delete(`/api/responsabili/${e}/${t}`)},h={getCertificazioni:(e,t)=>l.get(`/api/cantieri/${e}/certificazioni`,{params:t?{filtro_cavo:t}:{}}),createCertificazione:(e,t)=>l.post(`/api/cantieri/${e}/certificazioni`,t),getCertificazione:(e,t)=>l.get(`/api/cantieri/${e}/certificazioni/${t}`),updateCertificazione:(e,t,a)=>l.put(`/api/cantieri/${e}/certificazioni/${t}`,a),deleteCertificazione:(e,t)=>l.delete(`/api/cantieri/${e}/certificazioni/${t}`),generatePDF:(e,t)=>l.get(`/api/cantieri/${e}/certificazioni/${t}/pdf`,{responseType:"blob"}),getStatistiche:e=>l.get(`/api/cantieri/${e}/certificazioni/statistiche`),exportCertificazioni:(e,t)=>l.get(`/api/cantieri/${e}/certificazioni/export`,{params:t,responseType:"blob"}),generateReport:(e,t="completo")=>l.get(`/api/cantieri/${e}/certificazioni/report/${t}`),bulkDelete:(e,t)=>l.post(`/api/cantieri/${e}/certificazioni/bulk-delete`,{ids:t}),generateBulkPdf:(e,t)=>l.post(`/api/cantieri/${e}/certificazioni/bulk-pdf`,{ids:t},{responseType:"blob"}),validateCertificazione:(e,t)=>l.post(`/api/cantieri/${e}/certificazioni/validate`,t)},x={getStrumenti:e=>l.get(`/api/cantieri/${e}/strumenti`),createStrumento:(e,t)=>l.post(`/api/cantieri/${e}/strumenti`,t),updateStrumento:(e,t,a)=>l.put(`/api/cantieri/${e}/strumenti/${t}`,a),deleteStrumento:(e,t)=>l.delete(`/api/cantieri/${e}/strumenti/${t}`)},v={getRapporti:(e,t=0,a=100)=>l.get(`/api/cantieri/${e}/rapporti`,{params:{skip:t,limit:a}}),createRapporto:(e,t)=>l.post(`/api/cantieri/${e}/rapporti`,t),getRapporto:(e,t)=>l.get(`/api/cantieri/${e}/rapporti/${t}`),updateRapporto:(e,t,a)=>l.put(`/api/cantieri/${e}/rapporti/${t}`,a),deleteRapporto:(e,t)=>l.delete(`/api/cantieri/${e}/rapporti/${t}`),aggiornaStatistiche:(e,t)=>l.post(`/api/cantieri/${e}/rapporti/${t}/aggiorna-statistiche`)},f={getNonConformita:e=>l.get(`/api/cantieri/${e}/non-conformita`),createNonConformita:(e,t)=>l.post(`/api/cantieri/${e}/non-conformita`,t),updateNonConformita:(e,t,a)=>l.put(`/api/cantieri/${e}/non-conformita/${t}`,a),deleteNonConformita:(e,t)=>l.delete(`/api/cantieri/${e}/non-conformita/${t}`)},g={importCavi:(e,t,a)=>{let i=new FormData;return i.append("file",t),i.append("revisione",a),l.post(`/api/excel/${e}/import-cavi`,i,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),l.post(`/api/excel/${e}/import-parco-bobine`,a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>l.get(`/api/excel/${e}/export-cavi`,{responseType:"blob"}),exportBobine:e=>l.get(`/api/excel/${e}/export-parco-bobine`,{responseType:"blob"})},b={getReportAvanzamento:e=>l.get(`/api/reports/${e}/avanzamento`),getReportBOQ:e=>l.get(`/api/reports/${e}/boq`),getReportUtilizzoBobine:e=>l.get(`/api/reports/${e}/storico-bobine`),getReportProgress:e=>l.get(`/api/reports/${e}/progress`),getReportPosaPeriodo:(e,t,a)=>{let i=new URLSearchParams;t&&i.append("data_inizio",t),a&&i.append("data_fine",a);let s=i.toString();return l.get(`/api/reports/${e}/posa-periodo${s?`?${s}`:""}`)}},j={getCantieri:()=>l.get("/api/cantieri"),getCantiere:e=>l.get(`/api/cantieri/${e}`),createCantiere:e=>l.post("/api/cantieri",e),updateCantiere:(e,t)=>l.put(`/api/cantieri/${e}`,t),getCantiereStatistics:e=>l.get(`/api/cantieri/${e}/statistics`),getWeatherData:e=>l.get(`/api/cantieri/${e}/weather`)},C={getUsers:()=>l.get("/api/users"),getUser:e=>l.get(`/api/users/${e}`),createUser:e=>l.post("/api/users",e),updateUser:(e,t)=>l.put(`/api/users/${e}`,t),deleteUser:e=>l.delete(`/api/users/${e}`),toggleUserStatus:e=>l.get(`/api/users/toggle/${e}`),checkExpiredUsers:()=>l.get("/api/users/check-expired"),impersonateUser:e=>l.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>l.get("/api/users/db-raw"),resetDatabase:()=>l.post("/api/admin/reset-database")}},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>l});var i=a(60687),s=a(43210),r=a(62185);let n=(0,s.createContext)(void 0);function o(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,a]=(0,s.useState)(null),[o,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),[p,m]=(0,s.useState)(()=>!1),[u,h]=(0,s.useState)(()=>null),[x,v]=(0,s.useState)(null),[f,g]=(0,s.useState)(null),[b,j]=(0,s.useState)(null),C=!!t&&t.id_utente||!!o&&o.id_cantiere,$=async()=>{try{return void d(!1)}catch(e){console.error("\uD83D\uDD10 AuthContext: Errore generale durante checkAuth:",e),a(null),l(null),m(!1),h(null)}finally{console.log("\uD83D\uDD10 AuthContext: checkAuth completato, impostazione loading = false"),d(!1)}},N=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login per:",e),d(!0);let a=await r.ZQ.login({username:e,password:t});console.log("\uD83D\uDCE1 AuthContext: Risposta backend ricevuta:",a),console.log("\uD83D\uDD04 AuthContext: Fallback SSR");let i={id_utente:a.user_id,username:a.username,ruolo:a.role};return{success:!0,user:i}}catch(e){return console.error("❌ AuthContext: Errore durante login:",e),{success:!1,error:e.response?.data?.detail||e.message||"Errore durante il login"}}finally{d(!1)}},w=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login cantiere:",e),d(!0);let a=await r.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});console.log("\uD83D\uDD10 AuthContext: Risposta login cantiere:",a);let i={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};return{success:!0,cantiere:i}}catch(e){return console.error("❌ AuthContext: Errore durante login cantiere:",e),{success:!1,error:e.response?.data?.detail||e.message||"Errore durante il login cantiere"}}finally{d(!1)}},A=async e=>{try{await r.dG.impersonateUser(e)}catch(e){throw e}};return(0,i.jsx)(n.Provider,{value:{user:t,cantiere:o,isAuthenticated:C,isLoading:c,isImpersonating:p,impersonatedUser:u,expirationWarning:x,daysUntilExpiration:f,expirationDate:b,login:N,loginCantiere:w,logout:()=>{},checkAuth:$,impersonateUser:A,selectCantiere:e=>{if(!e||!e.id_cantiere||e.id_cantiere<=0)return void console.error("\uD83C\uDFD7️ AuthContext: Tentativo di selezione cantiere non valido:",e);try{let t=e.commessa||`Cantiere ${e.id_cantiere}`;localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t);let a={...e,commessa:t};console.log("\uD83C\uDFD7️ AuthContext: Cantiere selezionato:",a),l(a),localStorage.removeItem("cantiere_data")}catch(e){console.error("\uD83C\uDFD7️ AuthContext: Errore nella selezione cantiere:",e)}},clearCantiere:()=>{console.log("\uD83C\uDFD7️ AuthContext: Pulizia stato cantiere"),l(null),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data")},dismissExpirationWarning:()=>{v(null),g(null),j(null)}},children:e})}},69336:(e,t,a)=>{"use strict";a.d(t,{default:()=>h});var i=a(60687),s=a(43210),r=a(63213);let n=()=>{let{isAuthenticated:e,user:t,cantiere:a,checkAuth:i,logout:n}=(0,r.A)(),o=(0,s.useRef)(null),l=(0,s.useRef)(Date.now()),c=()=>{o.current&&(clearInterval(o.current),o.current=null)};return(0,s.useEffect)(()=>{},[e,t,a]),(0,s.useEffect)(()=>()=>{c()},[]),{updateActivity:()=>{l.current=Date.now()},checkSessionExpiry:()=>!0}};var o=a(91821),l=a(29523),c=a(43649),d=a(48730),p=a(40228),m=a(11860);function u(){let{expirationWarning:e,daysUntilExpiration:t,expirationDate:a,dismissExpirationWarning:s}=(0,r.A)();return e?(0,i.jsx)(o.Fc,{variant:0===t||1===t?"destructive":"default",className:"mb-4 border-l-4",children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[0===t?(0,i.jsx)(c.A,{className:"h-4 w-4"}):1===t?(0,i.jsx)(d.A,{className:"h-4 w-4"}):(0,i.jsx)(p.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)(o.TN,{className:"font-medium",children:e}),a&&(0,i.jsxs)(o.TN,{className:"text-sm mt-1 opacity-90",children:["Data di scadenza: ",new Date(a).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})]}),(0,i.jsx)(o.TN,{className:"text-sm mt-2 opacity-80",children:"Contatta l'amministratore per rinnovare il tuo account."})]})]}),(0,i.jsx)(l.$,{variant:"ghost",size:"sm",onClick:s,className:"h-6 w-6 p-0 hover:bg-transparent",children:(0,i.jsx)(m.A,{className:"h-4 w-4"})})]})}):null}function h({children:e}){let{isAuthenticated:t}=(0,r.A)();return n(),(0,i.jsxs)("main",{className:"pt-16",children:[t&&(0,i.jsx)("div",{className:"container mx-auto px-4 py-2",children:(0,i.jsx)(u,{})}),e]})}},79737:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\components\\ui\\toaster.tsx","Toaster")},91347:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>c});var i=a(60687),s=a(29867),r=a(29523),n=a(93613),o=a(5336),l=a(11860);function c(){let{toasts:e,dismiss:t}=(0,s.dj)();return(0,i.jsx)("div",{className:"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",children:e.map(e=>(0,i.jsxs)("div",{className:"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",style:{backgroundColor:"destructive"===e.variant?"#fef2f2":"#f0f9ff",borderColor:"destructive"===e.variant?"#fecaca":"#bae6fd"},children:[(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:["destructive"===e.variant?(0,i.jsx)(n.A,{className:"h-4 w-4 text-red-600 mt-0.5"}):(0,i.jsx)(o.A,{className:"h-4 w-4 text-green-600 mt-0.5"}),(0,i.jsxs)("div",{className:"grid gap-1",children:[e.title&&(0,i.jsx)("div",{className:`text-sm font-semibold ${"destructive"===e.variant?"text-red-900":"text-gray-900"}`,children:e.title}),e.description&&(0,i.jsx)("div",{className:`text-sm ${"destructive"===e.variant?"text-red-700":"text-gray-700"}`,children:e.description})]})]}),(0,i.jsx)(r.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent",onClick:()=>t(e.id),children:(0,i.jsx)(l.A,{className:"h-3 w-3"})})]},e.id))})}},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>l,TN:()=>c});var i=a(60687),s=a(43210),r=a(24224),n=a(4780);let o=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef(({className:e,variant:t,...a},s)=>(0,i.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(o({variant:t}),e),...a}));l.displayName="Alert",s.forwardRef(({className:e,...t},a)=>(0,i.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let c=s.forwardRef(({className:e,...t},a)=>(0,i.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));c.displayName="AlertDescription"},93319:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\components\\layout\\Navbar.tsx","Navbar")},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u,metadata:()=>m});var i=a(37413),s=a(35759),r=a.n(s),n=a(29404),o=a.n(n);a(61135),a(50346);var l=a(93319),c=a(54679),d=a(29131),p=a(79737);let m={title:"CABLYS - Cable Installation Advance System",description:"Sistema avanzato per la gestione dell'installazione cavi",manifest:"/manifest.json",themeColor:"#2563eb",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",appleWebApp:{capable:!0,statusBarStyle:"default",title:"CABLYS"}};function u({children:e}){return(0,i.jsx)("html",{lang:"it",children:(0,i.jsx)("body",{className:`${r().variable} ${o().variable} antialiased`,children:(0,i.jsxs)(d.AuthProvider,{children:[(0,i.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,i.jsx)(l.Navbar,{}),(0,i.jsx)(c.default,{children:e})]}),(0,i.jsx)(p.Toaster,{})]})})})}},99021:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))}};