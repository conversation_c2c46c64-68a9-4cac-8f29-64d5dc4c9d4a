(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{3493:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},23227:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var i=r(95155);r(12115);var a=r(99708),s=r(74466),n=r(59434);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:s=!1,...l}=e,d=s?a.DX:"span";return(0,i.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:r}),t),...l})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var i=r(95155);r(12115);var a=r(99708),s=r(74466),n=r(59434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...d}=e,c=l?a.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:s,className:t})),...d})}},35695:(e,t,r)=>{"use strict";var i=r(18999);r.o(i,"useParams")&&r.d(t,{useParams:function(){return i.useParams}}),r.o(i,"usePathname")&&r.d(t,{usePathname:function(){return i.usePathname}}),r.o(i,"useRouter")&&r.d(t,{useRouter:function(){return i.useRouter}}),r.o(i,"useSearchParams")&&r.d(t,{useSearchParams:function(){return i.useSearchParams}})},40286:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(95155),a=r(12115),s=r(35695),n=r(66695),o=r(30285),l=r(62523),d=r(85057),c=r(26126),u=r(40283),p=r(3493),m=r(71007),v=r(23227),g=r(85339),h=r(51154),f=r(61610);let x=()=>{let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)({totalEvents:0,loginAttempts:0,blockedRequests:0,suspiciousActivity:0,lastEventTime:null}),s=function(e,r){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"medium",s={type:e,timestamp:Date.now(),details:r,severity:a};t(e=>[...e,s].slice(-100)),i(t=>({totalEvents:t.totalEvents+1,loginAttempts:t.loginAttempts+ +("login_attempt"===e),blockedRequests:t.blockedRequests+ +("high"===a||"critical"===a),suspiciousActivity:t.suspiciousActivity+ +("suspicious_activity"===e),lastEventTime:s.timestamp})),console.log({type:e,details:r,timestamp:new Date(s.timestamp).toISOString()})};(0,a.useEffect)(()=>{let e=()=>{(window.outerHeight-window.innerHeight>160||window.outerWidth-window.innerWidth>160)&&s("suspicious_activity",{action:"devtools_detected",windowSize:{outer:[window.outerWidth,window.outerHeight],inner:[window.innerWidth,window.innerHeight]}},"low")},t=e=>{var t;let r=(null==(t=e.clipboardData)?void 0:t.getData("text"))||"";[/script/gi,/javascript:/gi,/vbscript:/gi,/onload|onerror|onclick/gi,/<iframe|<object|<embed/gi,/union.*select/gi,/drop.*table/gi].some(e=>e.test(r))&&(e.preventDefault(),s("suspicious_activity",{action:"malicious_paste_blocked",content:r.substring(0,100)},"high"))},r=Storage.prototype.setItem;Storage.prototype.setItem=function(e,t){return/<script|javascript:|vbscript:/gi.test(t)?void s("suspicious_activity",{action:"malicious_storage_attempt",key:e,value:t.substring(0,50)},"high"):r.call(this,e,t)};let i=e=>{let t=e.message.toLowerCase();["script error","permission denied","access denied","blocked by cors","network error"].some(e=>t.includes(e))&&s("suspicious_activity",{action:"suspicious_js_error",message:e.message,filename:e.filename,lineno:e.lineno},"medium")},a=e=>{5e3>performance.now()&&s("suspicious_activity",{action:"rapid_page_exit",timeOnPage:performance.now()},"low")};window.addEventListener("resize",e),window.addEventListener("paste",t),window.addEventListener("error",i),window.addEventListener("beforeunload",a);let n=setInterval(e,5e3);return()=>{window.removeEventListener("resize",e),window.removeEventListener("paste",t),window.removeEventListener("error",i),window.removeEventListener("beforeunload",a),clearInterval(n),Storage.prototype.setItem=r}},[]);let n=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,r=Date.now()-60*t*1e3;return e.filter(e=>e.timestamp>r)};return{events:e,metrics:r,getRecentEvents:n,getEventsByType:t=>e.filter(e=>e.type===t),getEventsBySeverity:t=>e.filter(e=>e.severity===t),isUnderAttack:()=>n(5).filter(e=>"high"===e.severity||"critical"===e.severity).length>3,getThreatLevel:()=>{let e=n(10),t=e.filter(e=>"critical"===e.severity).length,r=e.filter(e=>"high"===e.severity).length;return t>0?"critical":r>2?"high":e.length>10?"medium":"low"},logSecurityEvent:s,logLoginAttempt:(e,t,r)=>{s("login_attempt",{username:e,success:t,userAgent:navigator.userAgent,timestamp:Date.now(),...r},t?"low":"medium")},logFormSubmission:(e,t,r)=>{s("form_submission",{formType:e,success:t,userAgent:navigator.userAgent,...r},"low")},logSuspiciousActivity:(e,t)=>{s("suspicious_activity",{activity:e,userAgent:navigator.userAgent,url:window.location.href,...t},"high")},logRateLimitHit:(e,t)=>{s("rate_limit_hit",{endpoint:e,userAgent:navigator.userAgent,...t},"medium")}}};function b(){let[e,t]=(0,a.useState)("user"),[r,b]=(0,a.useState)({username:"",password:"",codice_cantiere:"",password_cantiere:""}),[w,y]=(0,a.useState)(""),[j,_]=(0,a.useState)(!1),[A,k]=(0,a.useState)({}),{login:N,loginCantiere:V}=(0,u.A)(),z=(0,s.useRouter)(),{logLoginAttempt:E,logSuspiciousActivity:P,getThreatLevel:S}=x(),C=()=>{let t={};if("user"===e){let e=(0,f.TU)(r.username);e.isValid||(t.username=e.error),r.password?r.password.length<3&&(t.password="Password troppo corta"):t.password="Password \xe8 obbligatoria"}else r.codice_cantiere.trim()?r.codice_cantiere.length<3&&(t.codice_cantiere="Codice cantiere troppo corto"):t.codice_cantiere="Codice cantiere \xe8 obbligatorio",r.password_cantiere||(t.password_cantiere="Password cantiere \xe8 obbligatoria");return k(t),0===Object.keys(t).length},R=async t=>{t.preventDefault(),y(""),k({});let i="user"===e?r.username:r.codice_cantiere;if(!(0,f.Eb)("login-".concat(i),5,3e5)){y("Troppi tentativi di login. Riprova tra 5 minuti."),P("rate_limit_exceeded",{loginType:e,identifier:i});return}if(!C())return;let a=S();if("critical"===a){y("Sistema temporaneamente non disponibile per motivi di sicurezza."),P("login_blocked_threat_level",{threatLevel:a});return}_(!0);try{if("user"===e){let e=await N(r.username,r.password);E(r.username,!0,{ruolo:null==e?void 0:e.ruolo}),(null==e?void 0:e.ruolo)==="owner"?z.push("/admin"):(null==e?void 0:e.ruolo)==="user"?z.push("/cantieri"):(null==e?void 0:e.ruolo)==="cantieri_user"?z.push("/cavi"):z.push("/cantieri")}else await V(r.codice_cantiere,r.password_cantiere),E(r.codice_cantiere,!0,{type:"cantiere"}),z.push("/cavi")}catch(t){var s,n,o,l;E("user"===e?r.username:r.codice_cantiere,!1,{error:(null==(n=t.response)||null==(s=n.data)?void 0:s.detail)||t.message,loginType:e}),y((null==(l=t.response)||null==(o=l.data)?void 0:o.detail)||"Credenziali non valide")}finally{_(!1)}},T=(e,t)=>{b(r=>({...r,[e]:t})),y("")};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,i.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,i.jsx)(p.A,{className:"w-8 h-8 text-white"})})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,i.jsx)("p",{className:"text-slate-600",children:"Cable Installation Advance System"})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(o.$,{variant:"user"===e?"default":"outline",className:"flex-1",onClick:()=>t("user"),children:[(0,i.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Utente"]}),(0,i.jsxs)(o.$,{variant:"cantiere"===e?"default":"outline",className:"flex-1",onClick:()=>t("cantiere"),children:[(0,i.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Cantiere"]})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)(n.ZB,{className:"flex items-center gap-2",children:"user"===e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(m.A,{className:"w-5 h-5 text-blue-600"}),"Login Utente"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(v.A,{className:"w-5 h-5 text-green-600"}),"Login Cantiere"]})}),(0,i.jsx)(n.BT,{children:"user"===e?"Accedi con le tue credenziali utente":"Accedi con il codice cantiere"})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)("form",{onSubmit:R,className:"space-y-4",children:["user"===e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"username",children:"Username"}),(0,i.jsx)(l.p,{id:"username",type:"text",placeholder:"Inserisci username",value:r.username,onChange:e=>T("username",e.target.value),required:!0,disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,i.jsx)(l.p,{id:"password",type:"password",placeholder:"Inserisci password",value:r.password,onChange:e=>T("password",e.target.value),required:!0,disabled:j})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"codice_cantiere",children:"Codice Cantiere"}),(0,i.jsx)(l.p,{id:"codice_cantiere",type:"text",placeholder:"Inserisci codice cantiere",value:r.codice_cantiere,onChange:e=>T("codice_cantiere",e.target.value),required:!0,disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"password_cantiere",children:"Password Cantiere"}),(0,i.jsx)(l.p,{id:"password_cantiere",type:"password",placeholder:"Inserisci password cantiere",value:r.password_cantiere,onChange:e=>T("password_cantiere",e.target.value),required:!0,disabled:j})]})]}),w&&(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,i.jsx)(g.A,{className:"w-4 h-4 text-red-500"}),(0,i.jsx)("span",{className:"text-sm text-red-700",children:w})]}),(0,i.jsx)(o.$,{type:"submit",className:"w-full",disabled:j,children:j?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(h.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Accesso in corso..."]}):"Accedi"}),"user"===e&&(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)(o.$,{type:"button",variant:"link",className:"text-sm text-blue-600 hover:text-blue-800",onClick:()=>z.push("/forgot-password"),children:"Password dimenticata?"})})]})})]}),(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,i.jsx)(c.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:"Next.js 15"}),(0,i.jsx)(c.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"PWA Ready"})]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"Sistema di gestione cavi di nuova generazione"})]})]})})}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var i=r(12115),a=r(63655),s=r(95155),n=i.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var i=r(52596),a=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,i.$)(t))}},61610:(e,t,r)=>{"use strict";r.d(t,{Eb:()=>v,GN:()=>g,TU:()=>o});let i=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,a=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,s=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,n=e=>"string"!=typeof e?"":e.trim().replace(i,"").replace(/\s+/g," ").substring(0,1e3),o=e=>{let t=n(e);return t.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:t.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(t)?/^[._-]|[._-]$/.test(t)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},l=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let t=0;return(/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[0-9]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,e.length>=12&&t++,t<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:t}:["password","123456","admin","qwerty","letmein"].some(t=>e.toLowerCase().includes(t))?{isValid:!1,error:"Password troppo comune",strength:t}:{isValid:!0,strength:t}},d=e=>{let t=n(e);return t?t.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return n(e).length>t?{isValid:!1,error:"Testo troppo lungo (max ".concat(t," caratteri)")}:s.test(e)||a.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},u=e=>{let t=n(e);return t?t.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:t.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(t)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},p=e=>{if(!e)return{isValid:!0};let t=n(e).replace(/\s/g,"");return t.length<8||t.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(t)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},m=new Map,v=(e,t,r)=>{let i=Date.now(),a=m.get(e);return!a||i>a.resetTime?(m.set(e,{count:1,resetTime:i+r}),!0):!(a.count>=t)&&(a.count++,!0)},g=e=>{let t={},r=o(e.username);if(r.isValid||(t.username=r.error),e.password){let r=l(e.password);r.isValid||(t.password=r.error)}let i=u(e.ragione_sociale);if(i.isValid||(t.ragione_sociale=i.error),e.email){let r=d(e.email);r.isValid||(t.email=r.error)}if(e.vat){let r=p(e.vat);r.isValid||(t.vat=r.error)}if(e.indirizzo){let r=c(e.indirizzo,200);r.isValid||(t.indirizzo=r.error)}if(e.nazione){let r=c(e.nazione,50);r.isValid||(t.nazione=r.error)}if(e.referente_aziendale){let r=c(e.referente_aziendale,100);r.isValid||(t.referente_aziendale=r.error)}return{isValid:0===Object.keys(t).length,errors:t}}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var i=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,i.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var i=r(12115),a=r(47650),s=r(99708),n=r(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),a=i.forwardRef((e,i)=>{let{asChild:a,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...s,ref:i})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>n});var i=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var i=r(95155);r(12115);var a=r(40968),s=r(59434);function n(e){let{className:t,...r}=e;return(0,i.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},95147:(e,t,r)=>{Promise.resolve().then(r.bind(r,40286))}},e=>{var t=t=>e(e.s=t);e.O(0,[455,464,283,441,684,358],()=>t(95147)),_N_E=e.O()}]);