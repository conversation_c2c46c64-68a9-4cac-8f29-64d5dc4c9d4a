(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[562],{12981:(e,a,t)=>{Promise.resolve().then(t.bind(t,58001))},13587:(e,a,t)=>{"use strict";t.d(a,{u:()=>x});var i=t(95155);t(12115);var s=t(55365),r=t(30285),n=t(66695),l=t(53904),o=t(85339),c=t(47957),d=t(35169),m=t(35695),u=t(17522);function x(e){let{children:a,fallback:t,showBackButton:x=!0,backUrl:b="/cantieri"}=e,g=(0,m.useRouter)(),{cantiereId:p,cantiere:h,isValidCantiere:v,isLoading:f,error:j,clearError:N}=(0,u.jV)();if(f)return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsx)(n.Zp,{children:(0,i.jsx)(n.Wu,{className:"flex items-center justify-center p-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(l.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,i.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"Caricamento cantiere..."}),(0,i.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Verifica della selezione cantiere in corso"})]})})})})});if(j||!v){let e=j||"Nessun cantiere selezionato";return t?(0,i.jsx)(i.Fragment,{children:t}):(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,i.jsxs)(n.Zp,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center text-red-800",children:[(0,i.jsx)(o.A,{className:"h-6 w-6 mr-2"}),"Problema con la selezione del cantiere"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)(s.Fc,{variant:"destructive",children:[(0,i.jsx)(o.A,{className:"h-4 w-4"}),(0,i.jsxs)(s.TN,{children:[(0,i.jsx)("strong",{children:"Errore:"})," ",e]})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center text-gray-700",children:[(0,i.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Informazioni cantiere"]})}),(0,i.jsx)(n.Wu,{className:"space-y-3",children:(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"ID Cantiere:"}),(0,i.jsx)("span",{className:"ml-2 text-gray-800",children:p||"Non disponibile"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Nome Cantiere:"}),(0,i.jsx)("span",{className:"ml-2 text-gray-800",children:(null==h?void 0:h.commessa)||"Non disponibile"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Cantiere Valido:"}),(0,i.jsx)("span",{className:"ml-2 ".concat(v?"text-green-600":"text-red-600"),children:v?"S\xec":"No"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"localStorage ID:"}),(0,i.jsx)("span",{className:"ml-2 text-gray-800",children:localStorage.getItem("selectedCantiereId")||"Non presente"})]})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{className:"text-gray-700",children:"Azioni disponibili"})}),(0,i.jsxs)(n.Wu,{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex flex-wrap gap-3",children:[x&&(0,i.jsxs)(r.$,{onClick:()=>g.push(b),variant:"default",className:"flex items-center",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Seleziona Cantiere"]}),j&&(0,i.jsxs)(r.$,{onClick:N,variant:"outline",className:"flex items-center",children:[(0,i.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Riprova"]}),(0,i.jsxs)(r.$,{onClick:()=>{localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data"),window.location.reload()},variant:"outline",className:"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50",children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Reset Dati Cantiere"]})]}),(0,i.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,i.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,i.jsx)("strong",{children:"Suggerimento:"})," Se il problema persiste, prova a selezionare nuovamente un cantiere dalla pagina principale o contatta l'amministratore del sistema."]})})]})]})]})})}return(0,i.jsx)(i.Fragment,{children:a})}},17313:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>l});var i=t(95155),s=t(12115),r=t(60704),n=t(59434);let l=r.bL,o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(r.B8,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});o.displayName=r.B8.displayName;let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(r.l9,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});c.displayName=r.l9.displayName;let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(r.UC,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});d.displayName=r.UC.displayName},17522:(e,a,t)=>{"use strict";t.d(a,{jV:()=>r});var i=t(12115),s=t(40283);function r(){let{cantiere:e,isLoading:a}=(0,s.A)(),[t,r]=(0,i.useState)(null),[n,l]=(0,i.useState)(!0),[o,c]=(0,i.useState)(null),d=e=>{if(null==e)return!1;let a="string"==typeof e?parseInt(e,10):e;return!isNaN(a)&&!(a<=0)||(console.warn("\uD83C\uDFD7️ useCantiere: ID cantiere non valido:",e),!1)};return(0,i.useEffect)(()=>{if(a)return void console.log("\uD83C\uDFD7️ useCantiere: Autenticazione in corso...");l(!0),c(null);try{let a=null;if((null==e?void 0:e.id_cantiere)&&d(e.id_cantiere))a=e.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere dal context (login cantiere):",a);else{let e=localStorage.getItem("cantiere_data");if(e)try{let t=JSON.parse(e);t.id_cantiere&&d(t.id_cantiere)&&(a=t.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da cantiere_data:",a))}catch(e){console.warn("\uD83C\uDFD7️ useCantiere: Errore parsing cantiere_data:",e)}if(!a){let e=localStorage.getItem("selectedCantiereId");e&&d(e)&&(a=parseInt(e,10),console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da selectedCantiereId:",a))}}a?(r(a),console.log("\uD83C\uDFD7️ useCantiere: Cantiere valido impostato:",a)):(console.warn("\uD83C\uDFD7️ useCantiere: Nessun cantiere valido trovato"),r(null),c("Nessun cantiere selezionato. Seleziona un cantiere per continuare."))}catch(e){console.error("\uD83C\uDFD7️ useCantiere: Errore nella gestione cantiere:",e),c("Errore nella gestione del cantiere selezionato."),r(null)}finally{l(!1)}},[e,a]),{cantiereId:t,cantiere:e,isValidCantiere:null!==t&&t>0,isLoading:n,error:o,validateCantiere:d,clearError:()=>c(null)}}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,r:()=>l});var i=t(95155);t(12115);var s=t(99708),r=t(74466),n=t(59434);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:t,size:r,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:r,className:a})),...c})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>x,L3:()=>b,c7:()=>u,lG:()=>l,rr:()=>g,zM:()=>o});var i=t(95155);t(12115);var s=t(15452),r=t(54416),n=t(59434);function l(e){let{...a}=e;return(0,i.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function m(e){let{className:a,children:t,showCloseButton:l=!0,...o}=e;return(0,i.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,i.jsx)(d,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[t,l&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(r.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function x(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function b(e){let{className:a,...t}=e;return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",a),...t})}function g(e){let{className:a,...t}=e;return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",a),...t})}},55365:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>o,TN:()=>c});var i=t(95155),s=t(12115),r=t(74466),n=t(59434);let l=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,a)=>{let{className:t,variant:s,...r}=e;return(0,i.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:s}),t),...r})});o.displayName="Alert",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})}).displayName="AlertTitle";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...s})});c.displayName="AlertDescription"},58001:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>H});var i=t(95155),s=t(12115),r=t(30285),n=t(62523),l=t(85127),o=t(55365),c=t(40283),d=t(17522),m=t(13587),u=t(25731);let x={DISPONIBILE:"Disponibile",IN_USO:"In uso",TERMINATA:"Terminata",OVER:"Over"},b=(e,a)=>e<0?x.OVER:0===e?x.TERMINATA:e<a?x.IN_USO:x.DISPONIBILE,g=e=>e===x.DISPONIBILE||e===x.IN_USO,p=e=>{switch(e){case x.DISPONIBILE:return"hover:bg-green-50";case x.IN_USO:return"hover:bg-yellow-50";case x.TERMINATA:return"hover:bg-red-50";case x.OVER:return"hover:bg-red-100";default:return"hover:bg-gray-50"}},h=e=>e!==x.OVER&&e!==x.TERMINATA,v=(e,a)=>a<=0?0:Math.max(0,Math.min(100,(a-e)/a*100)),f=e=>"".concat(e.toFixed(1),"m"),j=e=>{switch(e){case x.DISPONIBILE:return"Bobina disponibile per nuove installazioni";case x.IN_USO:return"Bobina parzialmente utilizzata";case x.TERMINATA:return"Bobina completamente esaurita";case x.OVER:return"Bobina sovra-utilizzata (metri negativi)";default:return"Stato non definito"}};var N=t(54165),y=t(85057),_=t(84616),w=t(51154),z=t(85339);let C={numero_bobina:"",utility:"",tipologia:"",n_conduttori:"0",sezione:"",metri_totali:"",ubicazione_bobina:"TBD",fornitore:"TBD",n_DDT:"TBD",data_DDT:"",configurazione:"s"};function S(e){let{open:a,onClose:t,cantiereId:l,onSuccess:c,onError:d}=e,[m,x]=(0,s.useState)(C),[b,g]=(0,s.useState)(!1),[p,h]=(0,s.useState)(""),[v,f]=(0,s.useState)("1"),[j,S]=(0,s.useState)(!1),[D,E]=(0,s.useState)(!0),[A,T]=(0,s.useState)(""),[I,k]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&l&&l>0&&(x(C),h(""),F())},[a,l]);let F=async()=>{if(l&&!(l<=0))try{S(!0);let e=await u.Fw.isFirstBobinaInsertion(l);E(e.is_first_insertion),e.is_first_insertion?(k(!0),T("")):(T(e.configurazione),x(a=>({...a,configurazione:e.configurazione})),k(!1),"s"===e.configurazione&&await O())}catch(e){E(!0),k(!0)}finally{S(!1)}},O=async()=>{if(l&&!(l<=0))try{let e=await u.Fw.getBobine(l);if(e&&e.length>0){let a=e.filter(e=>e.numero_bobina&&/^\d+$/.test(e.numero_bobina));if(a.length>0){let e=Math.max(...a.map(e=>parseInt(e.numero_bobina,10))),t=String(e+1);f(t),x(e=>({...e,numero_bobina:t}))}else f("1"),x(e=>({...e,numero_bobina:"1"}))}else f("1"),x(e=>({...e,numero_bobina:"1"}))}catch(e){f("1"),x(e=>({...e,numero_bobina:"1"}))}},B=(e,a)=>{x(t=>({...t,[e]:a})),h("")},R=async e=>{T(e),x(a=>({...a,configurazione:e})),k(!1),"s"===e?await O():x(e=>({...e,numero_bobina:""}))},L=()=>{if(!m.numero_bobina.trim())return"Il numero bobina \xe8 obbligatorio";if("m"===m.configurazione){let e=m.numero_bobina.trim();if(/[\s\\/:*?"<>|]/.test(e))return'Il numero bobina non pu\xf2 contenere spazi o caratteri speciali come \\ / : * ? " < > |'}if(!m.utility.trim())return"La utility \xe8 obbligatoria";if(!m.tipologia.trim())return"La tipologia \xe8 obbligatoria";if(!m.sezione.trim())return"La formazione \xe8 obbligatoria";if(!m.metri_totali.trim())return"I metri totali sono obbligatori";let e=parseFloat(m.metri_totali);return isNaN(e)||e<=0?"I metri totali devono essere un numero positivo":null},M=async()=>{let e=L();if(e)return void h(e);try{if(g(!0),h(""),!l||l<=0)throw Error("Cantiere non selezionato");let e={numero_bobina:m.numero_bobina.trim(),utility:m.utility.trim().toUpperCase(),tipologia:m.tipologia.trim().toUpperCase(),n_conduttori:"0",sezione:m.sezione.trim(),metri_totali:parseFloat(m.metri_totali),ubicazione_bobina:m.ubicazione_bobina.trim()||"TBD",fornitore:m.fornitore.trim()||"TBD",n_DDT:m.n_DDT.trim()||"TBD",data_DDT:m.data_DDT||null,configurazione:m.configurazione};await u.Fw.createBobina(l,e),c("Bobina ".concat(m.numero_bobina," creata con successo")),t()}catch(t){var a,i;let e=(null==(i=t.response)||null==(a=i.data)?void 0:a.detail)||t.message||"Errore durante la creazione della bobina";e.includes("gi\xe0 presente nel cantiere")||e.includes("gi\xe0 esistente")?h("⚠️ Bobina con numero ".concat(m.numero_bobina," gi\xe0 esistente. Scegli un numero diverso.")):d(e)}finally{g(!1)}},U=()=>{b||(x(C),h(""),t())};return(0,i.jsx)(N.lG,{open:a,onOpenChange:U,children:(0,i.jsxs)(N.Cf,{className:"max-h-[95vh] overflow-y-auto",style:{width:"1000px !important",maxWidth:"95vw !important",minWidth:"1000px"},children:[(0,i.jsxs)(N.c7,{children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-5 w-5"}),"Crea Nuova Bobina"]}),(0,i.jsx)(N.rr,{})]}),(0,i.jsxs)("div",{className:"grid gap-4 py-4",children:[j&&(0,i.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,i.jsx)(w.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Verifica configurazione..."})]}),I&&!j&&(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,i.jsx)("h4",{className:"font-medium mb-3",children:"Seleziona configurazione per questo cantiere"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Questa scelta determiner\xe0 come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere."}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,i.jsx)(r.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>R("s"),children:(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("div",{className:"font-medium",children:"Standard (s) - Numerazione automatica"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"I numeri bobina vengono generati automaticamente: 1, 2, 3..."})]})}),(0,i.jsx)(r.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>R("m"),children:(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("div",{className:"font-medium",children:"Manuale (m) - Inserimento manuale"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Puoi inserire numeri personalizzati: A123, TEST01, ecc."})]})})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[!I&&!j&&(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"numero_bobina",className:"text-right",children:"Bobina *"}),(0,i.jsx)(n.p,{id:"numero_bobina",value:m.numero_bobina,onChange:e=>B("numero_bobina",e.target.value),placeholder:"s"===m.configurazione?"Generato automaticamente":"Es: A123, TEST01",disabled:b||"s"===m.configurazione,className:"col-span-2 ".concat("s"===m.configurazione?"bg-gray-50":"")})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"utility",className:"text-right",children:"Utility *"}),(0,i.jsx)(n.p,{id:"utility",value:m.utility,onChange:e=>B("utility",e.target.value),className:"col-span-2",placeholder:"Es: ENEL, TIM, OPEN FIBER",disabled:b})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"tipologia",className:"text-right",children:"Tipologia *"}),(0,i.jsx)(n.p,{id:"tipologia",value:m.tipologia,onChange:e=>B("tipologia",e.target.value),className:"col-span-2",placeholder:"Es: FO, RAME",disabled:b})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"sezione",className:"text-right",children:"Formazione *"}),(0,i.jsx)(n.p,{id:"sezione",value:m.sezione,onChange:e=>B("sezione",e.target.value),className:"col-span-2",placeholder:"Es: 9/125, 50/125, 1.5",disabled:b})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"metri_totali",className:"text-right",children:"Metri Totali *"}),(0,i.jsx)(n.p,{id:"metri_totali",type:"number",step:"0.1",min:"0",value:m.metri_totali,onChange:e=>B("metri_totali",e.target.value),className:"col-span-2",placeholder:"Es: 1000",disabled:b})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"ubicazione_bobina",className:"text-right",children:"Ubicazione"}),(0,i.jsx)(n.p,{id:"ubicazione_bobina",value:m.ubicazione_bobina,onChange:e=>B("ubicazione_bobina",e.target.value),className:"col-span-2",placeholder:"Es: Magazzino A, Cantiere",disabled:b})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"fornitore",className:"text-right",children:"Fornitore"}),(0,i.jsx)(n.p,{id:"fornitore",value:m.fornitore,onChange:e=>B("fornitore",e.target.value),className:"col-span-2",placeholder:"Es: Prysmian, Nexans",disabled:b})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"n_DDT",className:"text-right",children:"N\xb0 DDT"}),(0,i.jsx)(n.p,{id:"n_DDT",value:m.n_DDT,onChange:e=>B("n_DDT",e.target.value),className:"col-span-2",placeholder:"Es: DDT001",disabled:b})]}),(0,i.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,i.jsx)(y.J,{htmlFor:"data_DDT",className:"text-right",children:"Data DDT"}),(0,i.jsx)(n.p,{id:"data_DDT",type:"date",value:m.data_DDT,onChange:e=>B("data_DDT",e.target.value),className:"col-span-2",disabled:b})]})]})]}),p&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:p})]})]}),(0,i.jsxs)(N.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:U,disabled:b||I,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:M,disabled:b||I||j,children:[b&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Creando...":"Crea Bobina"]})]})]})})}var D=t(59409),E=t(37108),A=t(81284);function T(e){let{open:a,onClose:t,bobina:l,onSuccess:d,onError:m}=e,{cantiere:x}=(0,c.A)(),[b,g]=(0,s.useState)({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),[p,h]=(0,s.useState)({}),[v,f]=(0,s.useState)({}),[j,_]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(a&&l){var e,t;g({numero_bobina:l.numero_bobina||"",utility:l.utility||"",tipologia:l.tipologia||"",sezione:l.sezione||"",metri_totali:(null==(e=l.metri_totali)?void 0:e.toString())||"",metri_residui:(null==(t=l.metri_residui)?void 0:t.toString())||"",stato_bobina:l.stato_bobina||"",ubicazione_bobina:l.ubicazione_bobina||"",fornitore:l.fornitore||"",n_DDT:l.n_DDT||"",data_DDT:l.data_DDT||"",configurazione:l.configurazione||""}),h({}),f({})}},[a,l]),(0,s.useEffect)(()=>{C()},[b]);let C=()=>{let e={};if(b.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),b.tipologia.trim()||(e.tipologia="Tipologia \xe8 obbligatoria"),b.sezione.trim()||(e.sezione="Formazione \xe8 obbligatoria"),b.metri_totali.trim()){let a=parseFloat(b.metri_totali);(isNaN(a)||a<=0)&&(e.metri_totali="Inserire un valore numerico valido maggiore di 0")}else e.metri_totali="Metri totali sono obbligatori";b.data_DDT&&!/^\d{4}-\d{2}-\d{2}$/.test(b.data_DDT)&&(e.data_DDT="Formato data non valido (YYYY-MM-DD)"),h(e),f({})},S=(e,a)=>{g(t=>{let i={...t,[e]:a};return"metri_totali"===e&&l&&(i.metri_residui=Math.max(0,(parseFloat(a)||0)-(l.metri_totali-l.metri_residui)).toString()),i})},T=()=>!!l&&("Over"===l.stato_bobina||"Disponibile"===l.stato_bobina),I=e=>!!l&&(!!["fornitore","ubicazione_bobina","n_DDT","data_DDT"].includes(e)||"Disponibile"===l.stato_bobina),k=async()=>{if(l&&x&&!(Object.keys(p).length>0)){if(!T())return void m("La bobina non pu\xf2 essere modificata nel suo stato attuale");try{_(!0);let e={utility:b.utility,tipologia:b.tipologia,sezione:b.sezione,metri_totali:parseFloat(b.metri_totali),ubicazione_bobina:b.ubicazione_bobina,fornitore:b.fornitore,n_DDT:b.n_DDT,data_DDT:b.data_DDT||null};await u.Fw.updateBobina(x.id_cantiere,l.id_bobina,e),d("Bobina ".concat(l.numero_bobina," aggiornata con successo")),t()}catch(t){var e,a;m((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante la modifica della bobina")}finally{_(!1)}}},F=()=>{j||(g({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),h({}),f({}),t())};return l?(0,i.jsx)(N.lG,{open:a,onOpenChange:F,children:(0,i.jsxs)(N.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(N.c7,{children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(E.A,{className:"h-5 w-5"}),"Modifica Bobina"]}),(0,i.jsxs)(N.rr,{children:["Modifica i dati della bobina ",l.numero_bobina]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)(o.Fc,{className:"border-blue-200 bg-blue-50",children:[(0,i.jsx)(A.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsxs)(o.TN,{className:"text-blue-800",children:[(0,i.jsx)("div",{className:"font-semibold mb-1",children:"Condizioni per la modifica:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1",children:[(0,i.jsx)("li",{children:'La bobina deve essere nello stato "Disponibile"'}),(0,i.jsx)("li",{children:"La bobina non deve essere associata a nessun cavo"}),(0,i.jsx)("li",{children:"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente"})]}),(0,i.jsxs)("div",{className:"mt-2 text-sm",children:[(0,i.jsx)("strong",{children:"Stato attuale:"})," ",l.stato_bobina]})]})]}),Object.keys(v).length>0&&(0,i.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,i.jsx)(z.A,{className:"h-4 w-4 text-amber-600"}),(0,i.jsxs)(o.TN,{className:"text-amber-800",children:[(0,i.jsx)("div",{className:"font-semibold",children:"Attenzione:"}),(0,i.jsx)("ul",{className:"list-disc list-inside text-sm",children:Object.values(v).map((e,a)=>(0,i.jsx)("li",{children:e},a))})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"numero_bobina",children:"ID Bobina"}),(0,i.jsx)(n.p,{id:"numero_bobina",value:b.numero_bobina,disabled:!0,className:"bg-gray-100 font-semibold"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"utility",children:"Utility *"}),(0,i.jsx)(n.p,{id:"utility",value:b.utility,onChange:e=>S("utility",e.target.value),disabled:j||!I("utility"),className:p.utility?"border-red-500":""}),p.utility&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:p.utility})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"tipologia",children:"Tipologia *"}),(0,i.jsx)(n.p,{id:"tipologia",value:b.tipologia,onChange:e=>S("tipologia",e.target.value),disabled:j||!I("tipologia"),className:p.tipologia?"border-red-500":""}),p.tipologia&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:p.tipologia})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"sezione",children:"Formazione *"}),(0,i.jsx)(n.p,{id:"sezione",value:b.sezione,onChange:e=>S("sezione",e.target.value),disabled:j||!I("sezione"),className:p.sezione?"border-red-500":""}),p.sezione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:p.sezione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"metri_totali",children:"Metri Totali *"}),(0,i.jsx)(n.p,{id:"metri_totali",type:"number",value:b.metri_totali,onChange:e=>S("metri_totali",e.target.value),disabled:j||!I("metri_totali"),className:p.metri_totali?"border-red-500":"",step:"0.1",min:"0"}),p.metri_totali&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:p.metri_totali})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"metri_residui",children:"Metri Residui"}),(0,i.jsx)(n.p,{id:"metri_residui",type:"number",value:b.metri_residui,disabled:!0,className:"bg-gray-100"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"I metri residui non possono essere modificati direttamente"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"stato_bobina",children:"Stato Bobina"}),(0,i.jsxs)(D.l6,{value:b.stato_bobina,disabled:!0,children:[(0,i.jsx)(D.bq,{className:"bg-gray-100",children:(0,i.jsx)(D.yv,{})}),(0,i.jsxs)(D.gC,{children:[(0,i.jsx)(D.eb,{value:"Disponibile",children:"Disponibile"}),(0,i.jsx)(D.eb,{value:"In uso",children:"In uso"}),(0,i.jsx)(D.eb,{value:"Terminata",children:"Terminata"}),(0,i.jsx)(D.eb,{value:"Danneggiata",children:"Danneggiata"}),(0,i.jsx)(D.eb,{value:"Over",children:"Over"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"ubicazione_bobina",children:"Ubicazione Bobina"}),(0,i.jsx)(n.p,{id:"ubicazione_bobina",value:b.ubicazione_bobina,onChange:e=>S("ubicazione_bobina",e.target.value),disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"fornitore",children:"Fornitore"}),(0,i.jsx)(n.p,{id:"fornitore",value:b.fornitore,onChange:e=>S("fornitore",e.target.value),disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"n_DDT",children:"Numero DDT"}),(0,i.jsx)(n.p,{id:"n_DDT",value:b.n_DDT,onChange:e=>S("n_DDT",e.target.value),disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(y.J,{htmlFor:"data_DDT",children:"Data DDT (YYYY-MM-DD)"}),(0,i.jsx)(n.p,{id:"data_DDT",type:"date",value:b.data_DDT,onChange:e=>S("data_DDT",e.target.value),disabled:j,className:p.data_DDT?"border-red-500":""}),p.data_DDT&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:p.data_DDT})]}),(0,i.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,i.jsx)(y.J,{htmlFor:"configurazione",children:"Modalit\xe0 Numerazione"}),(0,i.jsx)(n.p,{id:"configurazione",value:"s"===b.configurazione?"Automatica":"Manuale",disabled:!0,className:"bg-gray-100"}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"s"===b.configurazione?"Numerazione progressiva automatica (1, 2, 3, ...)":"Inserimento manuale dell'ID bobina (es. A123, SPEC01, ...)"})]})]})]}),(0,i.jsxs)(N.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:F,disabled:j,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:k,disabled:j||Object.keys(p).length>0||!T(),className:"bg-mariner-600 hover:bg-mariner-700",children:[j&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}var I=t(62525),k=t(1243);function F(e){let{open:a,onClose:t,bobina:n,cantiereId:l,onSuccess:c,onError:d}=e,[m,b]=(0,s.useState)(!1),p=async()=>{var e,a,i;if(n)try{if(b(!0),!l||l<=0)throw Error("Cantiere non selezionato");let a=n.id_bobina.split("_B")[1],i=await u.Fw.deleteBobina(l,a),s="Bobina ".concat(n.numero_bobina," eliminata con successo");(null==(e=i.data)?void 0:e.is_last_bobina)&&(s+=". Era l'ultima bobina del cantiere."),c(s),t()}catch(e){d((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||e.message||"Errore durante l'eliminazione della bobina")}finally{b(!1)}},h=()=>{m||t()};if(!n)return null;let v=n.stato_bobina===x.OVER,f=!v&&g(n.stato_bobina)&&n.metri_residui===n.metri_totali,y=v?{type:"error",title:"Bobina OVER - Eliminazione bloccata",message:"Le bobine in stato OVER non possono essere eliminate per preservare la tracciabilit\xe0 del sistema. Contattare l'amministratore per la gestione di bobine OVER."}:g(n.stato_bobina)?n.metri_residui!==n.metri_totali?{type:"error",title:"Bobina in uso",message:"La bobina ha ".concat(n.metri_residui,"m residui su ").concat(n.metri_totali,"m totali. Solo le bobine completamente disponibili possono essere eliminate.")}:{type:"warning",title:"Conferma eliminazione",message:"Questa operazione \xe8 irreversibile. La bobina verr\xe0 rimossa definitivamente dal parco cavi."}:{type:"error",title:"Eliminazione non consentita",message:'La bobina \xe8 in stato "'.concat(n.stato_bobina,'" e non pu\xf2 essere eliminata. ').concat(j(n.stato_bobina))};return(0,i.jsx)(N.lG,{open:a,onOpenChange:h,children:(0,i.jsxs)(N.Cf,{className:"max-w-md",children:[(0,i.jsxs)(N.c7,{children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(I.A,{className:"h-5 w-5 text-red-600"}),"Elimina Bobina"]}),(0,i.jsxs)(N.rr,{children:["Stai per eliminare la bobina ",n.numero_bobina]})]}),(0,i.jsxs)("div",{className:"py-4",children:[(0,i.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg mb-4",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Dettagli bobina:"}),(0,i.jsxs)("div",{className:"text-sm space-y-1",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Bobina:"})," ",n.numero_bobina]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Utility:"})," ",n.utility]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipologia:"})," ",n.tipologia]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Sezione:"})," ",n.sezione]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Stato:"})," ",n.stato_bobina]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Metri:"})," ",n.metri_residui,"m / ",n.metri_totali,"m"]}),n.ubicazione_bobina&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Ubicazione:"})," ",n.ubicazione_bobina]})]})]}),(0,i.jsxs)(o.Fc,{variant:"error"===y.type?"destructive":"default",children:[(0,i.jsx)(k.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:y.title}),(0,i.jsx)(o.TN,{className:"mt-1",children:y.message})]})]})]}),(0,i.jsxs)(N.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:h,disabled:m,children:"Annulla"}),(0,i.jsxs)(r.$,{variant:"destructive",onClick:p,disabled:m||!f,children:[m&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),m?"Eliminando...":"Elimina Bobina"]})]})]})})}var O=t(17313),B=t(3493),R=t(47924);let L=(e,a)=>{let[t,i]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let t=setTimeout(()=>{i(e)},a);return()=>{clearTimeout(t)}},[e,a]),t};function M(e){let{open:a,onClose:t,bobina:l,cantiereId:o,onSuccess:c,onError:d}=e,[m,x]=(0,s.useState)(!1),[b,g]=(0,s.useState)(!1),[p,h]=(0,s.useState)(!1),[v,f]=(0,s.useState)([]),[j,y]=(0,s.useState)([]),[_,z]=(0,s.useState)([]),[C,S]=(0,s.useState)({}),[D,E]=(0,s.useState)({}),[A,T]=(0,s.useState)(""),I=L(A,300),[k,F]=(0,s.useState)("id_cavo"),[M,U]=(0,s.useState)("asc"),[V,P]=(0,s.useState)("all"),[J,$]=(0,s.useState)("all"),[W,Z]=(0,s.useState)(""),[Y,G]=(0,s.useState)(""),[q,H]=(0,s.useState)(1),[X,Q]=(0,s.useState)(20),K=(0,s.useMemo)(()=>{let e=e=>e.filter(e=>{var a,t,i,s;let r=!I||e.id_cavo.toLowerCase().includes(I.toLowerCase())||(null==(a=e.tipologia)?void 0:a.toLowerCase().includes(I.toLowerCase()))||(null==(t=e.ubicazione_partenza)?void 0:t.toLowerCase().includes(I.toLowerCase()))||(null==(i=e.ubicazione_arrivo)?void 0:i.toLowerCase().includes(I.toLowerCase())),n="all"===V||e.tipologia===V,l="all"===J||e.sezione===J,o=parseFloat((null==(s=e.metri_teorici)?void 0:s.toString())||"0"),c=!W||o>=parseFloat(W),d=!Y||o<=parseFloat(Y);return r&&n&&l&&c&&d}),a=e=>[...e].sort((e,a)=>{let t,i;switch(k){case"id_cavo":t=e.id_cavo,i=a.id_cavo;break;case"metri_teorici":var s,r;t=parseFloat((null==(s=e.metri_teorici)?void 0:s.toString())||"0"),i=parseFloat((null==(r=a.metri_teorici)?void 0:r.toString())||"0");break;case"tipologia":t=e.tipologia||"",i=a.tipologia||"";break;case"ubicazione_partenza":t=e.ubicazione_partenza||"",i=a.ubicazione_partenza||"";break;default:return 0}if("string"!=typeof t)return"asc"===M?t-i:i-t;{let e=t.localeCompare(i);return"asc"===M?e:-e}});return{compatibiliFiltrati:a(e(v)),incompatibiliFiltrati:a(e(j))}},[v,j,I,k,M,V,J,W,Y]),ee=(0,s.useMemo)(()=>{let{compatibiliFiltrati:e,incompatibiliFiltrati:a}=K,t=(q-1)*X,i=t+X;return{compatibili:e.slice(t,i),incompatibili:a.slice(t,i),totalCompatibili:e.length,totalIncompatibili:a.length,totalPages:Math.ceil(Math.max(e.length,a.length)/X)}},[K,q,X]),ea=(0,s.useMemo)(()=>{let e=Object.values(C).reduce((e,a)=>e+parseFloat(a||"0"),0),a=(null==l?void 0:l.metri_residui)||0,t=Object.entries(C).some(e=>{let[t,i]=e;return parseFloat(i||"0")>a}),i=_.some(e=>e._isIncompatible),s=t||(null==l?void 0:l.stato_bobina)==="OVER",r=Math.max(0,e-a);return{metriTotaliSelezionati:e,metriResiduiBobina:a,isOverState:s,metriEccedenza:r,percentualeUtilizzo:a>0?e/a*100:0,hasSingleCavoOver:t,hasIncompatibleCavi:i}},[C,l,_]);(0,s.useMemo)(()=>Array.from(new Set([...v,...j].map(e=>e.tipologia).filter(Boolean))).sort(),[v,j]),(0,s.useMemo)(()=>Array.from(new Set([...v,...j].map(e=>e.sezione).filter(Boolean))).sort(),[v,j]);let et=async()=>{if(o&&l)try{g(!0);let e=(await u.At.getCavi(o)).filter(e=>{var a,t;let i=parseFloat((null==(a=e.metratura_reale)?void 0:a.toString())||"0")||0,s=parseFloat((null==(t=e.metri_teorici)?void 0:t.toString())||"0")||0,r=!("Installato"===e.stato_installazione||i>0),n=3!==e.modificato_manualmente;return r&&n&&0===i&&s>0}),a=e.filter(e=>{let a=e.tipologia===l.tipologia&&String(e.sezione)===String(l.sezione);return console.log("\uD83D\uDD0D AggiungiCaviDialogSimple: Controllo compatibilit\xe0:",{cavoTip:e.tipologia,bobinaTip:l.tipologia,cavoSez:e.sezione,bobinaSez:l.sezione,cavoTipType:typeof e.tipologia,cavoSezType:typeof e.sezione,tipMatch:e.tipologia===l.tipologia,sezMatch:String(e.sezione)===String(l.sezione),cavoSezioneString:String(e.sezione),bobinaSezioneString:String(l.sezione),isCompatible:a}),a}),t=e.filter(e=>e.tipologia!==l.tipologia||String(e.sezione)!==String(l.sezione));a.length,f(a),y(t)}catch(e){d("Errore nel caricamento dei cavi: "+(e.message||"Errore sconosciuto"))}finally{g(!1)}};(0,s.useEffect)(()=>{console.log("\uD83D\uDD04 AggiungiCaviDialogSimple: Dialog reset:",{open:a,bobina:!!l,cantiereId:o,bobinaData:l}),a&&l&&o&&!(o<=0)&&(z([]),S({}),E({}),et())},[a,l,o]);let ei=(e,a)=>{if(console.log("\uD83C\uDFAF AggiungiCaviDialogSimple: Toggle cavo:",{cavoId:e.id_cavo,isCompatible:a,metriTeorici:e.metri_teorici}),_.some(a=>a.id_cavo===e.id_cavo))z(a=>a.filter(a=>a.id_cavo!==e.id_cavo)),S(a=>{let t={...a};return delete t[e.id_cavo],t});else{let t={...e,_isIncompatible:!a};z(e=>[...e,t]),S(a=>({...a,[e.id_cavo]:"0"}))}},es=(e,a)=>{null==l||l.metri_residui,parseFloat(a||"0");let t=_.find(a=>a.id_cavo===e);null==t||t._isIncompatible,E(a=>{let t={...a};return delete t[e],t}),S(t=>({...t,[e]:a}))},er=(0,s.useMemo)(()=>{let e=(null==l?void 0:l.metri_residui)||0,a=!1,t=[],i=[],s=null;for(let r of _){let n=parseFloat(C[r.id_cavo]||"0");n>0?a?i.push(r.id_cavo):(e-n<0?(t.push(r.id_cavo),s=r.id_cavo,a=!0):t.push(r.id_cavo),e-=n):t.push(r.id_cavo)}return{metriResiduiSimulati:e,caviValidi:t,caviBloccati:i,bobinaGiaOver:a,cavoCheCausaOver:s}},[_,C,null==l?void 0:l.metri_residui]),en=()=>er,el=async()=>{if(!o||!l)return;if(0===_.length)return void d("Nessun cavo selezionato");let e=_.filter(e=>{let a=C[e.id_cavo];return!a||""===a.trim()||isNaN(parseFloat(a))||0>parseFloat(a)});if(e.length>0)return void d("Metri posati mancanti o non validi per: ".concat(e.map(e=>e.id_cavo).join(", ")));try{h(!0);let{caviValidi:e,caviBloccati:a}=en(),i=_.filter(e=>{let t=parseFloat(C[e.id_cavo]||"0"),i=a.includes(e.id_cavo);return t>0&&!i});if(0===i.length)return void d("Nessun cavo valido da salvare (tutti bloccati o senza metri)");console.log("\uD83D\uDCBE AggiungiCaviDialogSimple: Preparazione salvataggio:",{caviSelezionati:_.length,caviValidi:e.length,caviBloccati:a.length,caviDaSalvare:i.length});let s=[],r=[],n=(null==l?void 0:l.metri_residui)||0;for(let e of i)try{let a=C[e.id_cavo],t=parseFloat(a),i=n-t<0,r=e._isIncompatible||!1,c=i||r;console.log("⚡ AggiungiCaviDialogSimple: Aggiornamento cavo:",{metriPosati:t,metriResiduiCorrente:n,causaOver:i,isIncompatible:r,needsForceOver:c}),await u.At.updateMetriPosati(o,e.id_cavo,t,l.id_bobina,c),n-=t,s.push({cavo:e.id_cavo,metriPosati:t,success:!0,wasIncompatible:e._isIncompatible,wasForceOver:c})}catch(t){let a="Errore sconosciuto";if(t.response){let e=t.response.status,i=t.response.data;a=400===e?(null==i?void 0:i.message)||(null==i?void 0:i.error)||"Richiesta non valida (400)":404===e?"Cavo o bobina non trovati (404)":409===e?"Conflitto: cavo gi\xe0 assegnato o bobina non disponibile (409)":500===e?"Errore interno del server (500)":"Errore HTTP ".concat(e,": ").concat((null==i?void 0:i.message)||(null==i?void 0:i.error)||"Errore del server")}else a=t.request?"Errore di connessione al server":t.message||"Errore di validazione";r.push({cavo:e.id_cavo,error:a})}if(0===r.length){let e=s.filter(e=>e.wasIncompatible).length,a=s.filter(e=>e.wasForceOver).length,i="".concat(s.length," cavi aggiornati con successo");e>0&&(i+=" (".concat(e," incompatibili)")),a>0&&(i+=" (".concat(a," con force_over)")),c(i),t()}else d("Errori: ".concat(r.map(e=>"".concat(e.cavo,": ").concat(e.error)).join(", ")))}catch(a){let e="Errore durante il salvataggio dei cavi";if(a.response){let t=a.response.status,i=a.response.data;e=400===t?"Errore di validazione: ".concat((null==i?void 0:i.message)||(null==i?void 0:i.error)||"Dati non validi"):401===t?"Sessione scaduta. Effettua nuovamente il login.":403===t?"Non hai i permessi per questa operazione":500===t?"Errore interno del server. Riprova pi\xf9 tardi.":"Errore del server (".concat(t,"): ").concat((null==i?void 0:i.message)||(null==i?void 0:i.error)||"Errore sconosciuto")}else e=a.request?"Impossibile contattare il server. Verifica la connessione.":a.message||"Errore imprevisto durante il salvataggio";d(e)}finally{h(!1)}},eo=()=>{p||(z([]),S({}),E({}),t())};if(!l)return null;let ec=e=>{let a=e.match(/C\d+_B(\d+)/);return a?a[1]:e},ed=(e,a)=>(console.log("\uD83D\uDCCB AggiungiCaviDialogSimple: Rendering lista cavi:",{isCompatible:a,caviLength:e.length,primi3Cavi:e.slice(0,3).map(e=>({id:e.id_cavo,tipologia:e.tipologia,sezione:e.sezione}))}),0===e.length)?(0,i.jsx)("div",{className:"h-[300px] flex items-center justify-center text-gray-500 border rounded",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(B.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,i.jsxs)("div",{children:["Nessun cavo ",a?"compatibile":"incompatibile"," disponibile"]}),(0,i.jsx)("div",{className:"text-xs mt-2 text-gray-400",children:a?'Cerca cavi con tipologia "'.concat(null==l?void 0:l.tipologia,'" e formazione "').concat(null==l?void 0:l.sezione,'"'):"I cavi incompatibili hanno tipologia o formazione diverse"})]})}):(0,i.jsx)("div",{className:"space-y-1 h-[300px] overflow-y-auto border rounded p-2 w-full",children:e.map((e,t)=>{let s=_.some(a=>a.id_cavo===e.id_cavo),r=C[e.id_cavo]||"",{caviBloccati:n,cavoCheCausaOver:l}=er,o=s&&n.includes(e.id_cavo),c=s&&e.id_cavo===l;return(0,i.jsxs)("div",{className:"border rounded px-3 py-2 transition-colors ".concat(o?"border-red-300 bg-red-50":c?"border-orange-300 bg-orange-50":s?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 w-full overflow-hidden",children:[(0,i.jsx)("input",{type:"checkbox",checked:s,onChange:t=>{ei(e,a)},className:"h-4 w-4 text-blue-600 border-gray-300 rounded flex-shrink-0"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0 overflow-hidden",children:[(0,i.jsx)("span",{className:"font-medium text-gray-900 flex-shrink-0",children:e.id_cavo}),(0,i.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.tipologia}),(0,i.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.sezione}),(0,i.jsxs)("span",{className:"text-xs text-gray-600 flex-shrink-0",children:[e.metri_teorici,"m"]}),o&&(0,i.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"BLOCCATO"}),c&&(0,i.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"CAUSA OVER"}),(0,i.jsxs)("span",{className:"text-xs text-gray-500 truncate min-w-0",children:[e.ubicazione_partenza||"N/A"," → ",e.ubicazione_arrivo||"N/A"]})]}),s&&(0,i.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,i.jsx)("label",{className:"text-xs text-gray-600",children:"m:"}),(0,i.jsx)("input",{type:"number",step:"0.1",min:"0",value:r,onChange:a=>{es(e.id_cavo,a.target.value)},className:"w-16 px-1 py-1 border rounded text-xs",placeholder:"0"})]})]}),D[e.id_cavo]&&(0,i.jsx)("div",{className:"text-red-600 text-xs mt-1 ml-7",children:D[e.id_cavo]})]},e.id_cavo)})});return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(N.lG,{open:a,onOpenChange:eo,children:(0,i.jsxs)(N.Cf,{className:"h-[85vh] w-full max-w-5xl overflow-hidden",style:{width:"950px !important",maxWidth:"95vw !important",minWidth:"850px"},children:[(0,i.jsxs)(N.c7,{className:"pb-0",children:[(0,i.jsxs)(N.L3,{className:"flex items-center gap-2 mb-0 text-lg",children:[(0,i.jsx)(B.A,{className:"h-5 w-5"}),"\uD83D\uDD25 NUOVO SISTEMA OVER - Aggiungi cavi alla bobina ",ec(l.id_bobina)]}),(0,i.jsx)(N.rr,{className:"mb-0 text-xs text-gray-600 mt-0",children:"Seleziona cavi e inserisci metri posati (SISTEMA AGGIORNATO)"})]}),(0,i.jsxs)("div",{className:"space-y-1 mt-2",children:[(0,i.jsx)("div",{className:"bg-gray-50 px-3 py-1 rounded text-sm",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsxs)("span",{children:[(0,i.jsxs)("strong",{children:["Bobina ",ec(l.id_bobina)]})," • ",l.tipologia," • ",l.sezione]}),(0,i.jsxs)("span",{children:["Residui: ",(0,i.jsxs)("strong",{children:[l.metri_residui,"m"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)("span",{children:["Selezionati: ",(0,i.jsx)("strong",{children:_.length})," cavi • ",(0,i.jsxs)("strong",{children:[ea.metriTotaliSelezionati.toFixed(1),"m"]})]}),ea.isOverState&&(0,i.jsx)("span",{className:"text-red-600 font-medium",children:"OVER!"})]})]})}),(0,i.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)(R.A,{className:"absolute left-2 top-2 h-4 w-4 text-gray-400"}),(0,i.jsx)(n.p,{placeholder:"Cerca cavi...",value:A,onChange:e=>T(e.target.value),className:"pl-8 h-8 text-sm"})]}),(0,i.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>{z([]),S({}),E({})},disabled:0===_.length,className:"h-8 px-3 text-sm",children:"Reset"})]}),b&&(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(w.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento cavi..."})]}),!b&&(0,i.jsxs)(O.tU,{defaultValue:"compatibili",className:"w-full",children:[(0,i.jsxs)(O.j7,{className:"flex justify-center gap-6 bg-transparent border-0 h-auto p-0",children:[(0,i.jsxs)(O.Xi,{value:"compatibili",className:"tab-trigger flex items-center gap-2",children:[(0,i.jsx)("span",{className:"w-2 h-2 rounded-full bg-green-500"}),"Cavi Compatibili (",ee.totalCompatibili,")"]}),(0,i.jsxs)(O.Xi,{value:"incompatibili",className:"tab-trigger flex items-center gap-2",children:[(0,i.jsx)("span",{className:"w-2 h-2 rounded-full bg-orange-500"}),"Cavi Incompatibili (",ee.totalIncompatibili,")"]})]}),(0,i.jsx)(O.av,{value:"compatibili",className:"mt-4 w-full overflow-hidden",children:(0,i.jsx)("div",{className:"w-full overflow-hidden",children:ed(ee.compatibili,!0)})}),(0,i.jsx)(O.av,{value:"incompatibili",className:"mt-4 w-full overflow-hidden",children:(0,i.jsx)("div",{className:"w-full overflow-hidden",children:ed(ee.incompatibili,!1)})})]})]}),(0,i.jsxs)(N.Es,{className:"flex justify-between items-center",children:[(0,i.jsx)("div",{className:"text-sm text-gray-600",children:_.length>0?(()=>{let{metriResiduiSimulati:e,caviValidi:a,caviBloccati:t,cavoCheCausaOver:s}=er,r=_.reduce((e,a)=>e+parseFloat(C[a.id_cavo]||"0"),0),n=((null==l?void 0:l.metri_residui)||0)-e,o=_.filter(e=>e._isIncompatible).length;return(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{children:[_.length," cavi selezionati • ",r.toFixed(1),"m totali"]}),(0,i.jsxs)("div",{children:["✅ ",a.length," salvabili • ❌ ",t.length," bloccati"]}),(0,i.jsxs)("div",{children:["Usati: ",n.toFixed(1),"m / ",(null==l?void 0:l.metri_residui)||0,"m",e<0&&s&&(0,i.jsxs)("span",{className:"text-orange-600 font-medium ml-2",children:["(OVER da ",s,": +",Math.abs(e).toFixed(1),"m)"]})]}),o>0&&(0,i.jsxs)("div",{className:"text-orange-600 font-medium",children:["⚠️ ",o," cavi incompatibili"]})]})})():(0,i.jsx)("div",{children:"Nessun cavo selezionato"})}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(r.$,{variant:"outline",onClick:eo,disabled:p,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:el,disabled:p||0===_.length,className:ea.isOverState?"bg-orange-600 hover:bg-orange-700":"",children:[p&&(0,i.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),p?"Salvataggio...":ea.isOverState?"Salva ".concat(_.length," cavi (OVER)"):"Salva ".concat(_.length," cavi")]})]})]})]})})})}var U=t(66695),V=t(72713),P=t(40646),J=t(14186);function $(e){let{bobine:a,filteredBobine:t,className:r}=e,n=(0,s.useMemo)(()=>{let e=a.length,i=t.length,s=t.filter(e=>"Disponibile"===e.stato_bobina).length,r=t.filter(e=>"In uso"===e.stato_bobina).length,n=t.filter(e=>"Terminata"===e.stato_bobina).length,l=t.filter(e=>"Over"===e.stato_bobina).length,o=t.reduce((e,a)=>e+(a.metri_totali||0),0),c=t.reduce((e,a)=>e+(a.metri_residui||0),0),d=o-c,m=o>0?Math.round(d/o*100):0;return{totalBobine:e,filteredCount:i,disponibili:s,inUso:r,terminate:n,over:l,metriTotali:o,metriResidui:c,metriUtilizzati:d,percentualeUtilizzo:m}},[a,t]);return(0,i.jsx)(U.Zp,{className:r,children:(0,i.jsxs)(U.Wu,{className:"p-1.5",children:[(0,i.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,i.jsx)(V.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Bobine"})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(E.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:n.filteredCount}),(0,i.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",n.totalBobine," bobine"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(P.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-green-700 text-sm",children:n.disponibili}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:"disponibili"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(J.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:n.inUso}),(0,i.jsx)("div",{className:"text-xs text-yellow-600",children:"in uso"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(k.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-red-700 text-sm",children:n.terminate}),(0,i.jsx)("div",{className:"text-xs text-red-600",children:"terminate"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(z.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-red-700 text-sm",children:n.over}),(0,i.jsx)("div",{className:"text-xs text-red-600",children:"over"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,i.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[n.metriUtilizzati.toLocaleString(),"m"]}),(0,i.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",n.metriTotali.toLocaleString(),"m"]})]})]})]}),n.filteredCount>0&&(0,i.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,i.jsx)("span",{children:"Utilizzo Complessivo Bobine"}),(0,i.jsxs)("span",{className:"font-bold ".concat(n.percentualeUtilizzo>=80?"text-amber-700":n.percentualeUtilizzo>=60?"text-orange-700":n.percentualeUtilizzo>=40?"text-yellow-700":"text-emerald-700"),children:[n.percentualeUtilizzo,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(n.percentualeUtilizzo>=80?"bg-gradient-to-r from-amber-500 to-amber-600":n.percentualeUtilizzo>=60?"bg-gradient-to-r from-orange-500 to-orange-600":n.percentualeUtilizzo>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"),style:{width:"".concat(Math.min(n.percentualeUtilizzo,100),"%")}})}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,i.jsx)("span",{children:"Metri utilizzati vs totali disponibili"}),(0,i.jsxs)("span",{children:[n.metriResidui.toLocaleString(),"m residui"]})]})]})]})})}var W=t(83744),Z=t(23837),Y=t(13717),G=t(47650);function q(e){let{children:a,items:t,onAction:r,disabled:n=!1}=e,[l,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)({x:0,y:0}),[m,u]=(0,s.useState)(null),x=(0,s.useRef)(null),b=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=e=>{x.current&&!x.current.contains(e.target)&&o(!1)},a=e=>{"Escape"===e.key&&o(!1)};return l&&(document.addEventListener("mousedown",e),document.addEventListener("keydown",a)),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("keydown",a)}},[l]);let g=e=>{e.disabled||(o(!1),r(e.action,m))},p=e=>{switch(e){case"warning":return"text-amber-600 hover:bg-amber-50";case"danger":return"text-red-600 hover:bg-red-50";default:return"text-gray-700 hover:bg-gray-100"}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{ref:b,onContextMenu:e=>{var a;if(n)return;e.preventDefault(),e.stopPropagation(),null==(a=b.current)||a.getBoundingClientRect();let i=e.clientX,s=e.clientY,r=40*t.length,l=window.innerWidth;d({x:i+200>l?i-200:i,y:s+r>window.innerHeight?s-r:s}),u(e.currentTarget.dataset),o(!0)},className:"w-full h-full",children:a}),l?(0,G.createPortal)((0,i.jsx)("div",{ref:x,className:"fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1",style:{left:c.x,top:c.y},children:t.map((e,a)=>e.separator?(0,i.jsx)("div",{className:"border-t border-gray-200 my-1"},"separator-".concat(a)):(0,i.jsxs)("button",{className:"w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ".concat(e.disabled?"text-gray-400 cursor-not-allowed":p(e.color)),onClick:()=>g(e),disabled:e.disabled,children:[e.icon&&(0,i.jsx)("span",{className:"w-4 h-4",children:e.icon}),(0,i.jsx)("span",{children:e.label})]},e.id))}),document.body):null]})}function H(){let[e,a]=(0,s.useState)(""),[t,g]=(0,s.useState)("all"),[N,y]=(0,s.useState)([]),[C,D]=(0,s.useState)(!0),[E,A]=(0,s.useState)(""),{user:k,isLoading:O}=(0,c.A)(),{cantiereId:L,cantiere:U,isValidCantiere:V,isLoading:J,error:G}=(0,d.jV)(),[H,X]=(0,s.useState)(!1),[Q,K]=(0,s.useState)(!1),[ee,ea]=(0,s.useState)(!1),[et,ei]=(0,s.useState)(!1),[es,er]=(0,s.useState)(null),[en,el]=(0,s.useState)(""),[eo,ec]=(0,s.useState)("");(0,s.useEffect)(()=>{V&&L&&L>0&&!J?(console.log("\uD83C\uDFD7️ ParcoCaviPage: Caricamento bobine per cantiere:",L),ed()):J||V||(console.warn("\uD83C\uDFD7️ ParcoCaviPage: Cantiere non valido, reset dati"),y([]),A(G||"Nessun cantiere selezionato"))},[L,V,J,G]);let ed=async()=>{try{if(D(!0),A(""),!L||L<=0){A("Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine."),y([]);return}let e=await u.Fw.getBobine(L);y(e||[])}catch(t){var e,a;A((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il caricamento delle bobine"),y([])}finally{D(!1)}};(0,s.useEffect)(()=>{if(en){let e=setTimeout(()=>el(""),5e3);return()=>clearTimeout(e)}},[en]),(0,s.useEffect)(()=>{if(eo){let e=setTimeout(()=>ec(""),5e3);return()=>clearTimeout(e)}},[eo]);let em=e=>{er(e),ei(!0)},eu=e=>{er(e),K(!0)},ex=e=>{er(e),ea(!0)},eb=e=>{el(e),ed()},eg=e=>{ec(e)},ep=()=>{el("Funzione import in sviluppo")},eh=()=>{el("Funzione export in sviluppo")},ev=(e,a,t)=>{let s=e||b(a,t),r={disponibile:{dotColor:"bg-green-500",textColor:"text-green-700",label:"DISPONIBILE"},"in uso":{dotColor:"bg-blue-500",textColor:"text-blue-700",label:"IN USO"},terminata:{dotColor:"bg-gray-500",textColor:"text-gray-700",label:"TERMINATA"},over:{dotColor:"bg-red-500",textColor:"text-red-700",label:"OVER"}},n=r[null==s?void 0:s.toLowerCase()]||r.terminata;return(0,i.jsxs)("div",{className:"flex items-center gap-2",title:j(s),children:[(0,i.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(n.dotColor)}),(0,i.jsx)("span",{className:"text-sm font-semibold ".concat(n.textColor),children:n.label})]})},ef=N.filter(a=>{var i,s,r;let n=(null==(i=a.numero_bobina)?void 0:i.toLowerCase().includes(e.toLowerCase()))||(null==(s=a.tipologia)?void 0:s.toLowerCase().includes(e.toLowerCase()))||(null==(r=a.utility)?void 0:r.toLowerCase().includes(e.toLowerCase())),l=!0;if("all"!==t){let e=a.stato_bobina||b(a.metri_residui,a.metri_totali);switch(t){case"disponibile":l=e===x.DISPONIBILE;break;case"in_uso":l=e===x.IN_USO;break;case"esaurita":l=e===x.TERMINATA;break;case"over":l=e===x.OVER}}return n&&l});return(0,i.jsx)(m.u,{children:(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto py-6 space-y-6",children:[E&&(0,i.jsxs)(o.Fc,{variant:"destructive",className:"mb-6",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:E})]}),(0,i.jsx)($,{bobine:N,filteredBobine:ef,className:"mb-6"}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-1",children:"Ricerca e Filtri Bobine"})}),(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 items-start lg:items-center",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(R.A,{className:"h-4 w-4 text-gray-400"})}),(0,i.jsx)(n.p,{placeholder:"Cerca per ID bobina, tipologia, o numero...",value:e,onChange:e=>a(e.target.value),className:"pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"})]}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"Tutte",count:N.length},{key:"disponibile",label:"Disponibili",count:N.filter(e=>(e.stato_bobina||b(e.metri_residui,e.metri_totali))===x.DISPONIBILE).length},{key:"in_uso",label:"In Uso",count:N.filter(e=>(e.stato_bobina||b(e.metri_residui,e.metri_totali))===x.IN_USO).length},{key:"esaurita",label:"Esaurite",count:N.filter(e=>(e.stato_bobina||b(e.metri_residui,e.metri_totali))===x.TERMINATA).length},{key:"over",label:"Over",count:N.filter(e=>(e.stato_bobina||b(e.metri_residui,e.metri_totali))===x.OVER).length}].map(e=>(0,i.jsxs)("button",{onClick:()=>g(e.key),className:"inline-flex items-center gap-1.5 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ".concat(t===e.key?"bg-blue-600 text-white shadow-sm":"bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300"),children:[e.label,(0,i.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ".concat(t===e.key?"bg-blue-500 text-white":"bg-gray-200 text-gray-600"),children:e.count})]},e.key))})]})]}),(0,i.jsx)(q,{items:[{id:"import",label:"Importa Bobine",icon:(0,i.jsx)(W.A,{className:"h-4 w-4"}),action:"import",disabled:!L||L<=0},{id:"export",label:"Esporta Bobine",icon:(0,i.jsx)(Z.A,{className:"h-4 w-4"}),action:"export",disabled:!L||L<=0},{id:"separator1",separator:!0},{id:"add_bobina",label:"Aggiungi Bobina",icon:(0,i.jsx)(_.A,{className:"h-4 w-4"}),action:"add_bobina",disabled:!L||L<=0}],onAction:(e,a)=>{switch(e){case"import":ep();break;case"export":eh();break;case"add_bobina":X(!0)}},children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,i.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-lg font-semibold text-gray-800",children:["Elenco Bobine (",ef.length,")"]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Gestisci le bobine, visualizza lo stato di utilizzo e le metrature. Clicca tasto destro per azioni aggiuntive."})]}),(0,i.jsxs)(r.$,{onClick:()=>X(!0),disabled:!L||L<=0,title:!L||L<=0?"Seleziona un cantiere per creare una bobina":"Crea nuova bobina",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4"}),"Nuova Bobina"]})]})}),(0,i.jsx)("div",{className:"overflow-hidden",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)(l.XI,{children:[(0,i.jsx)(l.A0,{children:(0,i.jsxs)(l.Hj,{className:"border-b border-gray-200",children:[(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Bobina"}),(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Utility"}),(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Tipologia"}),(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Formazione"}),(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Metrature"}),(0,i.jsx)(l.nd,{className:"text-center font-semibold text-gray-700 py-3 px-4",children:"Utilizzo"}),(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Stato"}),(0,i.jsx)(l.nd,{className:"text-left font-semibold text-gray-700 py-3 px-4",children:"Ubicazione"}),(0,i.jsx)(l.nd,{className:"text-center font-semibold text-gray-700 py-3 px-4",children:"Azioni"})]})}),(0,i.jsx)(l.BF,{children:C?(0,i.jsx)(l.Hj,{children:(0,i.jsx)(l.nA,{colSpan:9,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(w.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):E?(0,i.jsx)(l.Hj,{children:(0,i.jsx)(l.nA,{colSpan:9,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),E]})})}):0===ef.length?(0,i.jsx)(l.Hj,{children:(0,i.jsx)(l.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):ef.map(e=>{let a=v(e.metri_residui,e.metri_totali),t=e.stato_bobina||b(e.metri_residui,e.metri_totali);return p(t),(0,i.jsxs)(l.Hj,{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150",children:[(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"font-semibold text-gray-900",children:e.numero_bobina||"-"})}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"text-gray-700",children:e.utility||"-"})}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"text-gray-700",children:e.tipologia||"-"})}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"font-medium text-gray-900",children:e.sezione||"-"})}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Residuo:"}),(0,i.jsx)("span",{className:"font-semibold ".concat(e.metri_residui<0?"text-red-600":"text-gray-900"),children:f(e.metri_residui)})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Totale:"}),(0,i.jsx)("span",{className:"font-medium text-gray-700",children:f(e.metri_totali)})]})]})}),(0,i.jsx)(l.nA,{className:"py-4 px-4 text-center",children:(0,i.jsxs)("div",{className:"font-semibold text-gray-900",children:[Math.round(a),"%"]})}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:ev(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsx)("div",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-md",children:e.ubicazione_bobina||"Non specificata"})}),(0,i.jsx)(l.nA,{className:"py-4 px-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)("button",{onClick:()=>em(e),disabled:!h(t),title:t===x.OVER?"Bobina OVER - Non pu\xf2 accettare nuovi cavi":t===x.TERMINATA?"Bobina terminata - Non pu\xf2 accettare nuovi cavi":"Aggiungi cavo a bobina",className:"p-2 rounded-lg transition-colors duration-200 ".concat(h(t)?"text-gray-600 hover:text-blue-600 hover:bg-blue-50":"opacity-50 cursor-not-allowed text-gray-400"),children:(0,i.jsx)(B.A,{className:"h-4 w-4"})}),(0,i.jsx)("button",{onClick:()=>eu(e),title:t===x.OVER?"Modifica bobina (limitata per bobine OVER)":"Modifica bobina",className:"p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200",children:(0,i.jsx)(Y.A,{className:"h-4 w-4"})}),(0,i.jsx)("button",{onClick:()=>ex(e),disabled:t===x.OVER||t!==x.DISPONIBILE,title:t===x.OVER?"Bobina OVER - Non pu\xf2 essere eliminata":t!==x.DISPONIBILE?"Solo bobine disponibili possono essere eliminate":"Elimina bobina",className:"p-2 rounded-lg transition-colors duration-200 ".concat(t===x.OVER||t!==x.DISPONIBILE?"opacity-50 cursor-not-allowed text-gray-400":"text-gray-600 hover:text-red-600 hover:bg-red-50"),children:(0,i.jsx)(I.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})}),en&&(0,i.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,i.jsxs)(o.Fc,{className:"bg-green-50 border-green-200",children:[(0,i.jsx)(P.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)(o.TN,{className:"text-green-800",children:en})]})}),eo&&(0,i.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(z.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:eo})]})}),(0,i.jsx)(S,{open:H,onClose:()=>X(!1),cantiereId:L,onSuccess:eb,onError:eg}),(0,i.jsx)(T,{open:Q,onClose:()=>K(!1),bobina:es,cantiereId:L,onSuccess:e=>{el(e),ed()},onError:e=>{ec(e)}}),(0,i.jsx)(F,{open:ee,onClose:()=>ea(!1),bobina:es,cantiereId:L,onSuccess:e=>{el(e),ed()},onError:e=>{ec(e)}}),(0,i.jsx)(M,{open:et,onClose:()=>ei(!1),bobina:es,cantiereId:L,onSuccess:eb,onError:eg})]})})})}},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>c,yv:()=>d});var i=t(95155);t(12115);var s=t(38715),r=t(66474),n=t(5196),l=t(47863),o=t(59434);function c(e){let{...a}=e;return(0,i.jsx)(s.bL,{"data-slot":"select",...a})}function d(e){let{...a}=e;return(0,i.jsx)(s.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:t="default",children:n,...l}=e;return(0,i.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l,children:[n,(0,i.jsx)(s.In,{asChild:!0,children:(0,i.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:a,children:t,position:r="popper",...n}=e;return(0,i.jsx)(s.ZL,{children:(0,i.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...n,children:[(0,i.jsx)(b,{}),(0,i.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,i.jsx)(g,{})]})})}function x(e){let{className:a,children:t,...r}=e;return(0,i.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...r,children:[(0,i.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,i.jsx)(s.VF,{children:(0,i.jsx)(n.A,{className:"size-4"})})}),(0,i.jsx)(s.p4,{children:t})]})}function b(e){let{className:a,...t}=e;return(0,i.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,i.jsx)(l.A,{className:"size-4"})})}function g(e){let{className:a,...t}=e;return(0,i.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,i.jsx)(r.A,{className:"size-4"})})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>r});var i=t(52596),s=t(39688);function r(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,i.$)(a))}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var i=t(95155),s=t(12115),r=t(59434);let n=s.forwardRef((e,a)=>{let{className:t,type:s,...n}=e;return(0,i.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),ref:a,...n})});n.displayName="Input"},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>r,aR:()=>n});var i=t(95155);t(12115);var s=t(59434);function r(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",a),...t})}function c(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...t})}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>n});var i=t(95155);t(12115);var s=t(40968),r=t(59434);function n(e){let{className:a,...t}=e;return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85127:(e,a,t)=>{"use strict";t.d(a,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>r,nA:()=>d,nd:()=>c});var i=t(95155);t(12115);var s=t(59434);function r(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",a),...t})})}function n(e){let{className:a,...t}=e;return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",a),...t})}function l(e){let{className:a,...t}=e;return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",a),...t})}function o(e){let{className:a,...t}=e;return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("data-[state=selected]:bg-muted border-b",a),...t})}function c(e){let{className:a,...t}=e;return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}function d(e){let{className:a,...t}=e;return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,3464,1909,9384,6955,3947,5731,283,8441,1684,7358],()=>a(12981)),_N_E=e.O()}]);