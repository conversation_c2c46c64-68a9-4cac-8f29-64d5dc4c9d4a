{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: 'owner' | 'user' | 'cantieri_user'\n  requiresUser?: boolean\n  requiresCantiere?: boolean\n  redirectTo?: string\n}\n\nexport default function ProtectedRoute({\n  children,\n  requiredRole,\n  requiresUser = false,\n  requiresCantiere = false,\n  redirectTo = '/login'\n}: ProtectedRouteProps) {\n  const { user, cantiere, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n  const [isChecking, setIsChecking] = useState(true)\n\n  useEffect(() => {\n    const checkAccess = () => {\n      console.log('🛡️ ProtectedRoute: Controllo accesso', {\n        isAuthenticated,\n        user,\n        cantiere,\n        requiredRole,\n        requiresUser,\n        requiresCantiere\n      })\n\n      // Se ancora in caricamento, aspetta\n      if (isLoading) {\n        console.log('⏳ ProtectedRoute: Ancora in caricamento...')\n        return\n      }\n\n      // Se non autenticato, reindirizza al login\n      if (!isAuthenticated) {\n        console.log('❌ ProtectedRoute: Non autenticato, reindirizzamento a', redirectTo)\n        router.replace(redirectTo)\n        return\n      }\n\n      // Se richiede specificamente un utente ma abbiamo solo cantiere\n      if (requiresUser && !user) {\n        console.log('❌ ProtectedRoute: Richiede utente ma abbiamo solo cantiere')\n        router.replace('/login')\n        return\n      }\n\n      // Se richiede specificamente un cantiere ma abbiamo solo utente\n      if (requiresCantiere && !cantiere) {\n        console.log('❌ ProtectedRoute: Richiede cantiere ma abbiamo solo utente')\n        router.replace('/login')\n        return\n      }\n\n      // Se richiede un ruolo specifico\n      if (requiredRole && user) {\n        if (user.ruolo !== requiredRole) {\n          console.log('❌ ProtectedRoute: Ruolo non autorizzato', {\n            required: requiredRole,\n            actual: user.ruolo\n          })\n          \n          // Reindirizza alla pagina appropriata per il ruolo dell'utente\n          switch (user.ruolo) {\n            case 'owner':\n              router.replace('/admin')\n              break\n            case 'user':\n              router.replace('/cantieri')\n              break\n            case 'cantieri_user':\n              router.replace('/cavi')\n              break\n            default:\n              router.replace('/login')\n          }\n          return\n        }\n      }\n\n      console.log('✅ ProtectedRoute: Accesso autorizzato')\n      setIsChecking(false)\n    }\n\n    checkAccess()\n  }, [isAuthenticated, user, cantiere, isLoading, requiredRole, requiresUser, requiresCantiere, router, redirectTo])\n\n  // Mostra loader durante il controllo\n  if (isLoading || isChecking) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-blue-600\" />\n          <p className=\"text-sm text-gray-600\">Verifica autenticazione...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Se tutto ok, mostra il contenuto\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS,eAAe,EACrC,QAAQ,EACR,YAAY,EACZ,eAAe,KAAK,EACpB,mBAAmB,KAAK,EACxB,aAAa,QAAQ,EACD;IACpB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,QAAQ,GAAG,CAAC,yCAAyC;gBACnD;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,oCAAoC;YACpC,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,GAAG,CAAC,yDAAyD;gBACrE,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,gEAAgE;YAChE,IAAI,gBAAgB,CAAC,MAAM;gBACzB,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,gEAAgE;YAChE,IAAI,oBAAoB,CAAC,UAAU;gBACjC,QAAQ,GAAG,CAAC;gBACZ,OAAO,OAAO,CAAC;gBACf;YACF;YAEA,iCAAiC;YACjC,IAAI,gBAAgB,MAAM;gBACxB,IAAI,KAAK,KAAK,KAAK,cAAc;oBAC/B,QAAQ,GAAG,CAAC,2CAA2C;wBACrD,UAAU;wBACV,QAAQ,KAAK,KAAK;oBACpB;oBAEA,+DAA+D;oBAC/D,OAAQ,KAAK,KAAK;wBAChB,KAAK;4BACH,OAAO,OAAO,CAAC;4BACf;wBACF,KAAK;4BACH,OAAO,OAAO,CAAC;4BACf;wBACF,KAAK;4BACH,OAAO,OAAO,CAAC;4BACf;wBACF;4BACE,OAAO,OAAO,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,cAAc;QAChB;QAEA;IACF,GAAG;QAAC;QAAiB;QAAM;QAAU;QAAW;QAAc;QAAc;QAAkB;QAAQ;KAAW;IAEjH,qCAAqC;IACrC,IAAI,aAAa,YAAY;QAC3B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,mCAAmC;IACnC,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/admin/layout.tsx"], "sourcesContent": ["'use client'\n\nimport ProtectedRoute from '@/components/auth/ProtectedRoute'\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <ProtectedRoute \n      requiredRole=\"owner\"\n      requiresUser={true}\n    >\n      {children}\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAc;QACb,cAAa;QACb,cAAc;kBAEb;;;;;;AAGP", "debugId": null}}]}