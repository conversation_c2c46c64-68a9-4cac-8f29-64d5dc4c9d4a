"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{40283:(e,t,o)=>{o.d(t,{A:()=>i,AuthProvider:()=>c});var a=o(95155),n=o(12115),r=o(25731);let l=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:t}=e,[o,i]=(0,n.useState)(null),[c,s]=(0,n.useState)(null),[u,m]=(0,n.useState)(!0),[g,d]=(0,n.useState)(()=>"true"===localStorage.getItem("isImpersonating")),[_,S]=(0,n.useState)(()=>{{let e=localStorage.getItem("impersonatedUser");return e?JSON.parse(e):null}}),[I,C]=(0,n.useState)(null),[v,h]=(0,n.useState)(null),[p,x]=(0,n.useState)(null),A=!!o&&o.id_utente||!!c&&c.id_cantiere;(0,n.useEffect)(()=>{console.log("\uD83D\uDD10 AuthContext: Inizializzazione - controllo autenticazione esistente"),localStorage.getItem("token")?(console.log("\uD83D\uDD10 AuthContext: Token trovato, verifica validit\xe0"),k()):(console.log("\uD83D\uDD10 AuthContext: Nessun token trovato, richiesto login"),m(!1),i(null),s(null),S(null),d(!1))},[]),(0,n.useEffect)(()=>{if(o&&!u&&!c){let e=localStorage.getItem("selectedCantiereId"),t=localStorage.getItem("selectedCantiereName");if(e&&"null"!==e&&"undefined"!==e){let a=parseInt(e,10);!isNaN(a)&&a>0?s({id_cantiere:a,commessa:t||"Cantiere ".concat(a),codice_univoco:"",id_utente:o.id_utente}):(localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"))}}},[o,u,c]);let k=async()=>{try{if(m(!0),localStorage.getItem("token"))try{let e=await r.ZQ.verifyToken(),t={id_utente:e.user_id,username:e.username,ruolo:e.role};i(t);let o=!0===e.is_impersonated;if(d(o),o&&e.impersonated_id){let t={id:e.impersonated_id,username:e.impersonated_username,role:e.impersonated_role};S(t),localStorage.setItem("impersonatedUser",JSON.stringify(t)),localStorage.setItem("isImpersonating","true")}else S(null),localStorage.removeItem("impersonatedUser"),localStorage.removeItem("isImpersonating");if("cantieri_user"===e.role&&e.cantiere_id){let t={id_cantiere:e.cantiere_id,commessa:e.cantiere_name||"Cantiere ".concat(e.cantiere_id),codice_univoco:"",id_utente:e.user_id};console.log("\uD83C\uDFD7️ AuthContext: Impostazione cantiere per utente cantiere:",t),s(t),localStorage.setItem("selectedCantiereId",e.cantiere_id.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}else{console.log("\uD83C\uDFD7️ AuthContext: Utente standard, controllo cantiere dal localStorage");let e=localStorage.getItem("cantiere_data");if(e)try{let t=JSON.parse(e);console.log("\uD83C\uDFD7️ AuthContext: Caricamento cantiere da cantiere_data:",t),s(t),localStorage.setItem("selectedCantiereId",t.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}catch(e){console.warn("\uD83C\uDFD7️ AuthContext: Errore parsing cantiere_data:",e),localStorage.removeItem("cantiere_data")}}}catch(e){console.error("\uD83D\uDD10 AuthContext: Token non valido:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("isImpersonating"),localStorage.removeItem("impersonatedUser"),i(null),s(null),d(!1),S(null)}else console.log("\uD83D\uDD10 AuthContext: Nessun token trovato"),i(null),s(null)}catch(e){console.error("\uD83D\uDD10 AuthContext: Errore generale durante checkAuth:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.removeItem("isImpersonating"),localStorage.removeItem("impersonatedUser"),i(null),s(null),d(!1),S(null)}finally{console.log("\uD83D\uDD10 AuthContext: checkAuth completato, impostazione loading = false"),m(!1)}},f=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login per:",e),m(!0);let o=await r.ZQ.login({username:e,password:t});console.log("\uD83D\uDCE1 AuthContext: Risposta backend ricevuta:",o);{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.setItem("token",o.access_token),localStorage.setItem("access_token",o.access_token),console.log("\uD83D\uDCBE AuthContext: Token salvato nel localStorage");let e={id_utente:o.user_id,username:o.username,ruolo:o.role};return localStorage.setItem("user_data",JSON.stringify(e)),o.expiration_warning?(console.log("⚠️ AuthContext: Warning scadenza ricevuto:",o.expiration_warning),C(o.expiration_warning),h(o.days_until_expiration),x(o.expiration_date)):(C(null),h(null),x(null)),console.log("\uD83D\uDC64 AuthContext: Dati utente creati:",e),i(e),s(null),console.log("✅ AuthContext: Stato utente aggiornato"),{success:!0,user:e}}}catch(e){var o,a;return console.error("❌ AuthContext: Errore durante login:",e),{success:!1,error:(null==(a=e.response)||null==(o=a.data)?void 0:o.detail)||e.message||"Errore durante il login"}}finally{m(!1)}},y=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login cantiere:",e),m(!0);let o=await r.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});console.log("\uD83D\uDD10 AuthContext: Risposta login cantiere:",o);{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.setItem("token",o.access_token),localStorage.setItem("access_token",o.access_token),console.log("\uD83D\uDD10 AuthContext: Token salvato");let t={id_cantiere:o.cantiere_id,commessa:o.cantiere_name,codice_univoco:e,id_utente:o.user_id};return console.log("\uD83D\uDD10 AuthContext: Dati cantiere preparati:",t),localStorage.setItem("cantiere_data",JSON.stringify(t)),console.log("\uD83D\uDD10 AuthContext: Dati cantiere salvati in localStorage"),s(t),i(null),console.log("\uD83D\uDD10 AuthContext: Context aggiornato"),await k(),console.log("\uD83D\uDD10 AuthContext: checkAuth completato"),{success:!0,cantiere:t}}}catch(e){var o,a;return console.error("❌ AuthContext: Errore durante login cantiere:",e),{success:!1,error:(null==(a=e.response)||null==(o=a.data)?void 0:o.detail)||e.message||"Errore durante il login cantiere"}}finally{m(!1)}},N=async e=>{try{let t=await r.dG.impersonateUser(e);{localStorage.setItem("token",t.access_token);let e={id:t.impersonated_id,username:t.impersonated_username,role:t.impersonated_role};return localStorage.setItem("impersonatedUser",JSON.stringify(e)),S(e),d(!0),localStorage.setItem("isImpersonating","true"),{impersonatedUser:e}}}catch(e){throw e}};return(0,a.jsx)(l.Provider,{value:{user:o,cantiere:c,isAuthenticated:A,isLoading:u,isImpersonating:g,impersonatedUser:_,expirationWarning:I,daysUntilExpiration:v,expirationDate:p,login:f,loginCantiere:y,logout:()=>{console.log("\uD83D\uDEAA AuthContext: Inizio logout"),["token","access_token","user_data","cantiere_data","selectedCantiereId","selectedCantiereName","isImpersonating","impersonatedUser"].forEach(e=>{localStorage.removeItem(e)}),sessionStorage.clear(),i(null),s(null),d(!1),S(null),C(null),h(null),x(null),console.log("\uD83D\uDEAA AuthContext: Logout completato, reindirizzamento a /login"),window.location.replace("/login")},checkAuth:k,impersonateUser:N,selectCantiere:e=>{if(!e||!e.id_cantiere||e.id_cantiere<=0)return void console.error("\uD83C\uDFD7️ AuthContext: Tentativo di selezione cantiere non valido:",e);try{let t=e.commessa||"Cantiere ".concat(e.id_cantiere);localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t);let o={...e,commessa:t};console.log("\uD83C\uDFD7️ AuthContext: Cantiere selezionato:",o),s(o),localStorage.removeItem("cantiere_data")}catch(e){console.error("\uD83C\uDFD7️ AuthContext: Errore nella selezione cantiere:",e)}},clearCantiere:()=>{console.log("\uD83C\uDFD7️ AuthContext: Pulizia stato cantiere"),s(null),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data")},dismissExpirationWarning:()=>{C(null),h(null),x(null)}},children:t})}}}]);