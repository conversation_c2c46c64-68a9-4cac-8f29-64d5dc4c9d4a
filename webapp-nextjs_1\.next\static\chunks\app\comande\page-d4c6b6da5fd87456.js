(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7484],{24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>l});var t=s(95155);s(12115);var i=s(55863),n=s(59434);function l(e){let{className:a,value:s,...l}=e;return(0,t.jsx)(i.bL,{"data-slot":"progress",className:(0,n.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...l,children:(0,t.jsx)(i.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>r});var t=s(95155);s(12115);var i=s(76981),n=s(5196),l=s(59434);function r(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(n.A,{className:"size-3.5"})})})}},56834:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Q});var t=s(95155),i=s(12115),n=s(66695),l=s(30285),r=s(26126),o=s(62523),c=s(63743),d=s(85127),m=s(40283),u=s(25731),h=s(54165),x=s(85057),p=s(88539),v=s(59409),g=s(55365),j=s(25273),b=s(85339),N=s(51154),f=s(71007);let A={POSA:"POSA",COLLEGAMENTO_PARTENZA:"COLLEGAMENTO_PARTENZA",COLLEGAMENTO_ARRIVO:"COLLEGAMENTO_ARRIVO",CERTIFICAZIONE:"CERTIFICAZIONE"},_={INSTALLATO:"Installato"};function C(e,a,s){let t={isValid:!0,errors:[],warnings:[],info:[],caviValidi:[],caviProblematici:[]};return e&&0!==e.length?s&&""!==s.trim()?(e.forEach(e=>{let i=function(e,a,s){let t={id_cavo:e.id_cavo,isValid:!0,errors:[],warnings:[],info:[]},i=function(e,a){let s={errors:[],warnings:[],info:[]},t=e.stato_installazione===_.INSTALLATO,i=e.metratura_reale&&parseFloat(e.metratura_reale)>0,n=e.collegamenti&&parseInt(e.collegamenti)>0,l="CERTIFICATO"===e.stato_certificazione;switch(a){case A.POSA:t&&s.errors.push("Cavo ".concat(e.id_cavo," \xe8 gi\xe0 installato e non pu\xf2 essere assegnato a comanda POSA")),i&&s.warnings.push("Cavo ".concat(e.id_cavo," ha gi\xe0 metratura reale registrata"));break;case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:t||i||s.warnings.push("Cavo ".concat(e.id_cavo," non risulta installato. Verificare prerequisiti.")),n&&s.warnings.push("Cavo ".concat(e.id_cavo," risulta gi\xe0 collegato"));break;case A.CERTIFICAZIONE:t||s.errors.push("Cavo ".concat(e.id_cavo," deve essere installato per la certificazione")),n||s.warnings.push("Cavo ".concat(e.id_cavo," non risulta collegato. Verificare prerequisiti.")),l&&s.warnings.push("Cavo ".concat(e.id_cavo," \xe8 gi\xe0 certificato"))}return s}(e,a);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info);let n=function(e,a){let s={errors:[],warnings:[],info:[]};switch(a){case A.POSA:e.comanda_posa&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda POSA assegnata: ").concat(e.comanda_posa));break;case A.COLLEGAMENTO_PARTENZA:e.comanda_partenza&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda COLLEGAMENTO_PARTENZA assegnata: ").concat(e.comanda_partenza));break;case A.COLLEGAMENTO_ARRIVO:e.comanda_arrivo&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda COLLEGAMENTO_ARRIVO assegnata: ").concat(e.comanda_arrivo));break;case A.CERTIFICAZIONE:e.comanda_certificazione&&s.errors.push("Cavo ".concat(e.id_cavo," ha gi\xe0 comanda CERTIFICAZIONE assegnata: ").concat(e.comanda_certificazione))}return s}(e,a);t.errors.push(...n.errors),t.warnings.push(...n.warnings),t.info.push(...n.info);let l=function(e,a){let s={warnings:[],info:[]};switch(a){case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:!e.comanda_posa&&(!e.metratura_reale||0>=parseFloat(e.metratura_reale))&&s.warnings.push("Cavo ".concat(e.id_cavo," non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti."));break;case A.CERTIFICAZIONE:e.comanda_partenza||e.comanda_arrivo||s.warnings.push("Cavo ".concat(e.id_cavo," non ha comande di collegamento assegnate. Verificare prerequisiti."))}return s}(e,a);t.warnings.push(...l.warnings),t.info.push(...l.info);let r=function(e,a,s){let t={warnings:[],info:[]},i=[...new Set(Object.values({posa:e.responsabile_posa||"",partenza:e.responsabile_partenza||"",arrivo:e.responsabile_arrivo||"",certificazione:e.responsabile_certificazione||""}).filter(e=>e&&""!==e.trim()))];return i.length>1&&!i.includes(s)&&t.warnings.push("Cavo ".concat(e.id_cavo," ha gi\xe0 responsabili diversi (").concat(i.join(", "),"). Nuovo responsabile: ").concat(s)),t}(e,0,s);return t.warnings.push(...r.warnings),t.info.push(...r.info),t.isValid=0===t.errors.length,t}(e,a,s);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info),i.isValid?t.caviValidi.push(e):t.caviProblematici.push({cavo:e,issues:i.errors})}),t.isValid=0===t.errors.length):(t.errors.push("Responsabile non specificato"),t.isValid=!1):(t.errors.push("Nessun cavo selezionato per la comanda"),t.isValid=!1),t}function E(e){let a=[];return e.errors.length>0&&(a.push("❌ Errori (".concat(e.errors.length,"):")),e.errors.forEach(e=>a.push("  • ".concat(e)))),e.warnings.length>0&&(a.push("⚠️ Avvisi (".concat(e.warnings.length,"):")),e.warnings.forEach(e=>a.push("  • ".concat(e)))),e.info.length>0&&(a.push("ℹ️ Informazioni (".concat(e.info.length,"):")),e.info.forEach(e=>a.push("  • ".concat(e)))),e.caviValidi.length>0&&a.push("✅ Cavi validi: ".concat(e.caviValidi.length)),e.caviProblematici.length>0&&a.push("❌ Cavi problematici: ".concat(e.caviProblematici.length)),a.join("\n")}function S(e){let{open:a,onClose:s,caviSelezionati:n=[],tipoComanda:r,onSuccess:c,onError:d,onComandaCreated:A}=e,[_,S]=(0,i.useState)(!1),[y,O]=(0,i.useState)(""),[T,w]=(0,i.useState)([]),[R,I]=(0,i.useState)(!1),[z,L]=(0,i.useState)(""),[P,k]=(0,i.useState)(!1),{cantiere:V}=(0,m.A)(),[M,F]=(0,i.useState)(0);(0,i.useEffect)(()=>{F((null==V?void 0:V.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[V]);let[U,G]=(0,i.useState)({tipo_comanda:r||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1});(0,i.useEffect)(()=>{a&&M>0&&D()},[a,M]),(0,i.useEffect)(()=>{a||(G({tipo_comanda:r||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1}),O(""))},[a,r]);let D=async()=>{try{I(!0);let e=await u.AR.getResponsabili(M),a=(null==e?void 0:e.data)||e||[];w(Array.isArray(a)?a:[])}catch(e){w([])}finally{I(!1)}},Z=async()=>{var e,a,t;try{let a;if(S(!0),O(""),!U.tipo_comanda)return void O("Seleziona il tipo di comanda");if(!U.responsabile)return void O("Seleziona un responsabile");if(!M||M<=0)return void O("Cantiere non selezionato");if(n.length>0){let e=C(n,U.tipo_comanda,U.responsabile);if(!e.isValid){O("Validazione cavi fallita. Controllare i dettagli nella sezione validazione."),L(E(e)),k(!0);return}e.warnings.length>0&&(L(E(e)),k(!0))}let t={tipo_comanda:U.tipo_comanda,responsabile:U.responsabile,descrizione:U.descrizione||null,data_scadenza:U.data_scadenza||null,numero_componenti_squadra:U.numero_componenti_squadra},i=(null==(a=n&&n.length>0?await u.CV.createComandaWithCavi(M,t,n):await u.CV.createComanda(M,t))||null==(e=a.data)?void 0:e.codice_comanda)||(null==a?void 0:a.codice_comanda),l=n&&n.length>0?"Comanda ".concat(i," creata con successo per ").concat(n.length," cavi"):"Comanda ".concat(i," creata con successo");c(l),null==A||A(),s()}catch(e){d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante la creazione della comanda")}finally{S(!1)}};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsx)(h.rr,{children:n&&n.length>0?"Crea una comanda per ".concat(n.length," cavi selezionati"):"Crea una nuova comanda di lavoro"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[y&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:y})]}),n.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"text-sm font-medium",children:["Validazione Cavi (",n.length," selezionati)"]}),(0,t.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(0===n.length){L("Nessun cavo selezionato per la validazione"),k(!0);return}L(E(C(n,U.tipo_comanda,U.responsabile))),k(!0)},disabled:!U.tipo_comanda||!U.responsabile,children:"Valida Cavi"})]}),P&&z&&(0,t.jsxs)(g.Fc,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap text-xs font-mono",children:z})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(v.l6,{value:U.tipo_comanda,onValueChange:e=>G(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsx)(v.gC,{children:[{value:"POSA",label:"\uD83D\uDD27 Posa Cavi",description:"Installazione e posa dei cavi"},{value:"COLLEGAMENTO_PARTENZA",label:"\uD83D\uDD0C Collegamento Partenza",description:"Collegamento lato partenza"},{value:"COLLEGAMENTO_ARRIVO",label:"⚡ Collegamento Arrivo",description:"Collegamento lato arrivo"},{value:"CERTIFICAZIONE",label:"\uD83D\uDCCB Certificazione",description:"Test e certificazione"}].map(e=>(0,t.jsx)(v.eb,{value:e.value,children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.label}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.description})]})},e.value))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"responsabile",children:"Responsabile *"}),R?(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 text-sm text-slate-500",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]}):(0,t.jsxs)(v.l6,{value:U.responsabile,onValueChange:e=>G(a=>({...a,responsabile:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile"})}),(0,t.jsx)(v.gC,{children:T.map(e=>(0,t.jsx)(v.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.numero_telefono})]})]})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"descrizione",children:"Descrizione"}),(0,t.jsx)(p.T,{id:"descrizione",placeholder:"Descrizione opzionale della comanda...",value:U.descrizione,onChange:e=>G(a=>({...a,descrizione:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,t.jsx)(o.p,{id:"data_scadenza",type:"date",value:U.data_scadenza,onChange:e=>G(a=>({...a,data_scadenza:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"squadra",children:"Componenti Squadra"}),(0,t.jsx)(o.p,{id:"squadra",type:"number",min:"1",max:"20",value:U.numero_componenti_squadra,onChange:e=>G(a=>({...a,numero_componenti_squadra:parseInt(e.target.value)||1}))})]})]}),n&&n.length>0&&(0,t.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"font-medium",children:["Cavi da assegnare: ",n.length]})]}),(0,t.jsx)("div",{className:"text-sm text-blue-600 mt-1",children:"I cavi selezionati verranno automaticamente assegnati a questa comanda"})]})]}),(0,t.jsxs)(h.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,disabled:_,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:Z,disabled:_,children:[_&&(0,t.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Crea Comanda"]})]})]})})}var y=s(17580),O=s(84616),T=s(54416),w=s(4229),R=s(19420),I=s(28883),z=s(13717),L=s(62525);function P(e){let{open:a,onClose:s,onSuccess:r,onError:c}=e,[d,p]=(0,i.useState)(!1),[v,j]=(0,i.useState)(""),[A,_]=(0,i.useState)([]),[C,E]=(0,i.useState)(null),[S,P]=(0,i.useState)(!1),{cantiere:k}=(0,m.A)(),[V,M]=(0,i.useState)(0);(0,i.useEffect)(()=>{M((null==k?void 0:k.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[k]);let[F,U]=(0,i.useState)({nome_responsabile:"",numero_telefono:"",mail:""});(0,i.useEffect)(()=>{a&&V>0&&G()},[a,V]),(0,i.useEffect)(()=>{a||(U({nome_responsabile:"",numero_telefono:"",mail:""}),j(""),E(null),P(!1))},[a]);let G=async()=>{try{p(!0);let e=await u.AR.getResponsabili(V),a=(null==e?void 0:e.data)||e||[];_(Array.isArray(a)?a:[])}catch(e){j("Errore durante il caricamento dei responsabili")}finally{p(!1)}},D=async()=>{try{p(!0),j("");let e={nome_responsabile:F.nome_responsabile.trim(),numero_telefono:F.numero_telefono.trim()||null,mail:F.mail.trim()||null},a=function(e){var a,s;let t=[];return e.nome_responsabile&&e.nome_responsabile.trim()||t.push("Il nome del responsabile \xe8 obbligatorio"),e.mail||e.numero_telefono||t.push("Almeno uno tra email e telefono deve essere specificato"),e.mail&&(a=e.mail,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))&&t.push("Formato email non valido"),e.numero_telefono&&(s=e.numero_telefono,!/^[\+]?[0-9\s\-\(\)]{8,15}$/.test(s.replace(/\s/g,"")))&&t.push("Formato telefono non valido"),{isValid:0===t.length,errors:t}}(e);if(!a.isValid)return void j("Errori di validazione: ".concat(a.errors.join(", ")));await u.AR.createResponsabile(V,e),r("Responsabile aggiunto con successo"),U({nome_responsabile:"",numero_telefono:"",mail:""}),P(!1),G()}catch(s){var e,a;j((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante la creazione del responsabile")}finally{p(!1)}},Z=e=>{U({nome_responsabile:e.nome_responsabile,numero_telefono:e.numero_telefono||"",mail:e.mail||""}),E(e.id_responsabile),P(!1)},B=async()=>{if(C)try{if(p(!0),j(""),!F.nome_responsabile.trim())return void j("Il nome del responsabile \xe8 obbligatorio");if(F.mail&&!F.mail.includes("@"))return void j("Inserisci un indirizzo email valido");let e={nome_responsabile:F.nome_responsabile.trim(),numero_telefono:F.numero_telefono.trim()||null,mail:F.mail.trim()||null};await u.AR.updateResponsabile(V,C,e),r("Responsabile aggiornato con successo"),U({nome_responsabile:"",numero_telefono:"",mail:""}),E(null),G()}catch(s){var e,a;j((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante l'aggiornamento del responsabile")}finally{p(!1)}},$=async(e,a)=>{if(confirm('Sei sicuro di voler eliminare il responsabile "'.concat(a,'"?')))try{p(!0),await u.AR.deleteResponsabile(V,e),r("Responsabile eliminato con successo"),G()}catch(e){var s,t;c((null==(t=e.response)||null==(s=t.data)?void 0:s.detail)||"Errore durante l'eliminazione del responsabile")}finally{p(!1)}},q=()=>{E(null),P(!1),U({nome_responsabile:"",numero_telefono:"",mail:""}),j("")};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"sm:max-w-[700px] max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5"}),"Gestisci Responsabili"]}),(0,t.jsxs)(h.rr,{children:["Gestisci i responsabili per il cantiere ",localStorage.getItem("selectedCantiereName")||V]})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[v&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:v})]}),!S&&!C&&(0,t.jsxs)(l.$,{onClick:()=>P(!0),className:"w-full",variant:"outline",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Aggiungi Nuovo Responsabile"]}),(S||C)&&(0,t.jsx)(n.Zp,{className:"border-2 border-blue-200",children:(0,t.jsxs)(n.Wu,{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:C?"Modifica Responsabile":"Nuovo Responsabile"}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:q,children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"nome",children:"Nome Responsabile *"}),(0,t.jsx)(o.p,{id:"nome",placeholder:"Nome e cognome",value:F.nome_responsabile,onChange:e=>U(a=>({...a,nome_responsabile:e.target.value}))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"telefono",children:"Numero Telefono"}),(0,t.jsx)(o.p,{id:"telefono",placeholder:"+39 ************",value:F.numero_telefono,onChange:e=>U(a=>({...a,numero_telefono:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:F.mail,onChange:e=>U(a=>({...a,mail:e.target.value}))})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,t.jsxs)(l.$,{onClick:C?B:D,disabled:d,className:"flex-1",children:[d&&(0,t.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4"}),C?"Aggiorna":"Aggiungi"]}),(0,t.jsx)(l.$,{variant:"outline",onClick:q,children:"Annulla"})]})]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),"Responsabili Esistenti (",A.length,")"]}),d&&0===A.length?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]})}):0===A.length?(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun responsabile trovato"}):(0,t.jsx)("div",{className:"space-y-2",children:A.map(e=>(0,t.jsx)(n.Zp,{className:C===e.id_responsabile?"border-blue-300":"",children:(0,t.jsx)(n.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.nome_responsabile})]}),(0,t.jsxs)("div",{className:"space-y-1 text-sm text-slate-600",children:[e.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),e.numero_telefono]}),e.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),e.mail]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>Z(e),disabled:d||C===e.id_responsabile,children:(0,t.jsx)(z.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>$(e.id_responsabile,e.nome_responsabile),disabled:d,className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]})})},e.id_responsabile))})]})]}),(0,t.jsx)(h.Es,{children:(0,t.jsx)(l.$,{variant:"outline",onClick:s,children:"Chiudi"})})]})})}var k=s(24944),V=s(79397),M=s(69074),F=s(14186),U=s(40646),G=s(3493);function D(e){let{open:a,onClose:s,codiceComanda:o,onSuccess:d,onError:x}=e,[p,v]=(0,i.useState)(!1),[A,_]=(0,i.useState)(""),[C,E]=(0,i.useState)(null),{cantiere:S}=(0,m.A)(),[O,T]=(0,i.useState)(0);(0,i.useEffect)(()=>{T((null==S?void 0:S.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[S]),(0,i.useEffect)(()=>{a&&o&&O>0&&w()},[a,o,O]),(0,i.useEffect)(()=>{a||(E(null),_(""))},[a]);let w=async()=>{if(o)try{v(!0),_("");let e=await u.CV.getComanda(O,o),a=(null==e?void 0:e.data)||e;E(a)}catch(e){_("Errore durante il caricamento dei dettagli della comanda")}finally{v(!1)}},z=e=>new Date(e).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return o?(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),"Dettagli Comanda ",o]}),(0,t.jsx)(h.rr,{children:"Visualizza tutti i dettagli e lo stato della comanda"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[A&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:A})]}),p?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5 animate-spin"}),"Caricamento dettagli comanda..."]})}):C?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Informazioni Generali"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(e=>{let a={POSA:{label:"Posa Cavi",icon:"\uD83D\uDD27"},COLLEGAMENTO_PARTENZA:{label:"Collegamento Partenza",icon:"\uD83D\uDD0C"},COLLEGAMENTO_ARRIVO:{label:"Collegamento Arrivo",icon:"⚡"},CERTIFICAZIONE:{label:"Certificazione",icon:"\uD83D\uDCCB"}}[e]||{label:e,icon:"❓"};return(0,t.jsxs)(r.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200",children:[a.icon," ",a.label]})})(C.tipo_comanda),(e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(r.E,{className:a.badge,children:e})})(C.stato)]})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Data Creazione"}),(0,t.jsx)("p",{className:"font-medium",children:z(C.data_creazione)})]})]}),C.data_scadenza&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Scadenza"}),(0,t.jsx)("p",{className:"font-medium",children:z(C.data_scadenza)})]})]}),C.data_completamento&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Completamento"}),(0,t.jsx)("p",{className:"font-medium text-green-700",children:z(C.data_completamento)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Componenti Squadra"}),(0,t.jsxs)("p",{className:"font-medium",children:[C.numero_componenti_squadra," persone"]})]})]}),C.descrizione&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Descrizione"}),(0,t.jsx)("p",{className:"font-medium",children:C.descrizione})]})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Responsabile"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"flex items-start gap-4",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-lg",children:C.responsabile||"Non assegnato"}),C.responsabile_dettagli&&(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-slate-600",children:[C.responsabile_dettagli.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),C.responsabile_dettagli.numero_telefono]}),C.responsabile_dettagli.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),C.responsabile_dettagli.mail]})]})]})})})]}),C.progresso&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Progresso Lavori"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[C.progresso.percentuale,"%"]})]}),(0,t.jsx)(k.k,{value:C.progresso.percentuale,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-slate-600",children:[(0,t.jsxs)("span",{children:[C.progresso.completati," completati"]}),(0,t.jsxs)("span",{children:[C.progresso.totale," totali"]})]})]})})]}),C.cavi_assegnati&&C.cavi_assegnati.length>0&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"h-5 w-5"}),"Cavi Assegnati (",C.cavi_assegnati.length,")"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:C.cavi_assegnati.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-slate-50 rounded",children:[(0,t.jsx)("span",{className:"font-mono text-sm",children:e.id_cavo||e}),e.stato&&(0,t.jsx)(r.E,{variant:"outline",className:"text-xs",children:e.stato})]},a))})})]})]}):(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun dettaglio disponibile"})]}),(0,t.jsxs)(h.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,children:"Chiudi"}),C&&(0,t.jsx)(l.$,{onClick:()=>{d("Funzione di modifica in sviluppo")},children:"Modifica Comanda"})]})]})}):null}var Z=s(1243),B=s(87481);function $(e){let{open:a,onClose:s,codiceComanda:n,tipoComanda:c,onSuccess:d,onError:m}=e,[p,v]=(0,i.useState)([]),[g,j]=(0,i.useState)({}),[b,f]=(0,i.useState)(!1),[A,_]=(0,i.useState)(!1),[C,E]=(0,i.useState)(null),{toast:S}=(0,B.dj)();(0,i.useEffect)(()=>{a&&n&&y()},[a,n]);let y=async()=>{var e,a,s;try{if(f(!0),E(null),!localStorage.getItem("selectedCantiereId"))throw Error("Nessun cantiere selezionato");let a=await u.CV.getCaviComanda(n);v(a.data.cavi||[]);let s={};null==(e=a.data.cavi)||e.forEach(e=>{s[e.id_cavo]={metratura_reale:e.metratura_reale||0,numero_persone_impiegate:1,sistemazione:"",fascettatura:""}}),j(s)}catch(t){let e=(null==(s=t.response)||null==(a=s.data)?void 0:a.detail)||t.message||"Errore nel caricamento dei cavi";E(e),null==m||m(e)}finally{f(!1)}},O=(e,a,s)=>{j(t=>({...t,[e]:{...t[e],[a]:s}}))},T=async()=>{try{_(!0),E(null);let e="",a={};"POSA"===c?(e="dati-posa",a={dati_posa:g}):("COLLEGAMENTO_PARTENZA"===c||"COLLEGAMENTO_ARRIVO"===c)&&(e="dati-collegamento",a={dati_collegamento:g}),await u.CV.updateDatiComanda(n,e,a);let t="POSA"===c?"Metri posati inseriti con successo":"Metri collegati inseriti con successo";null==d||d(t),S({title:"Successo",description:t}),s()}catch(t){var e,a;let s=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore nel salvataggio";E(s),null==m||m(s),S({title:"Errore",description:s,variant:"destructive"})}finally{_(!1)}};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-5 w-5 text-blue-600"}),(()=>{switch(c){case"POSA":return"Inserisci Metri Posati";case"COLLEGAMENTO_PARTENZA":return"Inserisci Metri Collegati - Partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci Metri Collegati - Arrivo";default:return"Inserisci Metri"}})()]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(()=>{switch(c){case"POSA":return"Inserisci i metri realmente posati per ogni cavo";case"COLLEGAMENTO_PARTENZA":return"Inserisci i metri collegati lato partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci i metri collegati lato arrivo";default:return"Inserisci i metri"}})()," - Comanda: ",n]})]}),b?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(N.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):C?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8 text-red-600",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 mr-2"}),C]}):0===p.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nessun cavo trovato per questa comanda"}):(0,t.jsx)("div",{className:"space-y-4",children:p.map(e=>{var a,s,i,n;return(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-600",children:e.id_cavo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsx)(r.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"metri-".concat(e.id_cavo),children:"POSA"===c?"Metri Posati":"Metri Collegati"}),(0,t.jsx)(o.p,{id:"metri-".concat(e.id_cavo),type:"number",min:"0",step:"0.1",value:(null==(a=g[e.id_cavo])?void 0:a.metratura_reale)||0,onChange:a=>O(e.id_cavo,"metratura_reale",parseFloat(a.target.value)||0),className:"mt-1"})]}),"POSA"===c&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"persone-".concat(e.id_cavo),children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:"persone-".concat(e.id_cavo),type:"number",min:"1",value:(null==(s=g[e.id_cavo])?void 0:s.numero_persone_impiegate)||1,onChange:a=>O(e.id_cavo,"numero_persone_impiegate",parseInt(a.target.value)||1),className:"mt-1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"sistemazione-".concat(e.id_cavo),children:"Sistemazione"}),(0,t.jsx)(o.p,{id:"sistemazione-".concat(e.id_cavo),value:(null==(i=g[e.id_cavo])?void 0:i.sistemazione)||"",onChange:a=>O(e.id_cavo,"sistemazione",a.target.value),className:"mt-1",placeholder:"Es: Interrato, Aereo..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"fascettatura-".concat(e.id_cavo),children:"Fascettatura"}),(0,t.jsx)(o.p,{id:"fascettatura-".concat(e.id_cavo),value:(null==(n=g[e.id_cavo])?void 0:n.fascettatura)||"",onChange:a=>O(e.id_cavo,"fascettatura",a.target.value),className:"mt-1",placeholder:"Es: Standard, Rinforzata..."})]})]})]})]},e.id_cavo)})}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,disabled:A,children:"Annulla"}),(0,t.jsx)(l.$,{onClick:T,disabled:A||0===p.length,className:"bg-blue-600 hover:bg-blue-700",children:A?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):"Salva Metri"})]})]})})}var q=s(47262),J=s(48313);function W(e){let{open:a,onClose:s,codiceComanda:n,onSuccess:c,onError:d}=e,[p,j]=(0,i.useState)([]),[f,A]=(0,i.useState)([]),[_,C]=(0,i.useState)({}),[E,S]=(0,i.useState)({}),[y,O]=(0,i.useState)(!1),[T,w]=(0,i.useState)(!1),[R,I]=(0,i.useState)(null),[z,L]=(0,i.useState)(""),[P,k]=(0,i.useState)(0),[V,M]=(0,i.useState)([]),[F,G]=(0,i.useState)(null),{toast:D}=(0,B.dj)(),{cantiere:Z}=(0,m.A)();(0,i.useEffect)(()=>{a&&n&&W()},[a,n]),(0,i.useEffect)(()=>{a&&(null==Z?void 0:Z.id_cantiere)&&H()},[a,null==Z?void 0:Z.id_cantiere]),(0,i.useEffect)(()=>{a||$()},[a]);let $=()=>{j([]),A([]),C({}),S({}),I(null),L(""),k(0),M([]),G(null)},W=async()=>{try{O(!0),I(null);let e=await u.CV.getCaviComanda(n),a=((null==e?void 0:e.data)||e||[]).filter(e=>"Installato"!==e.stato_installazione);j(a);let s={};a.forEach(e=>{s[e.id_cavo]={metratura_reale:0,numero_persone_impiegate:1,sistemazione:!1,fascettatura:!1,id_bobina:"BOBINA_VUOTA"}}),C(s)}catch(e){I("Errore durante il caricamento dei cavi della comanda")}finally{O(!1)}},H=async()=>{try{if(!(null==Z?void 0:Z.id_cantiere))return;let e=await u.Fw.getBobine(Z.id_cantiere),a=((null==e?void 0:e.data)||e||[]).filter(e=>"disponibile"===e.stato&&e.metri_residui>0);A(a)}catch(e){I("Errore durante il caricamento delle bobine")}},X=(e,a)=>{let s=parseFloat(a)||0;C(a=>{let t={...a,[e]:{...a[e],metratura_reale:s}};if(z&&"BOBINA_VUOTA"!==z){let a=f.find(e=>e.id_bobina===z);if(a){let s=0;Object.keys(t).forEach(e=>{var a;let i=(null==(a=t[e])?void 0:a.metratura_reale)||0;i>0&&(s+=i)});let i=a.metri_residui-s;if(k(i),i<0&&!F){G(e);let a=[];Object.keys(t).forEach(s=>{var i;0===((null==(i=t[s])?void 0:i.metratura_reale)||0)&&s!==e&&a.push(s)}),M(a)}else i>=0&&F===e&&(G(null),M([]))}}return t}),Q(e,s)},Q=(e,a)=>{let s=p.find(a=>a.id_cavo===e);s&&S(t=>{let i={...t};return delete i[e],a>0&&s.metratura_teorica,i})},Y=(e,a)=>{let s=parseInt(a)||1;C(a=>({...a,[e]:{...a[e],numero_persone_impiegate:s}}))},K=(e,a)=>{C(s=>({...s,[e]:{...s[e],sistemazione:a}}))},ee=(e,a)=>{C(s=>({...s,[e]:{...s[e],fascettatura:a}}))},ea=async()=>{try{if(w(!0),I(null),Object.keys(E).length>0)return void I("Correggere gli errori di validazione prima di salvare");let e={};if(Object.keys(_).forEach(a=>{let s=_[a];if(((null==s?void 0:s.metratura_reale)||0)>0){let t=F===a||P<0;e[a]={...s,id_bobina:z||"BOBINA_VUOTA",force_over:t}}}),0===Object.keys(e).length)return void I("Inserire almeno un metro per almeno un cavo");console.log("\uD83D\uDCBE InserisciMetriPosatiDialog: Salvataggio dati posa:",{codiceComanda:n,caviDaSalvare:Object.keys(e).length,datiPosaFiltrati:e,selectedBobina:z,metriResiduiSimulati:P,cavoCheCausaOver:F});let a=await fetch("/api/comande/".concat(n,"/dati-posa-bobine"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(e)});if(!a.ok){let e=await a.json();throw Error(e.detail||"Errore durante il salvataggio")}let t="Metri posati inseriti con successo per ".concat(Object.keys(e).length," cavi");null==c||c(t),D({title:"Successo",description:t}),s()}catch(t){var e,a;let s=(null==t||null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il salvataggio dei metri posati";I(s),null==d||d(s)}finally{w(!1)}};return(0,t.jsx)(h.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(h.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(J.A,{className:"h-5 w-5 text-blue-600"}),"Inserisci Metri Posati - Comanda ",n]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci i metri posati per ogni cavo della comanda POSA"})]}),R&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:R})]}),y?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(N.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-3",children:"Selezione Bobina Principale"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"bobina-principale",children:"Bobina da Utilizzare"}),(0,t.jsxs)(v.l6,{value:z,onValueChange:e=>{if(L(e),M([]),G(null),C(a=>{let s={...a};return Object.keys(s).forEach(a=>{s[a]={...s[a],id_bobina:e}}),s}),e&&"BOBINA_VUOTA"!==e){let a=f.find(a=>a.id_bobina===e);a&&k(a.metri_residui)}else k(0)},children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona bobina principale..."})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"BOBINA_VUOTA",children:"\uD83D\uDD04 BOBINA_VUOTA (Assegna dopo)"}),f.map(e=>(0,t.jsxs)(v.eb,{value:e.id_bobina,children:["✅ ",e.id_bobina," - ",e.tipologia," ",e.sezione," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]}),z&&"BOBINA_VUOTA"!==z&&(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Metri Residui: "}),(0,t.jsxs)("span",{className:P<0?"text-red-600 font-bold":"text-green-600",children:[P.toFixed(1),"m"]})]}),P<0&&(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"font-medium",children:["Cavi da Installare (",p.length,")"]}),p.map(e=>{let a=_[e.id_cavo],s=E[e.id_cavo],i=V.includes(e.id_cavo),n=F===e.id_cavo,l=P<0&&"BOBINA_VUOTA"!==z;return(0,t.jsxs)("div",{className:"border rounded-lg p-4 space-y-4 ".concat(i?"bg-red-50 border-red-200":n?"bg-orange-50 border-orange-200":""),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[e.id_cavo,i&&(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"BLOCCATO"}),n&&(0,t.jsx)(r.E,{variant:"outline",className:"text-xs border-orange-500 text-orange-700",children:"CAUSA OVER"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione}),l&&(0,t.jsx)(r.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"metri-".concat(e.id_cavo),children:"Metri Posati *"}),(0,t.jsx)(o.p,{id:"metri-".concat(e.id_cavo),type:"number",min:"0",step:"0.1",value:(null==a?void 0:a.metratura_reale)||"",onChange:a=>X(e.id_cavo,a.target.value),className:s?"border-red-500":i?"border-red-300 bg-red-50":"",placeholder:i?"Bloccato (OVER)":"0.0",disabled:i}),s&&(0,t.jsx)("p",{className:"text-xs text-red-500 mt-1",children:s}),i&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"⚠️ Cavo bloccato: bobina in stato OVER"}),n&&!i&&(0,t.jsx)("p",{className:"text-xs text-orange-600 mt-1",children:"⚠️ Questo cavo causa lo stato OVER della bobina"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"persone-".concat(e.id_cavo),children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:"persone-".concat(e.id_cavo),type:"number",min:"1",value:(null==a?void 0:a.numero_persone_impiegate)||1,onChange:a=>Y(e.id_cavo,a.target.value)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.S,{id:"sistemazione-".concat(e.id_cavo),checked:(null==a?void 0:a.sistemazione)||!1,onCheckedChange:a=>K(e.id_cavo,!!a)}),(0,t.jsx)(x.J,{htmlFor:"sistemazione-".concat(e.id_cavo),children:"Sistemazione"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(q.S,{id:"fascettatura-".concat(e.id_cavo),checked:(null==a?void 0:a.fascettatura)||!1,onCheckedChange:a=>ee(e.id_cavo,!!a)}),(0,t.jsx)(x.J,{htmlFor:"fascettatura-".concat(e.id_cavo),children:"Fascettatura"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Bobina assegnata: "}),(0,t.jsx)("span",{className:"BOBINA_VUOTA"===z?"text-orange-600":"text-blue-600",children:z||"Nessuna"})]})]},e.id_cavo)})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:s,children:"Annulla"}),(0,t.jsx)(l.$,{onClick:ea,disabled:T||y||0===p.length,children:T?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Salva Metri Posati"]})})]})]})})}var H=s(47924),X=s(92657);function Q(){let[e,a]=(0,i.useState)("active"),[s,h]=(0,i.useState)([]),[x,p]=(0,i.useState)([]),[v,g]=(0,i.useState)(!0),[b,f]=(0,i.useState)(""),[A,_]=(0,i.useState)(""),[C,E]=(0,i.useState)("all"),[T,w]=(0,i.useState)("all"),[R,I]=(0,i.useState)(!1),[k,V]=(0,i.useState)(!1),[M,F]=(0,i.useState)(!1),[U,G]=(0,i.useState)(!1),[q,J]=(0,i.useState)(!1),[Q,Y]=(0,i.useState)(null),[K,ee]=(0,i.useState)(null),{user:ea,cantiere:es}=(0,m.A)(),{toast:et}=(0,B.dj)(),[ei,en]=(0,i.useState)(0);(0,i.useEffect)(()=>{en((null==es?void 0:es.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[es]),(0,i.useEffect)(()=>{ei&&ei>0&&el()},[ei]);let el=async()=>{var e,a,s;try{if(g(!0),f(""),!ei||ei<=0)return void f("Cantiere non selezionato");let[a,s]=await Promise.all([u.CV.getComande(ei),u.AR.getResponsabili(ei)]),t=(null==a||null==(e=a.data)?void 0:e.comande)||(null==a?void 0:a.comande)||(null==a?void 0:a.data)||a||[],i=(null==s?void 0:s.data)||s||[];h(Array.isArray(t)?t:[]),p(Array.isArray(i)?i:[])}catch(e){f((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante il caricamento dei dati")}finally{g(!1)}},er=e=>{et({title:"Successo",description:e}),el()},eo=e=>{et({title:"Errore",description:e,variant:"destructive"})},ec=async e=>{if(confirm("Sei sicuro di voler eliminare la comanda ".concat(e,"?")))try{g(!0),await u.CV.deleteComanda(ei,e),er("Comanda ".concat(e," eliminata con successo"))}catch(e){eo("Errore durante l'eliminazione della comanda")}finally{g(!1)}},ed=e=>{switch(e){case"COMPLETATA":return(0,t.jsx)(r.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"IN_CORSO":return(0,t.jsx)(r.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"ASSEGNATA":return(0,t.jsx)(r.E,{className:"bg-yellow-100 text-yellow-800",children:"Assegnata"});case"CREATA":return(0,t.jsx)(r.E,{className:"bg-gray-100 text-gray-800",children:"Creata"});case"ANNULLATA":return(0,t.jsx)(r.E,{className:"bg-red-100 text-red-800",children:"Annullata"});default:return(0,t.jsx)(r.E,{variant:"secondary",children:e})}},em=e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(r.E,{className:a.badge,children:{POSA:"\uD83D\uDD27 Posa",COLLEGAMENTO_PARTENZA:"\uD83D\uDD0C Coll. Partenza",COLLEGAMENTO_ARRIVO:"⚡ Coll. Arrivo",CERTIFICAZIONE:"\uD83D\uDCCB Certificazione"}[e]||e.replace(/_/g," ")})},eu=Array.isArray(s)?s.filter(a=>{let s=!0;switch(e){case"active":s="IN_CORSO"===a.stato||"ASSEGNATA"===a.stato||"CREATA"===a.stato;break;case"completed":s="COMPLETATA"===a.stato;break;default:s=!0}let t=""===A||a.codice_comanda.toLowerCase().includes(A.toLowerCase())||a.descrizione&&a.descrizione.toLowerCase().includes(A.toLowerCase())||a.responsabile&&a.responsabile.toLowerCase().includes(A.toLowerCase()),i="all"===C||a.responsabile===C,n="all"===T||a.tipo_comanda===T;return s&&t&&i&&n}):[],eh={totali:Array.isArray(s)?s.length:0,in_corso:Array.isArray(s)?s.filter(e=>"IN_CORSO"===e.stato).length:0,completate:Array.isArray(s)?s.filter(e=>"COMPLETATA"===e.stato).length:0,pianificate:Array.isArray(s)?s.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length:0,filtrate:eu.length};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Gestione Comande"}),(0,t.jsx)("p",{className:"text-slate-600",children:ei>0?"Cantiere ".concat(localStorage.getItem("selectedCantiereName")||ei):"Nessun cantiere selezionato"})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(H.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(o.p,{placeholder:"Cerca per codice, responsabile, tipo, stato o descrizione...",value:A,onChange:e=>_(e.target.value),className:"pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,t.jsxs)(l.$,{onClick:()=>I(!0),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>{et({title:"Funzione in sviluppo",description:"L'assegnazione cavi sar\xe0 disponibile presto"})},disabled:0===eu.length,children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Assegna Cavi"]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>V(!0),children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Gestisci Responsabili"]})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Elenco Comande (",eu.length," di ",eh.totali,")"]})}),(0,t.jsx)(n.Zp,{className:"border border-gray-200 rounded-lg",children:(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{className:"bg-gray-50",children:[(0,t.jsx)(d.nd,{className:"font-semibold",children:"Codice"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Tipo"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Responsabile"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Contatti"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Stato"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Data Creazione"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Cavi"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Completamento"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Azioni"})]})}),(0,t.jsx)(d.BF,{children:v?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})})}):b?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),b]})})}):0===eu.length?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"})}):eu.map(e=>(0,t.jsxs)(d.Hj,{className:"hover:bg-gray-50",children:[(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.codice_comanda})}),(0,t.jsx)(d.nA,{children:em(e.tipo_comanda)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-medium",children:e.responsabile||"Non assegnato"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[e.responsabile_telefono&&(0,t.jsxs)("div",{children:["\uD83D\uDCDE ",e.responsabile_telefono]}),e.responsabile_email&&(0,t.jsxs)("div",{children:["✉️ ",e.responsabile_email]})]})}),(0,t.jsx)(d.nA,{children:ed(e.stato)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"text-sm",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.numero_cavi_assegnati||0})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"font-semibold",children:[(e.percentuale_completamento||0).toFixed(1),"%"]})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex gap-1 justify-center",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{Y(e.codice_comanda),F(!0)},title:"Visualizza",children:(0,t.jsx)(X.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{et({title:"Funzione in sviluppo",description:"La modifica comande sar\xe0 disponibile presto"})},title:"Modifica",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})}),["POSA","COLLEGAMENTO_PARTENZA","COLLEGAMENTO_ARRIVO"].includes(e.tipo_comanda)&&(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>{Y(e.codice_comanda),ee(e.tipo_comanda),"POSA"===e.tipo_comanda?J(!0):G(!0)},title:"POSA"===e.tipo_comanda?"Inserisci Metri Posati":"Inserisci Metri Collegati",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ec(e.codice_comanda),disabled:v,className:"text-red-600 hover:text-red-700",title:"Elimina",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})})]},e.codice_comanda))})]})})})]}),(0,t.jsx)(S,{open:R,onClose:()=>I(!1),onSuccess:er,onError:eo,onComandaCreated:()=>el()}),(0,t.jsx)(P,{open:k,onClose:()=>V(!1),onSuccess:er,onError:eo}),(0,t.jsx)(D,{open:M,onClose:()=>{F(!1),Y(null)},codiceComanda:Q,onSuccess:er,onError:eo}),(0,t.jsx)($,{open:U,onClose:()=>{G(!1),Y(null),ee(null)},codiceComanda:Q||"",tipoComanda:K||"POSA",onSuccess:e=>{er(e),loadComande()},onError:eo}),(0,t.jsx)(W,{open:q,onClose:()=>{J(!1),Y(null),ee(null)},codiceComanda:Q||"",onSuccess:e=>{er(e),loadComande()},onError:eo})]})}},63743:(e,a,s)=>{"use strict";s.d(a,{Fw:()=>d,NM:()=>c,Nj:()=>o,Tr:()=>l,mU:()=>t});let t={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};t.STATUS.SUCCESS,t.STATUS.WARNING,t.NEUTRAL,t.STATUS.ERROR,t.NEUTRAL,t.STATUS.ERROR;let i={DA_INSTALLARE:t.NEUTRAL,INSTALLATO:t.STATUS.SUCCESS,COLLEGATO_PARTENZA:t.STATUS.WARNING,COLLEGATO_ARRIVO:t.STATUS.WARNING,COLLEGATO:t.STATUS.SUCCESS,CERTIFICATO:t.STATUS.SUCCESS,SPARE:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},n={ATTIVA:t.STATUS.SUCCESS,COMPLETATA:t.STATUS.SUCCESS,ANNULLATA:t.NEUTRAL,IN_CORSO:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},l=e=>{let a=i[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," rounded-full px-3 py-1 text-xs font-medium"),text:a.text,bg:a.bg,border:a.border,hex:a.hex}},r=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),o=()=>r(),c=()=>({text:"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ".concat(t.NEUTRAL.text_light),color:t.NEUTRAL.text_light}),d=e=>{let a=n[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||n.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," ").concat(a.border),button:"".concat(a.bg," ").concat(a.text," ").concat(a.border," ").concat(a.hover),alert:"".concat(a.bg," ").concat(a.text," ").concat(a.border),text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};t.STATUS.ERROR,t.STATUS.WARNING,t.NEUTRAL,t.NEUTRAL},67391:(e,a,s)=>{Promise.resolve().then(s.bind(s,56834))},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>l,BF:()=>r,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var t=s(95155);s(12115);var i=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm border-collapse",a),...s})})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",a),...s})}function r(e){let{className:a,...s}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("data-[state=selected]:bg-muted border-b",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}},87481:(e,a,s)=>{"use strict";s.d(a,{dj:()=>u});var t=s(12115);let i=0,n=new Map,l=e=>{if(n.has(e))return;let a=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,a)},r=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=a;return s?l(s):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=r(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,s=(i=(i+1)%Number.MAX_VALUE).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...a,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,a]=(0,t.useState)(c);return(0,t.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,3464,1909,9384,6955,3431,5731,283,2904,8441,1684,7358],()=>a(67391)),_N_E=e.O()}]);