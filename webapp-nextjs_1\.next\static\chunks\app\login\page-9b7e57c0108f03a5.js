(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{3493:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},23227:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>n});var a=r(95155);r(12115);var i=r(99708),s=r(74466),o=r(59434);let n=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...c}=e,d=l?i.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,o.cn)(n({variant:r,size:s,className:t})),...c})}},32067:(e,t,r)=>{Promise.resolve().then(r.bind(r,40286))},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40286:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(95155),i=r(12115),s=r(35695),o=r(66695),n=r(30285),l=r(62523),c=r(85057),d=r(40283),u=r(3493),m=r(71007),p=r(23227),g=r(28883),v=r(32919),h=r(78749),f=r(92657),b=r(85339),x=r(51154),w=r(61610);let y=()=>{let[e,t]=(0,i.useState)([]),[r,a]=(0,i.useState)({totalEvents:0,loginAttempts:0,blockedRequests:0,suspiciousActivity:0,lastEventTime:null}),s=function(e,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"medium",s={type:e,timestamp:Date.now(),details:r,severity:i};t(e=>[...e,s].slice(-100)),a(t=>({totalEvents:t.totalEvents+1,loginAttempts:t.loginAttempts+ +("login_attempt"===e),blockedRequests:t.blockedRequests+ +("high"===i||"critical"===i),suspiciousActivity:t.suspiciousActivity+ +("suspicious_activity"===e),lastEventTime:s.timestamp})),console.log("Security Event:",{type:e,details:r,timestamp:new Date(s.timestamp).toISOString()})};(0,i.useEffect)(()=>{let e=()=>{(window.outerHeight-window.innerHeight>160||window.outerWidth-window.innerWidth>160)&&s("suspicious_activity",{action:"devtools_detected",windowSize:{outer:[window.outerWidth,window.outerHeight],inner:[window.innerWidth,window.innerHeight]}},"low")},t=e=>{var t;let r=(null==(t=e.clipboardData)?void 0:t.getData("text"))||"";[/script/gi,/javascript:/gi,/vbscript:/gi,/onload|onerror|onclick/gi,/<iframe|<object|<embed/gi,/union.*select/gi,/drop.*table/gi].some(e=>e.test(r))&&(e.preventDefault(),s("suspicious_activity",{action:"malicious_paste_blocked",content:r.substring(0,100)},"high"))},r=Storage.prototype.setItem;Storage.prototype.setItem=function(e,t){return/<script|javascript:|vbscript:/gi.test(t)?void s("suspicious_activity",{action:"malicious_storage_attempt",key:e,value:t.substring(0,50)},"high"):r.call(this,e,t)};let a=e=>{let t=e.message.toLowerCase();["script error","permission denied","access denied","blocked by cors","network error"].some(e=>t.includes(e))&&s("suspicious_activity",{action:"suspicious_js_error",message:e.message,filename:e.filename,lineno:e.lineno},"medium")},i=e=>{5e3>performance.now()&&s("suspicious_activity",{action:"rapid_page_exit",timeOnPage:performance.now()},"low")};window.addEventListener("resize",e),window.addEventListener("paste",t),window.addEventListener("error",a),window.addEventListener("beforeunload",i);let o=setInterval(e,5e3);return()=>{window.removeEventListener("resize",e),window.removeEventListener("paste",t),window.removeEventListener("error",a),window.removeEventListener("beforeunload",i),o&&clearInterval(o),Storage.prototype.setItem=r}},[]);let o=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,r=Date.now()-60*t*1e3;return e.filter(e=>e.timestamp>r)};return{events:e,metrics:r,getRecentEvents:o,getEventsByType:t=>e.filter(e=>e.type===t),getEventsBySeverity:t=>e.filter(e=>e.severity===t),isUnderAttack:()=>o(5).filter(e=>"high"===e.severity||"critical"===e.severity).length>3,getThreatLevel:()=>{let e=o(10),t=e.filter(e=>"critical"===e.severity).length,r=e.filter(e=>"high"===e.severity).length;return t>0?"critical":r>2?"high":e.length>10?"medium":"low"},logSecurityEvent:s,logLoginAttempt:(e,t,r)=>{s("login_attempt",{username:e,success:t,userAgent:navigator.userAgent,timestamp:Date.now(),...r},t?"low":"medium")},logFormSubmission:(e,t,r)=>{s("form_submission",{formType:e,success:t,userAgent:navigator.userAgent,...r},"low")},logSuspiciousActivity:(e,t)=>{s("suspicious_activity",{activity:e,userAgent:navigator.userAgent,url:window.location.href,...t},"high")},logRateLimitHit:(e,t)=>{s("rate_limit_hit",{endpoint:e,userAgent:navigator.userAgent,...t},"medium")}}};function j(){let[e,t]=(0,i.useState)("user"),[r,j]=(0,i.useState)({username:"",password:"",codice_cantiere:"",password_cantiere:""}),[_,N]=(0,i.useState)(""),[A,k]=(0,i.useState)(!1),[z,P]=(0,i.useState)({}),[V,L]=(0,i.useState)(!1),[E,R]=(0,i.useState)(!1),[C,S]=(0,i.useState)({}),M=(0,i.useRef)(null),I=(0,i.useRef)(null),T=(0,i.useRef)(null),D=(0,i.useRef)(null),{login:Z,loginCantiere:q}=(0,d.A)(),O=(0,s.useRouter)(),{logLoginAttempt:$,logSuspiciousActivity:F,getThreatLevel:U}=y();(0,i.useEffect)(()=>{if(Object.keys(C).length>0){let r=Object.keys(C).find(e=>C[e]);if(r){var e,t;null==(t=({username:M,password:I,codice_cantiere:T,password_cantiere:D})[r])||null==(e=t.current)||e.focus()}}},[C]);let B=()=>"user"===e?""!==r.username.trim()&&""!==r.password.trim():""!==r.codice_cantiere.trim()&&""!==r.password_cantiere.trim(),H=()=>{let t={};return"user"===e?(r.username&&0!==r.username.trim().length||(t.username="Username \xe8 obbligatorio"),r.password||(t.password="Password \xe8 obbligatoria")):(r.codice_cantiere.trim()?r.codice_cantiere.length<3&&(t.codice_cantiere="Codice cantiere troppo corto"):t.codice_cantiere="Codice cantiere \xe8 obbligatorio",r.password_cantiere||(t.password_cantiere="Password cantiere \xe8 obbligatoria")),P(t),0===Object.keys(t).length},W=async t=>{console.log("\uD83D\uDE80 LoginPage: handleSubmit chiamato!"),console.log("\uD83D\uDCDD LoginPage: Dati form:",{formData:r,loginType:e}),t.preventDefault(),N(""),P({});let a="user"===e?r.username:r.codice_cantiere;if(console.log("\uD83D\uDD12 LoginPage: Controllo rate limiting per:",a),!(0,w.Eb)("login-".concat(a),5,3e5)){console.log("❌ LoginPage: Rate limit superato per:",a),N("Troppi tentativi di login. Riprova tra 5 minuti."),F("rate_limit_exceeded",{loginType:e,identifier:a});return}if(console.log("✅ LoginPage: Rate limiting OK"),console.log("\uD83D\uDCCB LoginPage: Controllo validazione form"),!H())return void console.log("❌ LoginPage: Validazione form fallita");console.log("✅ LoginPage: Validazione form OK"),console.log("\uD83D\uDEE1️ LoginPage: Controllo livello di minaccia");let i=U();if("critical"===i){console.log("❌ LoginPage: Livello di minaccia critico:",i),N("Sistema temporaneamente non disponibile per motivi di sicurezza."),F("login_blocked_threat_level",{threatLevel:i});return}console.log("✅ LoginPage: Livello di minaccia OK:",i),k(!0);try{if("user"===e){console.log("\uD83D\uDE80 LoginPage: Inizio login per utente:",r.username);let e=await Z(r.username,r.password);if(console.log("✅ LoginPage: Login completato, risultato:",e),e.success&&e.user){$(r.username,!0,{ruolo:e.user.ruolo}),console.log("\uD83C\uDFAF LoginPage: Reindirizzamento basato su ruolo:",e.user.ruolo),"owner"===e.user.ruolo?(console.log("\uD83D\uDC51 LoginPage: Reindirizzamento admin -> /admin"),O.push("/admin")):"user"===e.user.ruolo?(console.log("\uD83D\uDC64 LoginPage: Reindirizzamento user -> /cantieri"),O.push("/cantieri")):"cantieri_user"===e.user.ruolo?(console.log("\uD83C\uDFD7️ LoginPage: Reindirizzamento cantiere -> /cavi"),O.push("/cavi")):(console.log("❓ LoginPage: Ruolo sconosciuto, default -> /cantieri"),O.push("/cantieri")),console.log("\uD83D\uDD04 LoginPage: Mantengo stato loading durante reindirizzamento");return}$(r.username,!1,{error:e.error}),N(e.error||"Credenziali non valide"),k(!1);return}{console.log("\uD83C\uDFD7️ LoginPage: Inizio login cantiere:",r.codice_cantiere);let e=await q(r.codice_cantiere,r.password_cantiere);if(e.success&&e.cantiere){$(r.codice_cantiere,!0,{type:"cantiere"}),console.log("\uD83C\uDFAF LoginPage: Reindirizzamento cantiere -> /cavi"),O.push("/cavi"),console.log("\uD83D\uDD04 LoginPage: Mantengo stato loading durante reindirizzamento");return}$(r.codice_cantiere,!1,{error:e.error}),N(e.error||"Credenziali cantiere non valide"),k(!1);return}}catch(t){console.error("❌ LoginPage: Errore durante il login:",t),$("user"===e?r.username:r.codice_cantiere,!1,{error:t.message||"Errore di rete",loginType:e}),N("Errore di connessione. Riprova."),k(!1)}},J=(e,t)=>{let r={...C};"username"===e||"codice_cantiere"===e?r[e]=""===t.trim():("password"===e||"password_cantiere"===e)&&(r[e]=""===t.trim()),S(r)},G=(e,t)=>{j(r=>({...r,[e]:t})),void 0!==C[e]&&J(e,t),_&&""!==t.trim()&&N("")},K=(e,t)=>{J(e,t)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"w-8 h-8 text-white"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,a.jsx)("p",{className:"text-slate-600",children:"Cable Installation Advance System"}),(0,a.jsx)("p",{className:"text-sm text-slate-700 font-medium",children:"Sistema di gestione cavi di nuova generazione"})]}),(0,a.jsxs)("div",{className:"flex gap-0",role:"tablist","aria-label":"Tipo di accesso",children:[(0,a.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 font-medium transition-all duration-200 relative focus:outline-none ".concat("user"===e?"text-slate-900":"text-slate-600 hover:text-slate-800"),onClick:()=>t("user"),"aria-pressed":"user"===e,role:"tab","aria-selected":"user"===e,id:"tab-user","aria-controls":"panel-user",children:[(0,a.jsx)(m.A,{className:"w-4 h-4","aria-hidden":"true"}),"Accesso Utente","user"===e&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-full","aria-hidden":"true"})]}),(0,a.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 font-medium transition-all duration-200 relative focus:outline-none ".concat("cantiere"===e?"text-slate-900":"text-slate-600 hover:text-slate-800"),onClick:()=>t("cantiere"),"aria-pressed":"cantiere"===e,role:"tab","aria-selected":"cantiere"===e,id:"tab-cantiere","aria-controls":"panel-cantiere",children:[(0,a.jsx)(p.A,{className:"w-4 h-4","aria-hidden":"true"}),"Accesso Cantiere","cantiere"===e&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 rounded-full","aria-hidden":"true"})]})]}),(0,a.jsx)(o.Zp,{children:(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("form",{onSubmit:W,className:"space-y-4",role:"tabpanel",id:"user"===e?"panel-user":"panel-cantiere","aria-labelledby":"user"===e?"tab-user":"tab-cantiere",noValidate:!0,children:["user"===e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"username",children:"Username"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,a.jsx)(l.p,{ref:M,id:"username",type:"text",placeholder:"Inserisci username",value:r.username,onChange:e=>G("username",e.target.value),onBlur:e=>K("username",e.target.value),required:!0,disabled:A,className:"pl-10 ".concat(C.username?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""),"aria-invalid":C.username,"aria-describedby":C.username?"username-error":void 0,autoComplete:"username"}),C.username&&(0,a.jsx)("p",{id:"username-error",className:"text-sm text-red-600 mt-1",children:"Il campo username \xe8 obbligatorio"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,a.jsx)(l.p,{ref:I,id:"password",type:V?"text":"password",placeholder:"Inserisci password",value:r.password,onChange:e=>G("password",e.target.value),onBlur:e=>K("password",e.target.value),required:!0,disabled:A,className:"pl-10 pr-10 ".concat(C.password?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""),"aria-invalid":C.password,"aria-describedby":C.password?"password-error":void 0,autoComplete:"current-password"}),C.password&&(0,a.jsx)("p",{id:"password-error",className:"text-sm text-red-600 mt-1",children:"Il campo password \xe8 obbligatorio"}),(0,a.jsx)("button",{type:"button",onClick:()=>L(!V),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600",disabled:A,"aria-label":V?"Nascondi password":"Mostra password",children:V?(0,a.jsx)(h.A,{className:"w-4 h-4"}):(0,a.jsx)(f.A,{className:"w-4 h-4"})})]})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"codice_cantiere",children:"Codice Cantiere"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,a.jsx)(l.p,{ref:T,id:"codice_cantiere",type:"text",placeholder:"Inserisci codice cantiere",value:r.codice_cantiere,onChange:e=>G("codice_cantiere",e.target.value),onBlur:e=>K("codice_cantiere",e.target.value),required:!0,disabled:A,className:"pl-10 ".concat(C.codice_cantiere?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""),"aria-invalid":C.codice_cantiere,"aria-describedby":C.codice_cantiere?"codice-cantiere-error":void 0,autoComplete:"off"}),C.codice_cantiere&&(0,a.jsx)("p",{id:"codice-cantiere-error",className:"text-sm text-red-600 mt-1",children:"Il campo codice cantiere \xe8 obbligatorio"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"password_cantiere",children:"Password Cantiere"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,a.jsx)(l.p,{ref:D,id:"password_cantiere",type:E?"text":"password",placeholder:"Inserisci password cantiere",value:r.password_cantiere,onChange:e=>G("password_cantiere",e.target.value),onBlur:e=>K("password_cantiere",e.target.value),required:!0,disabled:A,className:"pl-10 pr-10 ".concat(C.password_cantiere?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""),"aria-invalid":C.password_cantiere,"aria-describedby":C.password_cantiere?"password-cantiere-error":void 0,autoComplete:"current-password"}),C.password_cantiere&&(0,a.jsx)("p",{id:"password-cantiere-error",className:"text-sm text-red-600 mt-1",children:"Il campo password cantiere \xe8 obbligatorio"}),(0,a.jsx)("button",{type:"button",onClick:()=>R(!E),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600",disabled:A,"aria-label":E?"Nascondi password":"Mostra password",children:E?(0,a.jsx)(h.A,{className:"w-4 h-4"}):(0,a.jsx)(f.A,{className:"w-4 h-4"})})]})]})]}),_&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm",children:[(0,a.jsx)(b.A,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm text-red-800 font-medium",children:_})]}),(0,a.jsx)(n.$,{type:"submit",className:"w-full transition-all duration-200 ".concat(B()||A?"hover:shadow-md active:scale-[0.98] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2":"opacity-50 cursor-not-allowed bg-slate-300 hover:bg-slate-300 text-slate-500"),disabled:A||!B(),children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Accesso in corso..."]}):"Accedi"}),"user"===e&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{type:"button",className:"text-sm text-blue-700 hover:text-blue-900 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-1 py-1 transition-colors duration-200",onClick:()=>O.push("/forgot-password"),"aria-label":"Recupera password dimenticata",children:"Password dimenticata?"})})]})})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,a.jsx)("span",{className:"text-xs text-slate-400 bg-slate-50 px-2 py-1 rounded",children:"Next.js 15"}),(0,a.jsx)("span",{className:"text-xs text-slate-400 bg-slate-50 px-2 py-1 rounded",children:"PWA Ready"})]})})]})})}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var a=r(12115),i=r(63655),s=r(95155),o=a.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var n=o},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(52596),i=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,a.$)(t))}},61610:(e,t,r)=>{"use strict";r.d(t,{Eb:()=>g,GN:()=>v});let a=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,i=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,s=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,o=e=>"string"!=typeof e?"":e.trim().replace(a,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let t=o(e);return t.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:t.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(t)?/^[._-]|[._-]$/.test(t)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},l=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let t=0;return(/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[0-9]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,e.length>=12&&t++,t<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:t}:["password","123456","admin","qwerty","letmein"].some(t=>e.toLowerCase().includes(t))?{isValid:!1,error:"Password troppo comune",strength:t}:{isValid:!0,strength:t}},c=e=>{let t=o(e);return t?t.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return o(e).length>t?{isValid:!1,error:"Testo troppo lungo (max ".concat(t," caratteri)")}:s.test(e)||i.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},u=e=>{let t=o(e);return t?t.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:t.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(t)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},m=e=>{if(!e)return{isValid:!0};let t=o(e).replace(/\s/g,"");return t.length<8||t.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(t)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},p=new Map,g=(e,t,r)=>{let a=Date.now(),i=p.get(e);return!i||a>i.resetTime?(p.set(e,{count:1,resetTime:a+r}),!0):!(i.count>=t)&&(i.count++,!0)},v=e=>{let t={},r=n(e.username);if(r.isValid||(t.username=r.error),e.password){let r=l(e.password);r.isValid||(t.password=r.error)}let a=u(e.ragione_sociale);if(a.isValid||(t.ragione_sociale=a.error),e.email){let r=c(e.email);r.isValid||(t.email=r.error)}if(e.vat){let r=m(e.vat);r.isValid||(t.vat=r.error)}if(e.indirizzo){let r=d(e.indirizzo,200);r.isValid||(t.indirizzo=r.error)}if(e.nazione){let r=d(e.nazione,50);r.isValid||(t.nazione=r.error)}if(e.referente_aziendale){let r=d(e.referente_aziendale,100);r.isValid||(t.referente_aziendale=r.error)}return{isValid:0===Object.keys(t).length,errors:t}}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var a=r(95155),i=r(12115),s=r(59434);let o=i.forwardRef((e,t)=>{let{className:r,type:i,...o}=e;return(0,a.jsx)("input",{type:i,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),ref:t,...o})});o.displayName="Input"},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>n});var a=r(12115),i=r(47650),s=r(99708),o=r(95155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),i=a.forwardRef((e,a)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?r:t,{...s,ref:a})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>n,Zp:()=>s,aR:()=>o});var a=r(95155);r(12115);var i=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...r})}},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var a=r(95155);r(12115);var i=r(40968),s=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)(i.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,3464,5731,283,8441,1684,7358],()=>t(32067)),_N_E=e.O()}]);