[{"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx": "1", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\bulk-delete\\route.ts": "2", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\bulk-status\\route.ts": "3", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\export\\route.ts": "4", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\confirm-password-reset\\route.ts": "5", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\request-password-reset\\route.ts": "6", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\validate-password\\route.ts": "7", "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\verify-reset-token\\route.ts": "8", "C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx": "9", "C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx": "10", "C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx": "11", "C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx": "12", "C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx": "13", "C:\\CMS\\webapp-nextjs\\src\\app\\forgot-password\\page.tsx": "14", "C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx": "15", "C:\\CMS\\webapp-nextjs\\src\\app\\login\\page.tsx": "16", "C:\\CMS\\webapp-nextjs\\src\\app\\page.tsx": "17", "C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx": "18", "C:\\CMS\\webapp-nextjs\\src\\app\\productivity\\page.tsx": "19", "C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx": "20", "C:\\CMS\\webapp-nextjs\\src\\app\\reset-password\\page.tsx": "21", "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\DatabaseView.tsx": "22", "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\ImpersonateUser.tsx": "23", "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\QuickImpersonate.tsx": "24", "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\ResetDatabase.tsx": "25", "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\TipologieCaviManager.tsx": "26", "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\UserForm.tsx": "27", "C:\\CMS\\webapp-nextjs\\src\\components\\auth\\PasswordManagement.tsx": "28", "C:\\CMS\\webapp-nextjs\\src\\components\\auth\\PasswordResetConfirm.tsx": "29", "C:\\CMS\\webapp-nextjs\\src\\components\\auth\\SecureLoginForm.tsx": "30", "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\AggiungiCaviDialogSimple.tsx": "31", "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\BobineStatistics.tsx": "32", "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\CreaBobinaDialog.tsx": "33", "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\EliminaBobinaDialog.tsx": "34", "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\ModificaBobinaDialog.tsx": "35", "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\VisualizzaBobinaDialog.tsx": "36", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviContextMenu.tsx": "37", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviFilters.tsx": "38", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviStatistics.tsx": "39", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviTable.tsx": "40", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CertificazioneDialog.tsx": "41", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CollegamentiDialog.tsx": "42", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CreaComandaDialog.tsx": "43", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\ExportDataDialog.tsx": "44", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\ImportExcelDialog.tsx": "45", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\IncompatibleReelDialog.tsx": "46", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\InserisciMetriDialog.tsx": "47", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\ModificaBobinaDialog.tsx": "48", "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\SmartCaviFilter.tsx": "49", "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\CreaComandaDialog.tsx": "50", "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\DettagliComandaDialog.tsx": "51", "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\GestisciResponsabiliDialog.tsx": "52", "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\InserisciMetriDialog.tsx": "53", "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\InserisciMetriPosatiDialog.tsx": "54", "C:\\CMS\\webapp-nextjs\\src\\components\\common\\FilterableTable.tsx": "55", "C:\\CMS\\webapp-nextjs\\src\\components\\common\\TruncatedText.tsx": "56", "C:\\CMS\\webapp-nextjs\\src\\components\\debug\\BobinaCompatibilityDebug.tsx": "57", "C:\\CMS\\webapp-nextjs\\src\\components\\debug\\CaviDebugDialog.tsx": "58", "C:\\CMS\\webapp-nextjs\\src\\components\\debug\\MetriPosatiDebugDialog.tsx": "59", "C:\\CMS\\webapp-nextjs\\src\\components\\layout\\Navbar.tsx": "60", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\alert.tsx": "61", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\animated-button.tsx": "62", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\badge.tsx": "63", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\button.tsx": "64", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\card.tsx": "65", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\checkbox.tsx": "66", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\collapsible.tsx": "67", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\compact-actions-dropdown.tsx": "68", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\context-menu-custom.tsx": "69", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\context-menu.tsx": "70", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\dialog.tsx": "71", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\dropdown-menu.tsx": "72", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\inline-actions-menu.tsx": "73", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\input.tsx": "74", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\label.tsx": "75", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\popover.tsx": "76", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\progress.tsx": "77", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\select.tsx": "78", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\sheet.tsx": "79", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\simple-actions.tsx": "80", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\switch.tsx": "81", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\table.tsx": "82", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\tabs.tsx": "83", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\textarea.tsx": "84", "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\toaster.tsx": "85", "C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx": "86", "C:\\CMS\\webapp-nextjs\\src\\hooks\\use-toast.ts": "87", "C:\\CMS\\webapp-nextjs\\src\\hooks\\useSecurityMonitoring.ts": "88", "C:\\CMS\\webapp-nextjs\\src\\lib\\api.ts": "89", "C:\\CMS\\webapp-nextjs\\src\\lib\\utils.ts": "90", "C:\\CMS\\webapp-nextjs\\src\\middleware.ts": "91", "C:\\CMS\\webapp-nextjs\\src\\types\\index.ts": "92", "C:\\CMS\\webapp-nextjs\\src\\utils\\bobineUtils.ts": "93", "C:\\CMS\\webapp-nextjs\\src\\utils\\comandeValidation.ts": "94", "C:\\CMS\\webapp-nextjs\\src\\utils\\securityValidation.ts": "95", "C:\\CMS\\webapp-nextjs\\src\\utils\\softColors.ts": "96"}, {"size": 16346, "mtime": 1751092831524, "results": "97", "hashOfConfig": "98"}, {"size": 1825, "mtime": 1751092831524, "results": "99", "hashOfConfig": "98"}, {"size": 2284, "mtime": 1751092831526, "results": "100", "hashOfConfig": "98"}, {"size": 1991, "mtime": 1751092831526, "results": "101", "hashOfConfig": "98"}, {"size": 1210, "mtime": 1751092831527, "results": "102", "hashOfConfig": "98"}, {"size": 1210, "mtime": 1751092831527, "results": "103", "hashOfConfig": "98"}, {"size": 864, "mtime": 1751092831529, "results": "104", "hashOfConfig": "98"}, {"size": 865, "mtime": 1751092831529, "results": "105", "hashOfConfig": "98"}, {"size": 33070, "mtime": 1751092831532, "results": "106", "hashOfConfig": "98"}, {"size": 2992, "mtime": 1751092831532, "results": "107", "hashOfConfig": "98"}, {"size": 19181, "mtime": 1751643350639, "results": "108", "hashOfConfig": "98"}, {"size": 12276, "mtime": 1751092831537, "results": "109", "hashOfConfig": "98"}, {"size": 19965, "mtime": 1751092831539, "results": "110", "hashOfConfig": "98"}, {"size": 1379, "mtime": 1750351848864, "results": "111", "hashOfConfig": "98"}, {"size": 1367, "mtime": 1750362784169, "results": "112", "hashOfConfig": "98"}, {"size": 11003, "mtime": 1750351832296, "results": "113", "hashOfConfig": "98"}, {"size": 1742, "mtime": 1750249303765, "results": "114", "hashOfConfig": "98"}, {"size": 20925, "mtime": 1751092831543, "results": "115", "hashOfConfig": "98"}, {"size": 9060, "mtime": 1750398294447, "results": "116", "hashOfConfig": "98"}, {"size": 40493, "mtime": 1751092831544, "results": "117", "hashOfConfig": "98"}, {"size": 1511, "mtime": 1750366630071, "results": "118", "hashOfConfig": "98"}, {"size": 6860, "mtime": 1751092831546, "results": "119", "hashOfConfig": "98"}, {"size": 8740, "mtime": 1751092831547, "results": "120", "hashOfConfig": "98"}, {"size": 3729, "mtime": 1751092831547, "results": "121", "hashOfConfig": "98"}, {"size": 7467, "mtime": 1750265355462, "results": "122", "hashOfConfig": "98"}, {"size": 6952, "mtime": 1750249737661, "results": "123", "hashOfConfig": "98"}, {"size": 12209, "mtime": 1750361250071, "results": "124", "hashOfConfig": "98"}, {"size": 17116, "mtime": 1751092831550, "results": "125", "hashOfConfig": "98"}, {"size": 13233, "mtime": 1751092831551, "results": "126", "hashOfConfig": "98"}, {"size": 10588, "mtime": 1751092831552, "results": "127", "hashOfConfig": "98"}, {"size": 36686, "mtime": 1751643733704, "results": "128", "hashOfConfig": "98"}, {"size": 6788, "mtime": 1750573309493, "results": "129", "hashOfConfig": "98"}, {"size": 17444, "mtime": 1751092831557, "results": "130", "hashOfConfig": "98"}, {"size": 5799, "mtime": 1751092831557, "results": "131", "hashOfConfig": "98"}, {"size": 16523, "mtime": 1751092831559, "results": "132", "hashOfConfig": "98"}, {"size": 8951, "mtime": 1750542426652, "results": "133", "hashOfConfig": "98"}, {"size": 5981, "mtime": 1750363547273, "results": "134", "hashOfConfig": "98"}, {"size": 13864, "mtime": 1750362922128, "results": "135", "hashOfConfig": "98"}, {"size": 9136, "mtime": 1750573349901, "results": "136", "hashOfConfig": "98"}, {"size": 22800, "mtime": 1751092831561, "results": "137", "hashOfConfig": "98"}, {"size": 11693, "mtime": 1751092831562, "results": "138", "hashOfConfig": "98"}, {"size": 8974, "mtime": 1751092831562, "results": "139", "hashOfConfig": "98"}, {"size": 8904, "mtime": 1751092831564, "results": "140", "hashOfConfig": "98"}, {"size": 9315, "mtime": 1751092831565, "results": "141", "hashOfConfig": "98"}, {"size": 8990, "mtime": 1751092831565, "results": "142", "hashOfConfig": "98"}, {"size": 5027, "mtime": 1750912515313, "results": "143", "hashOfConfig": "98"}, {"size": 26507, "mtime": 1751643933032, "results": "144", "hashOfConfig": "98"}, {"size": 21031, "mtime": 1751643656049, "results": "145", "hashOfConfig": "98"}, {"size": 8598, "mtime": 1750398677824, "results": "146", "hashOfConfig": "98"}, {"size": 13640, "mtime": 1751092831570, "results": "147", "hashOfConfig": "98"}, {"size": 13119, "mtime": 1751092831572, "results": "148", "hashOfConfig": "98"}, {"size": 13771, "mtime": 1751092831573, "results": "149", "hashOfConfig": "98"}, {"size": 9824, "mtime": 1751090534193, "results": "150", "hashOfConfig": "98"}, {"size": 20476, "mtime": 1751643691850, "results": "151", "hashOfConfig": "98"}, {"size": 21164, "mtime": 1750395534867, "results": "152", "hashOfConfig": "98"}, {"size": 2200, "mtime": 1750397351417, "results": "153", "hashOfConfig": "98"}, {"size": 5897, "mtime": 1751092831576, "results": "154", "hashOfConfig": "98"}, {"size": 13082, "mtime": 1751092831578, "results": "155", "hashOfConfig": "98"}, {"size": 12985, "mtime": 1750572395489, "results": "156", "hashOfConfig": "98"}, {"size": 13867, "mtime": 1750352312095, "results": "157", "hashOfConfig": "98"}, {"size": 1584, "mtime": 1750304634556, "results": "158", "hashOfConfig": "98"}, {"size": 3031, "mtime": 1750265556183, "results": "159", "hashOfConfig": "98"}, {"size": 1631, "mtime": 1750160954384, "results": "160", "hashOfConfig": "98"}, {"size": 2123, "mtime": 1750160954323, "results": "161", "hashOfConfig": "98"}, {"size": 1989, "mtime": 1750160954342, "results": "162", "hashOfConfig": "98"}, {"size": 1226, "mtime": 1750222686455, "results": "163", "hashOfConfig": "98"}, {"size": 3596, "mtime": 1750362940491, "results": "164", "hashOfConfig": "98"}, {"size": 5325, "mtime": 1751092831581, "results": "165", "hashOfConfig": "98"}, {"size": 4133, "mtime": 1750572618683, "results": "166", "hashOfConfig": "98"}, {"size": 6586, "mtime": 1750363577866, "results": "167", "hashOfConfig": "98"}, {"size": 3982, "mtime": 1750160954369, "results": "168", "hashOfConfig": "98"}, {"size": 6844, "mtime": 1750362823723, "results": "169", "hashOfConfig": "98"}, {"size": 2395, "mtime": 1750278295642, "results": "170", "hashOfConfig": "98"}, {"size": 967, "mtime": 1750160954345, "results": "171", "hashOfConfig": "98"}, {"size": 611, "mtime": 1750160954349, "results": "172", "hashOfConfig": "98"}, {"size": 1244, "mtime": 1750366507846, "results": "173", "hashOfConfig": "98"}, {"size": 740, "mtime": 1750160954390, "results": "174", "hashOfConfig": "98"}, {"size": 6253, "mtime": 1750222643808, "results": "175", "hashOfConfig": "98"}, {"size": 4090, "mtime": 1750160954380, "results": "176", "hashOfConfig": "98"}, {"size": 2205, "mtime": 1751092831581, "results": "177", "hashOfConfig": "98"}, {"size": 1294, "mtime": 1750705893261, "results": "178", "hashOfConfig": "98"}, {"size": 2464, "mtime": 1750365492900, "results": "179", "hashOfConfig": "98"}, {"size": 1897, "mtime": 1750247264710, "results": "180", "hashOfConfig": "98"}, {"size": 772, "mtime": 1750364605031, "results": "181", "hashOfConfig": "98"}, {"size": 2563, "mtime": 1750362761447, "results": "182", "hashOfConfig": "98"}, {"size": 10296, "mtime": 1751092831587, "results": "183", "hashOfConfig": "98"}, {"size": 3167, "mtime": 1750362855321, "results": "184", "hashOfConfig": "98"}, {"size": 8229, "mtime": 1751643513423, "results": "185", "hashOfConfig": "98"}, {"size": 15344, "mtime": 1751090521553, "results": "186", "hashOfConfig": "98"}, {"size": 166, "mtime": 1750160913708, "results": "187", "hashOfConfig": "98"}, {"size": 3158, "mtime": 1751092831589, "results": "188", "hashOfConfig": "98"}, {"size": 8311, "mtime": 1751090023112, "results": "189", "hashOfConfig": "98"}, {"size": 10557, "mtime": 1750658372442, "results": "190", "hashOfConfig": "98"}, {"size": 10634, "mtime": 1751086692362, "results": "191", "hashOfConfig": "98"}, {"size": 8393, "mtime": 1750276096197, "results": "192", "hashOfConfig": "98"}, {"size": 5596, "mtime": 1750572765419, "results": "193", "hashOfConfig": "98"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 32, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ld7ue1", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 22, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 39, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 49, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 23, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 83, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx", ["482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\bulk-delete\\route.ts", ["515", "516"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\bulk-status\\route.ts", ["517", "518"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\export\\route.ts", ["519"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\confirm-password-reset\\route.ts", ["520"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\request-password-reset\\route.ts", ["521"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\validate-password\\route.ts", ["522"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\verify-reset-token\\route.ts", ["523"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx", ["524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx", ["541", "542", "543", "544", "545", "546", "547", "548", "549"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx", ["550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx", ["563", "564", "565"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx", ["566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\forgot-password\\page.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\app\\login\\page.tsx", ["589", "590", "591"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\page.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx", ["592", "593", "594", "595", "596", "597", "598", "599", "600", "601"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\productivity\\page.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx", ["602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640"], [], "C:\\CMS\\webapp-nextjs\\src\\app\\reset-password\\page.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\DatabaseView.tsx", ["641", "642", "643", "644"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\ImpersonateUser.tsx", ["645", "646", "647", "648", "649", "650"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\QuickImpersonate.tsx", ["651", "652"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\ResetDatabase.tsx", ["653", "654", "655", "656", "657", "658", "659", "660"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\TipologieCaviManager.tsx", ["661", "662"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\admin\\UserForm.tsx", ["663", "664", "665", "666"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\auth\\PasswordManagement.tsx", ["667", "668", "669", "670", "671"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\auth\\PasswordResetConfirm.tsx", ["672", "673", "674"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\auth\\SecureLoginForm.tsx", ["675", "676", "677"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\AggiungiCaviDialogSimple.tsx", ["678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\BobineStatistics.tsx", ["728"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\CreaBobinaDialog.tsx", ["729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\EliminaBobinaDialog.tsx", ["741"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\ModificaBobinaDialog.tsx", ["742", "743", "744", "745"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\bobine\\VisualizzaBobinaDialog.tsx", ["746"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviContextMenu.tsx", ["747", "748", "749", "750", "751"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviFilters.tsx", ["752", "753", "754", "755", "756", "757", "758", "759", "760"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviStatistics.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CaviTable.tsx", ["761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CertificazioneDialog.tsx", ["779", "780", "781", "782", "783"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CollegamentiDialog.tsx", ["784", "785", "786", "787", "788", "789"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\CreaComandaDialog.tsx", ["790", "791", "792", "793"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\ExportDataDialog.tsx", ["794", "795", "796", "797"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\ImportExcelDialog.tsx", ["798"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\IncompatibleReelDialog.tsx", ["799", "800", "801"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\InserisciMetriDialog.tsx", ["802", "803", "804", "805", "806", "807"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\ModificaBobinaDialog.tsx", ["808", "809", "810", "811", "812", "813", "814", "815", "816"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\cavi\\SmartCaviFilter.tsx", ["817"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\CreaComandaDialog.tsx", ["818", "819", "820", "821", "822", "823", "824"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\DettagliComandaDialog.tsx", ["825", "826", "827", "828", "829", "830", "831", "832", "833"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\GestisciResponsabiliDialog.tsx", ["834", "835", "836", "837", "838", "839", "840"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\InserisciMetriDialog.tsx", ["841", "842", "843", "844", "845", "846", "847"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\comande\\InserisciMetriPosatiDialog.tsx", ["848", "849", "850", "851", "852", "853", "854", "855", "856"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\common\\FilterableTable.tsx", ["857", "858", "859", "860", "861", "862", "863", "864"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\common\\TruncatedText.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\debug\\BobinaCompatibilityDebug.tsx", ["865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\debug\\CaviDebugDialog.tsx", ["889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\debug\\MetriPosatiDebugDialog.tsx", ["900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\layout\\Navbar.tsx", ["918", "919", "920"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\alert.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\animated-button.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\badge.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\button.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\card.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\compact-actions-dropdown.tsx", ["921", "922", "923"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\context-menu-custom.tsx", ["924", "925", "926"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\context-menu.tsx", ["927"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\dialog.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\dropdown-menu.tsx", ["928"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\inline-actions-menu.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\input.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\label.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\popover.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\progress.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\select.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\sheet.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\simple-actions.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\switch.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\table.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\tabs.tsx", [], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\textarea.tsx", ["929"], [], "C:\\CMS\\webapp-nextjs\\src\\components\\ui\\toaster.tsx", ["930", "931"], [], "C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx", ["932", "933", "934", "935", "936", "937"], [], "C:\\CMS\\webapp-nextjs\\src\\hooks\\use-toast.ts", ["938", "939"], [], "C:\\CMS\\webapp-nextjs\\src\\hooks\\useSecurityMonitoring.ts", ["940", "941", "942", "943", "944", "945", "946"], [], "C:\\CMS\\webapp-nextjs\\src\\lib\\api.ts", ["947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029"], [], "C:\\CMS\\webapp-nextjs\\src\\lib\\utils.ts", [], [], "C:\\CMS\\webapp-nextjs\\src\\middleware.ts", ["1030"], [], "C:\\CMS\\webapp-nextjs\\src\\types\\index.ts", ["1031", "1032"], [], "C:\\CMS\\webapp-nextjs\\src\\utils\\bobineUtils.ts", ["1033", "1034", "1035", "1036"], [], "C:\\CMS\\webapp-nextjs\\src\\utils\\comandeValidation.ts", ["1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045"], [], "C:\\CMS\\webapp-nextjs\\src\\utils\\securityValidation.ts", [], [], "C:\\CMS\\webapp-nextjs\\src\\utils\\softColors.ts", [], [], {"ruleId": "1046", "severity": 2, "message": "1047", "line": 4, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 44}, {"ruleId": "1046", "severity": 2, "message": "1049", "line": 5, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1050", "line": 6, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1051", "line": 6, "column": 41, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 56}, {"ruleId": "1046", "severity": 2, "message": "1052", "line": 6, "column": 58, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 70}, {"ruleId": "1046", "severity": 2, "message": "1053", "line": 6, "column": 72, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 85}, {"ruleId": "1046", "severity": 2, "message": "1054", "line": 6, "column": 87, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 98}, {"ruleId": "1046", "severity": 2, "message": "1055", "line": 7, "column": 8, "nodeType": null, "messageId": "1048", "endLine": 7, "endColumn": 30}, {"ruleId": "1046", "severity": 2, "message": "1056", "line": 10, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1057", "line": 16, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 16, "endColumn": 13}, {"ruleId": "1046", "severity": 2, "message": "1058", "line": 23, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 23, "endColumn": 11}, {"ruleId": "1046", "severity": 2, "message": "1059", "line": 25, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 25, "endColumn": 12}, {"ruleId": "1046", "severity": 2, "message": "1060", "line": 26, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 26, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1061", "line": 27, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 27, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1062", "line": 29, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 29, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1063", "line": 30, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 30, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1064", "line": 31, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 31, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1065", "line": 32, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 32, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1066", "line": 33, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 6}, {"ruleId": "1046", "severity": 2, "message": "1067", "line": 34, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 34, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1068", "line": 35, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 35, "endColumn": 6}, {"ruleId": "1046", "severity": 2, "message": "1069", "line": 42, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 42, "endColumn": 12}, {"ruleId": "1046", "severity": 2, "message": "1070", "line": 48, "column": 22, "nodeType": null, "messageId": "1048", "endLine": 48, "endColumn": 35}, {"ruleId": "1046", "severity": 2, "message": "1071", "line": 50, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 50, "endColumn": 18}, {"ruleId": "1046", "severity": 2, "message": "1072", "line": 54, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 54, "endColumn": 22}, {"ruleId": "1046", "severity": 2, "message": "1073", "line": 54, "column": 24, "nodeType": null, "messageId": "1048", "endLine": 54, "endColumn": 39}, {"ruleId": "1074", "severity": 1, "message": "1075", "line": 61, "column": 6, "nodeType": "1076", "endLine": 61, "endColumn": 17, "suggestions": "1077"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 75, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 75, "endColumn": 24, "suggestions": "1082"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 91, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 91, "endColumn": 24, "suggestions": "1083"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 101, "column": 23, "nodeType": "1080", "messageId": "1081", "endLine": 101, "endColumn": 26, "suggestions": "1084"}, {"ruleId": "1046", "severity": 2, "message": "1085", "line": 107, "column": 27, "nodeType": null, "messageId": "1048", "endLine": 107, "endColumn": 36}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 131, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 131, "endColumn": 24, "suggestions": "1086"}, {"ruleId": "1046", "severity": 2, "message": "1087", "line": 184, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 184, "endColumn": 22}, {"ruleId": "1046", "severity": 2, "message": "1088", "line": 30, "column": 51, "nodeType": null, "messageId": "1048", "endLine": 30, "endColumn": 53}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 52, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 52, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1088", "line": 46, "column": 46, "nodeType": null, "messageId": "1048", "endLine": 46, "endColumn": 48}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 69, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 69, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 37, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 33, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 33, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 27, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 27, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 27, "column": 12, "nodeType": null, "messageId": "1048", "endLine": 27, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1047", "line": 5, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 44}, {"ruleId": "1046", "severity": 2, "message": "1090", "line": 5, "column": 46, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 56}, {"ruleId": "1046", "severity": 2, "message": "1091", "line": 5, "column": 58, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 67}, {"ruleId": "1046", "severity": 2, "message": "1092", "line": 7, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 7, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1062", "line": 19, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 19, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1093", "line": 22, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 22, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1094", "line": 23, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 23, "endColumn": 11}, {"ruleId": "1046", "severity": 2, "message": "1095", "line": 24, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 24, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1096", "line": 25, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 25, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1097", "line": 35, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 35, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 81, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 81, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 103, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 103, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 116, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 116, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1098", "line": 277, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 277, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1099", "line": 281, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 281, "endColumn": 17}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 726, "column": 44, "nodeType": "1102", "messageId": "1103", "suggestions": "1104"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 726, "column": 65, "nodeType": "1102", "messageId": "1103", "suggestions": "1105"}, {"ruleId": "1046", "severity": 2, "message": "1106", "line": 5, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1107", "line": 5, "column": 16, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1047", "line": 5, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 44}, {"ruleId": "1046", "severity": 2, "message": "1090", "line": 5, "column": 46, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 56}, {"ruleId": "1046", "severity": 2, "message": "1091", "line": 5, "column": 58, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 67}, {"ruleId": "1046", "severity": 2, "message": "1092", "line": 7, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 7, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1097", "line": 18, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 18, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1108", "line": 37, "column": 6, "nodeType": "1076", "endLine": 37, "endColumn": 35, "suggestions": "1109"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 48, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 48, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1110", "line": 53, "column": 20, "nodeType": null, "messageId": "1048", "endLine": 53, "endColumn": 25}, {"ruleId": "1046", "severity": 2, "message": "1111", "line": 53, "column": 27, "nodeType": null, "messageId": "1048", "endLine": 53, "endColumn": 38}, {"ruleId": "1046", "severity": 2, "message": "1112", "line": 53, "column": 40, "nodeType": null, "messageId": "1048", "endLine": 53, "endColumn": 47}, {"ruleId": "1046", "severity": 2, "message": "1113", "line": 61, "column": 28, "nodeType": null, "messageId": "1048", "endLine": 61, "endColumn": 47}, {"ruleId": "1046", "severity": 2, "message": "1114", "line": 102, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 102, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1115", "line": 149, "column": 6, "nodeType": "1076", "endLine": 149, "endColumn": 18, "suggestions": "1116"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 166, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 166, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 190, "column": 26, "nodeType": "1080", "messageId": "1081", "endLine": 190, "endColumn": 29, "suggestions": "1117"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 198, "column": 61, "nodeType": "1080", "messageId": "1081", "endLine": 198, "endColumn": 64, "suggestions": "1118"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 199, "column": 68, "nodeType": "1080", "messageId": "1081", "endLine": 199, "endColumn": 71, "suggestions": "1119"}, {"ruleId": "1046", "severity": 2, "message": "1120", "line": 209, "column": 18, "nodeType": null, "messageId": "1048", "endLine": 209, "endColumn": 28}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 214, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 214, "endColumn": 24, "suggestions": "1121"}, {"ruleId": "1046", "severity": 2, "message": "1099", "line": 516, "column": 28, "nodeType": null, "messageId": "1048", "endLine": 516, "endColumn": 31}, {"ruleId": "1046", "severity": 2, "message": "1062", "line": 18, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 18, "endColumn": 9}, {"ruleId": "1074", "severity": 1, "message": "1122", "line": 41, "column": 6, "nodeType": "1076", "endLine": 41, "endColumn": 8, "suggestions": "1123"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 56, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 56, "endColumn": 24, "suggestions": "1124"}, {"ruleId": "1046", "severity": 2, "message": "1047", "line": 4, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 44}, {"ruleId": "1046", "severity": 2, "message": "1090", "line": 4, "column": 46, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 56}, {"ruleId": "1046", "severity": 2, "message": "1091", "line": 4, "column": 58, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 67}, {"ruleId": "1046", "severity": 2, "message": "1125", "line": 9, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 9, "endColumn": 18}, {"ruleId": "1046", "severity": 2, "message": "1064", "line": 24, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 24, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1063", "line": 25, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 25, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1126", "line": 27, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 27, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1127", "line": 28, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 28, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1128", "line": 29, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 29, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1129", "line": 35, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 35, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1058", "line": 36, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 36, "endColumn": 11}, {"ruleId": "1046", "severity": 2, "message": "1130", "line": 40, "column": 23, "nodeType": null, "messageId": "1048", "endLine": 40, "endColumn": 37}, {"ruleId": "1046", "severity": 2, "message": "1131", "line": 42, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 42, "endColumn": 22}, {"ruleId": "1046", "severity": 2, "message": "1132", "line": 46, "column": 32, "nodeType": null, "messageId": "1048", "endLine": 46, "endColumn": 55}, {"ruleId": "1046", "severity": 2, "message": "1133", "line": 47, "column": 24, "nodeType": null, "messageId": "1048", "endLine": 47, "endColumn": 39}, {"ruleId": "1046", "severity": 2, "message": "1097", "line": 56, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 56, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1075", "line": 75, "column": 6, "nodeType": "1076", "endLine": 75, "endColumn": 18, "suggestions": "1134"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 101, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 101, "endColumn": 24, "suggestions": "1135"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 134, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 134, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 134, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 134, "endColumn": 24, "suggestions": "1136"}, {"ruleId": "1046", "severity": 2, "message": "1137", "line": 141, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 141, "endColumn": 26}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 146, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 146, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 146, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 146, "endColumn": 24, "suggestions": "1138"}, {"ruleId": "1046", "severity": 2, "message": "1067", "line": 11, "column": 56, "nodeType": null, "messageId": "1048", "endLine": 11, "endColumn": 62}, {"ruleId": "1046", "severity": 2, "message": "1139", "line": 25, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 25, "endColumn": 26}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 119, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 119, "endColumn": 24, "suggestions": "1140"}, {"ruleId": "1046", "severity": 2, "message": "1125", "line": 9, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 9, "endColumn": 18}, {"ruleId": "1046", "severity": 2, "message": "1141", "line": 16, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 16, "endColumn": 20}, {"ruleId": "1046", "severity": 2, "message": "1142", "line": 30, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 30, "endColumn": 10}, {"ruleId": "1046", "severity": 2, "message": "1143", "line": 38, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 38, "endColumn": 11}, {"ruleId": "1046", "severity": 2, "message": "1144", "line": 39, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 39, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1097", "line": 55, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 55, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1145", "line": 87, "column": 6, "nodeType": "1076", "endLine": 87, "endColumn": 31, "suggestions": "1146"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 103, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 103, "endColumn": 24, "suggestions": "1147"}, {"ruleId": "1046", "severity": 2, "message": "1148", "line": 173, "column": 52, "nodeType": null, "messageId": "1048", "endLine": 173, "endColumn": 56}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 173, "column": 59, "nodeType": "1080", "messageId": "1081", "endLine": 173, "endColumn": 62, "suggestions": "1149"}, {"ruleId": "1046", "severity": 2, "message": "1150", "line": 10, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1151", "line": 10, "column": 18, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 31}, {"ruleId": "1046", "severity": 2, "message": "1152", "line": 10, "column": 33, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 43}, {"ruleId": "1046", "severity": 2, "message": "1153", "line": 10, "column": 45, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 58}, {"ruleId": "1046", "severity": 2, "message": "1154", "line": 10, "column": 60, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 71}, {"ruleId": "1046", "severity": 2, "message": "1155", "line": 12, "column": 22, "nodeType": null, "messageId": "1048", "endLine": 12, "endColumn": 33}, {"ruleId": "1046", "severity": 2, "message": "1156", "line": 13, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 13, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1157", "line": 13, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 13, "endColumn": 38}, {"ruleId": "1046", "severity": 2, "message": "1158", "line": 13, "column": 40, "nodeType": null, "messageId": "1048", "endLine": 13, "endColumn": 48}, {"ruleId": "1046", "severity": 2, "message": "1159", "line": 15, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 12}, {"ruleId": "1046", "severity": 2, "message": "1160", "line": 28, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 28, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1161", "line": 40, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 40, "endColumn": 11}, {"ruleId": "1046", "severity": 2, "message": "1162", "line": 41, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 41, "endColumn": 6}, {"ruleId": "1046", "severity": 2, "message": "1163", "line": 42, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 42, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1164", "line": 43, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 43, "endColumn": 12}, {"ruleId": "1046", "severity": 2, "message": "1165", "line": 44, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 44, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1166", "line": 45, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 45, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1167", "line": 46, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 46, "endColumn": 12}, {"ruleId": "1046", "severity": 2, "message": "1168", "line": 51, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 51, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1169", "line": 51, "column": 26, "nodeType": null, "messageId": "1048", "endLine": 51, "endColumn": 43}, {"ruleId": "1046", "severity": 2, "message": "1170", "line": 52, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 52, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1171", "line": 52, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 52, "endColumn": 49}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 52, "column": 62, "nodeType": "1080", "messageId": "1081", "endLine": 52, "endColumn": 65, "suggestions": "1172"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 53, "column": 46, "nodeType": "1080", "messageId": "1081", "endLine": 53, "endColumn": 49, "suggestions": "1173"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 54, "column": 68, "nodeType": "1080", "messageId": "1081", "endLine": 54, "endColumn": 71, "suggestions": "1174"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 55, "column": 56, "nodeType": "1080", "messageId": "1081", "endLine": 55, "endColumn": 59, "suggestions": "1175"}, {"ruleId": "1046", "severity": 2, "message": "1097", "line": 59, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 59, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1176", "line": 71, "column": 15, "nodeType": null, "messageId": "1048", "endLine": 71, "endColumn": 20}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 83, "column": 57, "nodeType": "1080", "messageId": "1081", "endLine": 83, "endColumn": 60, "suggestions": "1177"}, {"ruleId": "1046", "severity": 2, "message": "1099", "line": 173, "column": 16, "nodeType": null, "messageId": "1048", "endLine": 173, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1178", "line": 211, "column": 57, "nodeType": null, "messageId": "1048", "endLine": 211, "endColumn": 63}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 237, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 237, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1179", "line": 242, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 242, "endColumn": 21}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 533, "column": 100, "nodeType": "1080", "messageId": "1081", "endLine": 533, "endColumn": 103, "suggestions": "1180"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 629, "column": 79, "nodeType": "1080", "messageId": "1081", "endLine": 629, "endColumn": 82, "suggestions": "1181"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 679, "column": 76, "nodeType": "1080", "messageId": "1081", "endLine": 679, "endColumn": 79, "suggestions": "1182"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 746, "column": 76, "nodeType": "1080", "messageId": "1081", "endLine": 746, "endColumn": 79, "suggestions": "1183"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 762, "column": 90, "nodeType": "1080", "messageId": "1081", "endLine": 762, "endColumn": 93, "suggestions": "1184"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 793, "column": 80, "nodeType": "1080", "messageId": "1081", "endLine": 793, "endColumn": 83, "suggestions": "1185"}, {"ruleId": "1046", "severity": 2, "message": "1049", "line": 5, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 16}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 13, "column": 40, "nodeType": "1080", "messageId": "1081", "endLine": 13, "endColumn": 43, "suggestions": "1186"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 25, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 25, "endColumn": 22, "suggestions": "1187"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 37, "column": 54, "nodeType": "1080", "messageId": "1081", "endLine": 37, "endColumn": 57, "suggestions": "1188"}, {"ruleId": "1046", "severity": 2, "message": "1049", "line": 6, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 16}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 37, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 37, "endColumn": 22, "suggestions": "1189"}, {"ruleId": "1046", "severity": 2, "message": "1190", "line": 60, "column": 13, "nodeType": null, "messageId": "1048", "endLine": 60, "endColumn": 21}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 74, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 74, "endColumn": 22, "suggestions": "1191"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 104, "column": 74, "nodeType": "1102", "messageId": "1103", "suggestions": "1192"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 130, "column": 48, "nodeType": "1102", "messageId": "1103", "suggestions": "1193"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 33, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 57, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 57, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1049", "line": 5, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1051", "line": 6, "column": 24, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 39}, {"ruleId": "1046", "severity": 2, "message": "1194", "line": 11, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 11, "endColumn": 17}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 35, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 35, "endColumn": 22, "suggestions": "1195"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 64, "column": 50, "nodeType": "1102", "messageId": "1103", "suggestions": "1196"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 99, "column": 60, "nodeType": "1102", "messageId": "1103", "suggestions": "1197"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 178, "column": 20, "nodeType": "1102", "messageId": "1103", "suggestions": "1198"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 180, "column": 20, "nodeType": "1102", "messageId": "1103", "suggestions": "1199"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 83, "column": 29, "nodeType": "1102", "messageId": "1103", "suggestions": "1200"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 96, "column": 45, "nodeType": "1102", "messageId": "1103", "suggestions": "1201"}, {"ruleId": "1046", "severity": 2, "message": "1194", "line": 14, "column": 23, "nodeType": null, "messageId": "1048", "endLine": 14, "endColumn": 30}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 63, "column": 46, "nodeType": "1080", "messageId": "1081", "endLine": 63, "endColumn": 49, "suggestions": "1202"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 126, "column": 29, "nodeType": "1080", "messageId": "1081", "endLine": 126, "endColumn": 32, "suggestions": "1203"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 144, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 144, "endColumn": 22, "suggestions": "1204"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 70, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 70, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1205", "line": 82, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 82, "endColumn": 33}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 157, "column": 51, "nodeType": "1102", "messageId": "1103", "suggestions": "1206"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 483, "column": 38, "nodeType": "1102", "messageId": "1103", "suggestions": "1207"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 484, "column": 67, "nodeType": "1102", "messageId": "1103", "suggestions": "1208"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 79, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 79, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1205", "line": 91, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 91, "endColumn": 33}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 362, "column": 36, "nodeType": "1102", "messageId": "1103", "suggestions": "1209"}, {"ruleId": "1046", "severity": 2, "message": "1210", "line": 12, "column": 28, "nodeType": null, "messageId": "1048", "endLine": 12, "endColumn": 44}, {"ruleId": "1046", "severity": 2, "message": "1099", "line": 202, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 202, "endColumn": 17}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 202, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 202, "endColumn": 22, "suggestions": "1211"}, {"ruleId": "1046", "severity": 2, "message": "1212", "line": 31, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 31, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1106", "line": 33, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1107", "line": 33, "column": 16, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1090", "line": 33, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 39}, {"ruleId": "1046", "severity": 2, "message": "1091", "line": 33, "column": 41, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 50}, {"ruleId": "1046", "severity": 2, "message": "1092", "line": 34, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 34, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1213", "line": 35, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 35, "endColumn": 18}, {"ruleId": "1046", "severity": 2, "message": "1214", "line": 36, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 36, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1215", "line": 36, "column": 17, "nodeType": null, "messageId": "1048", "endLine": 36, "endColumn": 33}, {"ruleId": "1046", "severity": 2, "message": "1150", "line": 37, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1151", "line": 37, "column": 18, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 31}, {"ruleId": "1046", "severity": 2, "message": "1152", "line": 37, "column": 33, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 43}, {"ruleId": "1046", "severity": 2, "message": "1153", "line": 37, "column": 45, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 58}, {"ruleId": "1046", "severity": 2, "message": "1154", "line": 37, "column": 60, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 71}, {"ruleId": "1046", "severity": 2, "message": "1065", "line": 39, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 39, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1129", "line": 43, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 43, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1216", "line": 44, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 44, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1128", "line": 45, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 45, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1217", "line": 46, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 46, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1218", "line": 47, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 47, "endColumn": 13}, {"ruleId": "1046", "severity": 2, "message": "1219", "line": 48, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 48, "endColumn": 6}, {"ruleId": "1046", "severity": 2, "message": "1220", "line": 51, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 51, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1221", "line": 92, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 92, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1222", "line": 92, "column": 19, "nodeType": null, "messageId": "1048", "endLine": 92, "endColumn": 29}, {"ruleId": "1046", "severity": 2, "message": "1223", "line": 108, "column": 18, "nodeType": null, "messageId": "1048", "endLine": 108, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1224", "line": 109, "column": 21, "nodeType": null, "messageId": "1048", "endLine": 109, "endColumn": 33}, {"ruleId": "1046", "severity": 2, "message": "1225", "line": 110, "column": 27, "nodeType": null, "messageId": "1048", "endLine": 110, "endColumn": 45}, {"ruleId": "1046", "severity": 2, "message": "1226", "line": 111, "column": 28, "nodeType": null, "messageId": "1048", "endLine": 111, "endColumn": 47}, {"ruleId": "1046", "severity": 2, "message": "1227", "line": 112, "column": 26, "nodeType": null, "messageId": "1048", "endLine": 112, "endColumn": 43}, {"ruleId": "1046", "severity": 2, "message": "1228", "line": 113, "column": 26, "nodeType": null, "messageId": "1048", "endLine": 113, "endColumn": 43}, {"ruleId": "1046", "severity": 2, "message": "1229", "line": 115, "column": 24, "nodeType": null, "messageId": "1048", "endLine": 115, "endColumn": 39}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 145, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 145, "endColumn": 24, "suggestions": "1230"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 145, "column": 34, "nodeType": "1080", "messageId": "1081", "endLine": 145, "endColumn": 37, "suggestions": "1231"}, {"ruleId": "1046", "severity": 2, "message": "1232", "line": 208, "column": 64, "nodeType": null, "messageId": "1048", "endLine": 208, "endColumn": 70}, {"ruleId": "1046", "severity": 2, "message": "1233", "line": 234, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 234, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1234", "line": 239, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 239, "endColumn": 25}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 300, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 300, "endColumn": 24, "suggestions": "1235"}, {"ruleId": "1074", "severity": 1, "message": "1236", "line": 332, "column": 6, "nodeType": "1076", "endLine": 332, "endColumn": 32, "suggestions": "1237"}, {"ruleId": "1046", "severity": 2, "message": "1238", "line": 367, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 367, "endColumn": 29}, {"ruleId": "1046", "severity": 2, "message": "1239", "line": 368, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 368, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1240", "line": 372, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 372, "endColumn": 25}, {"ruleId": "1046", "severity": 2, "message": "1241", "line": 395, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 395, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1242", "line": 416, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 416, "endColumn": 28}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 569, "column": 25, "nodeType": "1080", "messageId": "1081", "endLine": 569, "endColumn": 28, "suggestions": "1243"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 623, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 623, "endColumn": 24, "suggestions": "1244"}, {"ruleId": "1046", "severity": 2, "message": "1245", "line": 671, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 671, "endColumn": 25}, {"ruleId": "1046", "severity": 2, "message": "1246", "line": 674, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 674, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1248", "line": 677, "column": 9, "nodeType": "1249", "messageId": "1250", "endLine": 677, "endColumn": 16, "fix": "1251"}, {"ruleId": "1046", "severity": 2, "message": "1252", "line": 784, "column": 26, "nodeType": null, "messageId": "1048", "endLine": 784, "endColumn": 31}, {"ruleId": "1046", "severity": 2, "message": "1253", "line": 812, "column": 30, "nodeType": null, "messageId": "1048", "endLine": 812, "endColumn": 31}, {"ruleId": "1046", "severity": 2, "message": "1092", "line": 5, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 5, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1150", "line": 16, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 16, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1151", "line": 17, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 17, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1152", "line": 18, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 18, "endColumn": 13}, {"ruleId": "1046", "severity": 2, "message": "1153", "line": 19, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 19, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1154", "line": 20, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 20, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1254", "line": 72, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 72, "endColumn": 26}, {"ruleId": "1046", "severity": 2, "message": "1255", "line": 74, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 74, "endColumn": 26}, {"ruleId": "1046", "severity": 2, "message": "1256", "line": 75, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 75, "endColumn": 29}, {"ruleId": "1074", "severity": 1, "message": "1257", "line": 85, "column": 6, "nodeType": "1076", "endLine": 85, "endColumn": 24, "suggestions": "1258"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 115, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 115, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 164, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 164, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 272, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 272, "endColumn": 24, "suggestions": "1259"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 63, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 63, "endColumn": 24, "suggestions": "1260"}, {"ruleId": "1074", "severity": 1, "message": "1261", "line": 127, "column": 6, "nodeType": "1076", "endLine": 127, "endColumn": 16, "suggestions": "1262"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 236, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 236, "endColumn": 24, "suggestions": "1263"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 288, "column": 55, "nodeType": "1102", "messageId": "1103", "suggestions": "1265"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 288, "column": 67, "nodeType": "1102", "messageId": "1103", "suggestions": "1266"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 174, "column": 31, "nodeType": "1102", "messageId": "1103", "suggestions": "1267"}, {"ruleId": "1046", "severity": 2, "message": "1268", "line": 3, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 3, "endColumn": 18}, {"ruleId": "1046", "severity": 2, "message": "1269", "line": 9, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 9, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1270", "line": 10, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1271", "line": 11, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 11, "endColumn": 24}, {"ruleId": "1046", "severity": 2, "message": "1160", "line": 29, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 29, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1150", "line": 8, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 8, "endColumn": 9}, {"ruleId": "1046", "severity": 2, "message": "1151", "line": 9, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 9, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1152", "line": 10, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 10, "endColumn": 13}, {"ruleId": "1046", "severity": 2, "message": "1153", "line": 11, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 11, "endColumn": 16}, {"ruleId": "1046", "severity": 2, "message": "1154", "line": 12, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 12, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1272", "line": 28, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 28, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1273", "line": 81, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 81, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1274", "line": 82, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 82, "endColumn": 15}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 162, "column": 56, "nodeType": "1080", "messageId": "1081", "endLine": 162, "endColumn": 59, "suggestions": "1275"}, {"ruleId": "1046", "severity": 2, "message": "1106", "line": 8, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 8, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1107", "line": 8, "column": 16, "nodeType": null, "messageId": "1048", "endLine": 8, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1090", "line": 8, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 8, "endColumn": 39}, {"ruleId": "1046", "severity": 2, "message": "1091", "line": 8, "column": 41, "nodeType": null, "messageId": "1048", "endLine": 8, "endColumn": 50}, {"ruleId": "1046", "severity": 2, "message": "1276", "line": 15, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1272", "line": 16, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 16, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1058", "line": 17, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 17, "endColumn": 11}, {"ruleId": "1046", "severity": 2, "message": "1219", "line": 18, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 18, "endColumn": 6}, {"ruleId": "1046", "severity": 2, "message": "1063", "line": 19, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 19, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1065", "line": 20, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 20, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1064", "line": 21, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 21, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1142", "line": 22, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 22, "endColumn": 10}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 112, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 112, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 141, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 141, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 175, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 175, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1277", "line": 387, "column": 6, "nodeType": "1076", "endLine": 387, "endColumn": 95, "suggestions": "1278"}, {"ruleId": "1046", "severity": 2, "message": "1252", "line": 390, "column": 33, "nodeType": null, "messageId": "1048", "endLine": 390, "endColumn": 38}, {"ruleId": "1046", "severity": 2, "message": "1279", "line": 433, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 433, "endColumn": 23}, {"ruleId": "1046", "severity": 2, "message": "1280", "line": 24, "column": 39, "nodeType": null, "messageId": "1048", "endLine": 24, "endColumn": 47}, {"ruleId": "1074", "severity": 1, "message": "1281", "line": 75, "column": 6, "nodeType": "1076", "endLine": 75, "endColumn": 18, "suggestions": "1282"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 84, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 84, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 126, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 126, "endColumn": 24, "suggestions": "1283"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 154, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 154, "endColumn": 24, "suggestions": "1284"}, {"ruleId": "1046", "severity": 2, "message": "1063", "line": 22, "column": 37, "nodeType": null, "messageId": "1048", "endLine": 22, "endColumn": 48}, {"ruleId": "1074", "severity": 1, "message": "1281", "line": 63, "column": 6, "nodeType": "1076", "endLine": 63, "endColumn": 18, "suggestions": "1285"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 72, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 72, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 112, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 112, "endColumn": 24, "suggestions": "1286"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 136, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 136, "endColumn": 24, "suggestions": "1287"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 160, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 160, "endColumn": 24, "suggestions": "1288"}, {"ruleId": "1046", "severity": 2, "message": "1056", "line": 14, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 14, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1281", "line": 74, "column": 6, "nodeType": "1076", "endLine": 74, "endColumn": 25, "suggestions": "1289"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 83, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 83, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 137, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 137, "endColumn": 24, "suggestions": "1290"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 69, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 69, "endColumn": 24, "suggestions": "1291"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 95, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 95, "endColumn": 24, "suggestions": "1292"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 135, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 135, "endColumn": 24, "suggestions": "1293"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 253, "column": 22, "nodeType": "1102", "messageId": "1103", "suggestions": "1294"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 101, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 101, "endColumn": 24, "suggestions": "1295"}, {"ruleId": "1247", "severity": 2, "message": "1296", "line": 70, "column": 7, "nodeType": "1249", "messageId": "1250", "endLine": 70, "endColumn": 24, "fix": "1297"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 140, "column": 38, "nodeType": "1102", "messageId": "1103", "suggestions": "1298"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 140, "column": 75, "nodeType": "1102", "messageId": "1103", "suggestions": "1299"}, {"ruleId": "1074", "severity": 1, "message": "1145", "line": 116, "column": 6, "nodeType": "1076", "endLine": 116, "endColumn": 28, "suggestions": "1300"}, {"ruleId": "1074", "severity": 1, "message": "1301", "line": 126, "column": 6, "nodeType": "1076", "endLine": 126, "endColumn": 35, "suggestions": "1302"}, {"ruleId": "1247", "severity": 2, "message": "1303", "line": 131, "column": 9, "nodeType": "1249", "messageId": "1250", "endLine": 131, "endColumn": 27, "fix": "1304"}, {"ruleId": "1247", "severity": 2, "message": "1305", "line": 132, "column": 9, "nodeType": "1249", "messageId": "1250", "endLine": 132, "endColumn": 31, "fix": "1306"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 215, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 215, "endColumn": 24, "suggestions": "1307"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 370, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 370, "endColumn": 24, "suggestions": "1308"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 100, "column": 23, "nodeType": "1080", "messageId": "1081", "endLine": 100, "endColumn": 26, "suggestions": "1309"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 103, "column": 57, "nodeType": "1080", "messageId": "1081", "endLine": 103, "endColumn": 60, "suggestions": "1310"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 104, "column": 35, "nodeType": "1080", "messageId": "1081", "endLine": 104, "endColumn": 38, "suggestions": "1311"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 105, "column": 57, "nodeType": "1080", "messageId": "1081", "endLine": 105, "endColumn": 60, "suggestions": "1312"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 106, "column": 35, "nodeType": "1080", "messageId": "1081", "endLine": 106, "endColumn": 38, "suggestions": "1313"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 111, "column": 62, "nodeType": "1080", "messageId": "1081", "endLine": 111, "endColumn": 65, "suggestions": "1314"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 141, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 141, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1315", "line": 178, "column": 6, "nodeType": "1076", "endLine": 178, "endColumn": 56, "suggestions": "1316"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 302, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 302, "endColumn": 24, "suggestions": "1317"}, {"ruleId": "1046", "severity": 2, "message": "1212", "line": 6, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 6, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1160", "line": 24, "column": 47, "nodeType": null, "messageId": "1048", "endLine": 24, "endColumn": 52}, {"ruleId": "1046", "severity": 2, "message": "1094", "line": 24, "column": 54, "nodeType": null, "messageId": "1048", "endLine": 24, "endColumn": 62}, {"ruleId": "1046", "severity": 2, "message": "1318", "line": 27, "column": 59, "nodeType": null, "messageId": "1048", "endLine": 27, "endColumn": 72}, {"ruleId": "1074", "severity": 1, "message": "1281", "line": 95, "column": 6, "nodeType": "1076", "endLine": 95, "endColumn": 24, "suggestions": "1319"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 117, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 117, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 117, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 117, "endColumn": 24, "suggestions": "1320"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 215, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 215, "endColumn": 24, "suggestions": "1321"}, {"ruleId": "1046", "severity": 2, "message": "1126", "line": 25, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 25, "endColumn": 7}, {"ruleId": "1046", "severity": 2, "message": "1127", "line": 26, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 26, "endColumn": 8}, {"ruleId": "1046", "severity": 2, "message": "1095", "line": 30, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 30, "endColumn": 9}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 56, "column": 20, "nodeType": "1080", "messageId": "1081", "endLine": 56, "endColumn": 23, "suggestions": "1322"}, {"ruleId": "1046", "severity": 2, "message": "1323", "line": 74, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 74, "endColumn": 10}, {"ruleId": "1074", "severity": 1, "message": "1324", "line": 97, "column": 6, "nodeType": "1076", "endLine": 97, "endColumn": 39, "suggestions": "1325"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 118, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 118, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 118, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 118, "endColumn": 24, "suggestions": "1326"}, {"ruleId": "1046", "severity": 2, "message": "1327", "line": 161, "column": 9, "nodeType": null, "messageId": "1048", "endLine": 161, "endColumn": 25}, {"ruleId": "1046", "severity": 2, "message": "1092", "line": 16, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 16, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1281", "line": 91, "column": 6, "nodeType": "1076", "endLine": 91, "endColumn": 24, "suggestions": "1328"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 113, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 113, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 113, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 113, "endColumn": 24, "suggestions": "1329"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 148, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 148, "endColumn": 24, "suggestions": "1330"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 200, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 200, "endColumn": 24, "suggestions": "1331"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 218, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 218, "endColumn": 24, "suggestions": "1332"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 40, "column": 61, "nodeType": "1080", "messageId": "1081", "endLine": 40, "endColumn": 64, "suggestions": "1333"}, {"ruleId": "1074", "severity": 1, "message": "1236", "line": 51, "column": 6, "nodeType": "1076", "endLine": 51, "endColumn": 27, "suggestions": "1334"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 68, "column": 41, "nodeType": "1080", "messageId": "1081", "endLine": 68, "endColumn": 44, "suggestions": "1335"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 79, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 79, "endColumn": 22, "suggestions": "1336"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 88, "column": 68, "nodeType": "1080", "messageId": "1081", "endLine": 88, "endColumn": 71, "suggestions": "1337"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 105, "column": 24, "nodeType": "1080", "messageId": "1081", "endLine": 105, "endColumn": 27, "suggestions": "1338"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 128, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 128, "endColumn": 22, "suggestions": "1339"}, {"ruleId": "1046", "severity": 2, "message": "1340", "line": 17, "column": 32, "nodeType": null, "messageId": "1048", "endLine": 17, "endColumn": 45}, {"ruleId": "1046", "severity": 2, "message": "1272", "line": 17, "column": 47, "nodeType": null, "messageId": "1048", "endLine": 17, "endColumn": 52}, {"ruleId": "1074", "severity": 1, "message": "1341", "line": 88, "column": 6, "nodeType": "1076", "endLine": 88, "endColumn": 27, "suggestions": "1342"}, {"ruleId": "1074", "severity": 1, "message": "1145", "line": 95, "column": 6, "nodeType": "1076", "endLine": 95, "endColumn": 35, "suggestions": "1343"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 144, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 144, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 144, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 144, "endColumn": 24, "suggestions": "1344"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 165, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 165, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 165, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 165, "endColumn": 24, "suggestions": "1345"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 376, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 376, "endColumn": 24, "suggestions": "1346"}, {"ruleId": "1046", "severity": 2, "message": "1347", "line": 31, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 31, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1348", "line": 32, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 32, "endColumn": 12}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 55, "column": 22, "nodeType": "1080", "messageId": "1081", "endLine": 55, "endColumn": 25, "suggestions": "1349"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 59, "column": 9, "nodeType": "1080", "messageId": "1081", "endLine": 59, "endColumn": 12, "suggestions": "1350"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 63, "column": 41, "nodeType": "1080", "messageId": "1081", "endLine": 63, "endColumn": 44, "suggestions": "1351"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 64, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 64, "endColumn": 24, "suggestions": "1352"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 499, "column": 9, "nodeType": "1080", "messageId": "1081", "endLine": 499, "endColumn": 12, "suggestions": "1353"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 503, "column": 26, "nodeType": "1080", "messageId": "1081", "endLine": 503, "endColumn": 29, "suggestions": "1354"}, {"ruleId": "1046", "severity": 2, "message": "1272", "line": 12, "column": 15, "nodeType": null, "messageId": "1048", "endLine": 12, "endColumn": 20}, {"ruleId": "1046", "severity": 2, "message": "1355", "line": 29, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 29, "endColumn": 14}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 31, "column": 46, "nodeType": "1080", "messageId": "1081", "endLine": 31, "endColumn": 49, "suggestions": "1356"}, {"ruleId": "1074", "severity": 1, "message": "1357", "line": 37, "column": 6, "nodeType": "1076", "endLine": 37, "endColumn": 30, "suggestions": "1358"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 72, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 72, "endColumn": 19}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 100, "column": 48, "nodeType": "1102", "messageId": "1103", "suggestions": "1359"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 100, "column": 67, "nodeType": "1102", "messageId": "1103", "suggestions": "1360"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 101, "column": 46, "nodeType": "1102", "messageId": "1103", "suggestions": "1361"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 101, "column": 63, "nodeType": "1102", "messageId": "1103", "suggestions": "1362"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 102, "column": 53, "nodeType": "1102", "messageId": "1103", "suggestions": "1363"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 102, "column": 78, "nodeType": "1102", "messageId": "1103", "suggestions": "1364"}, {"ruleId": "1046", "severity": 2, "message": "1252", "line": 114, "column": 39, "nodeType": null, "messageId": "1048", "endLine": 114, "endColumn": 44}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 126, "column": 63, "nodeType": "1102", "messageId": "1103", "suggestions": "1365"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 126, "column": 85, "nodeType": "1102", "messageId": "1103", "suggestions": "1366"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 127, "column": 65, "nodeType": "1102", "messageId": "1103", "suggestions": "1367"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 127, "column": 89, "nodeType": "1102", "messageId": "1103", "suggestions": "1368"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 131, "column": 61, "nodeType": "1102", "messageId": "1103", "suggestions": "1369"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 131, "column": 81, "nodeType": "1102", "messageId": "1103", "suggestions": "1370"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 132, "column": 63, "nodeType": "1102", "messageId": "1103", "suggestions": "1371"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 132, "column": 85, "nodeType": "1102", "messageId": "1103", "suggestions": "1372"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 136, "column": 68, "nodeType": "1102", "messageId": "1103", "suggestions": "1373"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 136, "column": 95, "nodeType": "1102", "messageId": "1103", "suggestions": "1374"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 137, "column": 70, "nodeType": "1102", "messageId": "1103", "suggestions": "1375"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 137, "column": 99, "nodeType": "1102", "messageId": "1103", "suggestions": "1376"}, {"ruleId": "1046", "severity": 2, "message": "1272", "line": 15, "column": 15, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 20}, {"ruleId": "1046", "severity": 2, "message": "1377", "line": 15, "column": 22, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 30}, {"ruleId": "1046", "severity": 2, "message": "1221", "line": 33, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 33, "endColumn": 17}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 37, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 37, "endColumn": 22, "suggestions": "1378"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 38, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 38, "endColumn": 24, "suggestions": "1379"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 39, "column": 23, "nodeType": "1080", "messageId": "1081", "endLine": 39, "endColumn": 26, "suggestions": "1380"}, {"ruleId": "1074", "severity": 1, "message": "1381", "line": 47, "column": 6, "nodeType": "1076", "endLine": 47, "endColumn": 30, "suggestions": "1382"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 57, "column": 27, "nodeType": "1080", "messageId": "1081", "endLine": 57, "endColumn": 30, "suggestions": "1383"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 58, "column": 29, "nodeType": "1080", "messageId": "1081", "endLine": 58, "endColumn": 32, "suggestions": "1384"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 59, "column": 31, "nodeType": "1080", "messageId": "1081", "endLine": 59, "endColumn": 34, "suggestions": "1385"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 134, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 134, "endColumn": 19}, {"ruleId": "1046", "severity": 2, "message": "1386", "line": 14, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 14, "endColumn": 14}, {"ruleId": "1046", "severity": 2, "message": "1387", "line": 14, "column": 16, "nodeType": null, "messageId": "1048", "endLine": 14, "endColumn": 27}, {"ruleId": "1046", "severity": 2, "message": "1388", "line": 14, "column": 29, "nodeType": null, "messageId": "1048", "endLine": 14, "endColumn": 37}, {"ruleId": "1046", "severity": 2, "message": "1389", "line": 14, "column": 39, "nodeType": null, "messageId": "1048", "endLine": 14, "endColumn": 50}, {"ruleId": "1046", "severity": 2, "message": "1272", "line": 15, "column": 15, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 20}, {"ruleId": "1046", "severity": 2, "message": "1377", "line": 15, "column": 22, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 30}, {"ruleId": "1046", "severity": 2, "message": "1390", "line": 16, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 16, "endColumn": 17}, {"ruleId": "1046", "severity": 2, "message": "1391", "line": 18, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 18, "endColumn": 14}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 24, "column": 20, "nodeType": "1080", "messageId": "1081", "endLine": 24, "endColumn": 23, "suggestions": "1392"}, {"ruleId": "1046", "severity": 2, "message": "1393", "line": 37, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 37, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 42, "column": 18, "nodeType": "1080", "messageId": "1081", "endLine": 42, "endColumn": 21, "suggestions": "1394"}, {"ruleId": "1074", "severity": 1, "message": "1395", "line": 52, "column": 6, "nodeType": "1076", "endLine": 52, "endColumn": 59, "suggestions": "1396"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 60, "column": 24, "nodeType": "1080", "messageId": "1081", "endLine": 60, "endColumn": 27, "suggestions": "1397"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 145, "column": 37, "nodeType": "1102", "messageId": "1103", "suggestions": "1398"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 312, "column": 33, "nodeType": "1102", "messageId": "1103", "suggestions": "1399"}, {"ruleId": "1100", "severity": 2, "message": "1264", "line": 312, "column": 44, "nodeType": "1102", "messageId": "1103", "suggestions": "1400"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 312, "column": 65, "nodeType": "1102", "messageId": "1103", "suggestions": "1401"}, {"ruleId": "1100", "severity": 2, "message": "1101", "line": 317, "column": 60, "nodeType": "1102", "messageId": "1103", "suggestions": "1402"}, {"ruleId": "1046", "severity": 2, "message": "1092", "line": 8, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 8, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1058", "line": 15, "column": 3, "nodeType": null, "messageId": "1048", "endLine": 15, "endColumn": 11}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 29, "column": 98, "nodeType": "1080", "messageId": "1081", "endLine": 29, "endColumn": 101, "suggestions": "1403"}, {"ruleId": "1046", "severity": 2, "message": "1404", "line": 4, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 22}, {"ruleId": "1046", "severity": 2, "message": "1405", "line": 25, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 25, "endColumn": 26}, {"ruleId": "1046", "severity": 2, "message": "1406", "line": 75, "column": 45, "nodeType": null, "messageId": "1048", "endLine": 75, "endColumn": 55}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 9, "column": 37, "nodeType": "1080", "messageId": "1081", "endLine": 9, "endColumn": 40, "suggestions": "1407"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 32, "column": 50, "nodeType": "1080", "messageId": "1081", "endLine": 32, "endColumn": 53, "suggestions": "1408"}, {"ruleId": "1046", "severity": 2, "message": "1409", "line": 66, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 66, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1410", "line": 101, "column": 43, "nodeType": null, "messageId": "1048", "endLine": 101, "endColumn": 50}, {"ruleId": "1046", "severity": 2, "message": "1411", "line": 56, "column": 71, "nodeType": null, "messageId": "1048", "endLine": 56, "endColumn": 74}, {"ruleId": "1412", "severity": 2, "message": "1413", "line": 5, "column": 18, "nodeType": "1249", "messageId": "1414", "endLine": 5, "endColumn": 31, "suggestions": "1415"}, {"ruleId": "1046", "severity": 2, "message": "1214", "line": 4, "column": 10, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 15}, {"ruleId": "1046", "severity": 2, "message": "1215", "line": 4, "column": 17, "nodeType": null, "messageId": "1048", "endLine": 4, "endColumn": 33}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 13, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 13, "endColumn": 24, "suggestions": "1416"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 18, "column": 48, "nodeType": "1080", "messageId": "1081", "endLine": 18, "endColumn": 51, "suggestions": "1417"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 46, "column": 60, "nodeType": "1080", "messageId": "1081", "endLine": 46, "endColumn": 63, "suggestions": "1418"}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 145, "column": 24, "nodeType": null, "messageId": "1048", "endLine": 145, "endColumn": 29}, {"ruleId": "1046", "severity": 2, "message": "1419", "line": 150, "column": 18, "nodeType": null, "messageId": "1048", "endLine": 150, "endColumn": 28}, {"ruleId": "1046", "severity": 2, "message": "1089", "line": 163, "column": 14, "nodeType": null, "messageId": "1048", "endLine": 163, "endColumn": 19}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 45, "column": 52, "nodeType": "1080", "messageId": "1081", "endLine": 45, "endColumn": 55, "suggestions": "1420"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 102, "column": 27, "nodeType": "1080", "messageId": "1081", "endLine": 102, "endColumn": 30, "suggestions": "1421"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 8, "column": 27, "nodeType": "1080", "messageId": "1081", "endLine": 8, "endColumn": 30, "suggestions": "1422"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 33, "column": 29, "nodeType": "1080", "messageId": "1081", "endLine": 33, "endColumn": 32, "suggestions": "1423"}, {"ruleId": "1046", "severity": 2, "message": "1253", "line": 147, "column": 33, "nodeType": null, "messageId": "1048", "endLine": 147, "endColumn": 34}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 212, "column": 89, "nodeType": "1080", "messageId": "1081", "endLine": 212, "endColumn": 92, "suggestions": "1424"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 222, "column": 91, "nodeType": "1080", "messageId": "1081", "endLine": 222, "endColumn": 94, "suggestions": "1425"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 231, "column": 77, "nodeType": "1080", "messageId": "1081", "endLine": 231, "endColumn": 80, "suggestions": "1426"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 240, "column": 71, "nodeType": "1080", "messageId": "1081", "endLine": 240, "endColumn": 74, "suggestions": "1427"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 51, "column": 34, "nodeType": "1080", "messageId": "1081", "endLine": 51, "endColumn": 37, "suggestions": "1428"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 68, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 68, "endColumn": 22, "suggestions": "1429"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 74, "column": 20, "nodeType": "1080", "messageId": "1081", "endLine": 74, "endColumn": 23, "suggestions": "1430"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 74, "column": 45, "nodeType": "1080", "messageId": "1081", "endLine": 74, "endColumn": 48, "suggestions": "1431"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 80, "column": 19, "nodeType": "1080", "messageId": "1081", "endLine": 80, "endColumn": 22, "suggestions": "1432"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 80, "column": 44, "nodeType": "1080", "messageId": "1081", "endLine": 80, "endColumn": 47, "suggestions": "1433"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 86, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 86, "endColumn": 24, "suggestions": "1434"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 86, "column": 46, "nodeType": "1080", "messageId": "1081", "endLine": 86, "endColumn": 49, "suggestions": "1435"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 92, "column": 22, "nodeType": "1080", "messageId": "1081", "endLine": 92, "endColumn": 25, "suggestions": "1436"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 116, "column": 68, "nodeType": "1080", "messageId": "1081", "endLine": 116, "endColumn": 71, "suggestions": "1437"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 123, "column": 22, "nodeType": "1080", "messageId": "1081", "endLine": 123, "endColumn": 25, "suggestions": "1438"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 142, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 142, "endColumn": 16, "suggestions": "1439"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 146, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 146, "endColumn": 16, "suggestions": "1440"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 150, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 150, "endColumn": 16, "suggestions": "1441"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 153, "column": 42, "nodeType": "1080", "messageId": "1081", "endLine": 153, "endColumn": 45, "suggestions": "1442"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 154, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 154, "endColumn": 17, "suggestions": "1443"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 157, "column": 61, "nodeType": "1080", "messageId": "1081", "endLine": 157, "endColumn": 64, "suggestions": "1444"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 158, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 158, "endColumn": 16, "suggestions": "1445"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 161, "column": 62, "nodeType": "1080", "messageId": "1081", "endLine": 161, "endColumn": 65, "suggestions": "1446"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 166, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 166, "endColumn": 17, "suggestions": "1447"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 174, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 174, "endColumn": 17, "suggestions": "1448"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 181, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 181, "endColumn": 17, "suggestions": "1449"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 185, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 185, "endColumn": 17, "suggestions": "1450"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 198, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 198, "endColumn": 16, "suggestions": "1451"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 204, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 204, "endColumn": 16, "suggestions": "1452"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 207, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 207, "endColumn": 16, "suggestions": "1453"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 218, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 218, "endColumn": 16, "suggestions": "1454"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 222, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 222, "endColumn": 16, "suggestions": "1455"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 230, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 230, "endColumn": 16, "suggestions": "1456"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 233, "column": 46, "nodeType": "1080", "messageId": "1081", "endLine": 233, "endColumn": 49, "suggestions": "1457"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 234, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 234, "endColumn": 17, "suggestions": "1458"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 237, "column": 68, "nodeType": "1080", "messageId": "1081", "endLine": 237, "endColumn": 71, "suggestions": "1459"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 238, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 238, "endColumn": 16, "suggestions": "1460"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 242, "column": 16, "nodeType": "1080", "messageId": "1081", "endLine": 242, "endColumn": 19, "suggestions": "1461"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 249, "column": 65, "nodeType": "1080", "messageId": "1081", "endLine": 249, "endColumn": 68, "suggestions": "1462"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 250, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 250, "endColumn": 16, "suggestions": "1463"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 258, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 258, "endColumn": 16, "suggestions": "1464"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 266, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 266, "endColumn": 16, "suggestions": "1465"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 270, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 270, "endColumn": 16, "suggestions": "1466"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 274, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 274, "endColumn": 16, "suggestions": "1467"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 277, "column": 48, "nodeType": "1080", "messageId": "1081", "endLine": 277, "endColumn": 51, "suggestions": "1468"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 278, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 278, "endColumn": 17, "suggestions": "1469"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 281, "column": 56, "nodeType": "1080", "messageId": "1081", "endLine": 281, "endColumn": 59, "suggestions": "1470"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 282, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 282, "endColumn": 17, "suggestions": "1471"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 287, "column": 70, "nodeType": "1080", "messageId": "1081", "endLine": 287, "endColumn": 73, "suggestions": "1472"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 288, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 288, "endColumn": 16, "suggestions": "1473"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 291, "column": 71, "nodeType": "1080", "messageId": "1081", "endLine": 291, "endColumn": 74, "suggestions": "1474"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 292, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 292, "endColumn": 16, "suggestions": "1475"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 300, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 300, "endColumn": 17, "suggestions": "1476"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 310, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 310, "endColumn": 16, "suggestions": "1477"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 314, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 314, "endColumn": 16, "suggestions": "1478"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 322, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 322, "endColumn": 16, "suggestions": "1479"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 325, "column": 58, "nodeType": "1080", "messageId": "1081", "endLine": 325, "endColumn": 61, "suggestions": "1480"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 326, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 326, "endColumn": 17, "suggestions": "1481"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 329, "column": 65, "nodeType": "1080", "messageId": "1081", "endLine": 329, "endColumn": 68, "suggestions": "1482"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 330, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 330, "endColumn": 16, "suggestions": "1483"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 340, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 340, "endColumn": 16, "suggestions": "1484"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 343, "column": 62, "nodeType": "1080", "messageId": "1081", "endLine": 343, "endColumn": 65, "suggestions": "1485"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 344, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 344, "endColumn": 17, "suggestions": "1486"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 359, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 359, "endColumn": 24, "suggestions": "1487"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 368, "column": 21, "nodeType": "1080", "messageId": "1081", "endLine": 368, "endColumn": 24, "suggestions": "1488"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 389, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 389, "endColumn": 16, "suggestions": "1489"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 393, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 393, "endColumn": 16, "suggestions": "1490"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 397, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 397, "endColumn": 16, "suggestions": "1491"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 401, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 401, "endColumn": 16, "suggestions": "1492"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 409, "column": 20, "nodeType": "1080", "messageId": "1081", "endLine": 409, "endColumn": 23, "suggestions": "1493"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 416, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 416, "endColumn": 16, "suggestions": "1494"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 420, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 420, "endColumn": 16, "suggestions": "1495"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 423, "column": 30, "nodeType": "1080", "messageId": "1081", "endLine": 423, "endColumn": 33, "suggestions": "1496"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 424, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 424, "endColumn": 17, "suggestions": "1497"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 427, "column": 41, "nodeType": "1080", "messageId": "1081", "endLine": 427, "endColumn": 44, "suggestions": "1498"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 428, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 428, "endColumn": 16, "suggestions": "1499"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 434, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 434, "endColumn": 16, "suggestions": "1500"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 438, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 438, "endColumn": 16, "suggestions": "1501"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 441, "column": 22, "nodeType": "1080", "messageId": "1081", "endLine": 441, "endColumn": 25, "suggestions": "1502"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 442, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 442, "endColumn": 17, "suggestions": "1503"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 445, "column": 37, "nodeType": "1080", "messageId": "1081", "endLine": 445, "endColumn": 40, "suggestions": "1504"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 446, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 446, "endColumn": 16, "suggestions": "1505"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 454, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 454, "endColumn": 16, "suggestions": "1506"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 458, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 458, "endColumn": 16, "suggestions": "1507"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 462, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 462, "endColumn": 17, "suggestions": "1508"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 466, "column": 13, "nodeType": "1080", "messageId": "1081", "endLine": 466, "endColumn": 16, "suggestions": "1509"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 470, "column": 14, "nodeType": "1080", "messageId": "1081", "endLine": 470, "endColumn": 17, "suggestions": "1510"}, {"ruleId": "1046", "severity": 2, "message": "1511", "line": 89, "column": 11, "nodeType": null, "messageId": "1048", "endLine": 89, "endColumn": 13}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 114, "column": 29, "nodeType": "1080", "messageId": "1081", "endLine": 114, "endColumn": 32, "suggestions": "1512"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 262, "column": 34, "nodeType": "1080", "messageId": "1081", "endLine": 262, "endColumn": 37, "suggestions": "1513"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 32, "column": 40, "nodeType": "1080", "messageId": "1081", "endLine": 32, "endColumn": 43, "suggestions": "1514"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 42, "column": 36, "nodeType": "1080", "messageId": "1081", "endLine": 42, "endColumn": 39, "suggestions": "1515"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 53, "column": 36, "nodeType": "1080", "messageId": "1081", "endLine": 53, "endColumn": 39, "suggestions": "1516"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 53, "column": 49, "nodeType": "1080", "messageId": "1081", "endLine": 53, "endColumn": 52, "suggestions": "1517"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 18, "column": 15, "nodeType": "1080", "messageId": "1081", "endLine": 18, "endColumn": 18, "suggestions": "1518"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 20, "column": 11, "nodeType": "1080", "messageId": "1081", "endLine": 20, "endColumn": 14, "suggestions": "1519"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 56, "column": 9, "nodeType": "1080", "messageId": "1081", "endLine": 56, "endColumn": 12, "suggestions": "1520"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 98, "column": 9, "nodeType": "1080", "messageId": "1081", "endLine": 98, "endColumn": 12, "suggestions": "1521"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 150, "column": 32, "nodeType": "1080", "messageId": "1081", "endLine": 150, "endColumn": 35, "suggestions": "1522"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 197, "column": 38, "nodeType": "1080", "messageId": "1081", "endLine": 197, "endColumn": 41, "suggestions": "1523"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 232, "column": 35, "nodeType": "1080", "messageId": "1081", "endLine": 232, "endColumn": 38, "suggestions": "1524"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 258, "column": 42, "nodeType": "1080", "messageId": "1081", "endLine": 258, "endColumn": 45, "suggestions": "1525"}, {"ruleId": "1078", "severity": 2, "message": "1079", "line": 283, "column": 56, "nodeType": "1080", "messageId": "1081", "endLine": 283, "endColumn": 59, "suggestions": "1526"}, "@typescript-eslint/no-unused-vars", "'CardDescription' is defined but never used.", "unusedVar", "'Button' is defined but never used.", "'AnimatedButton' is defined but never used.", "'SecondaryButton' is defined but never used.", "'DangerButton' is defined but never used.", "'OutlineButton' is defined but never used.", "'QuickButton' is defined but never used.", "'CompactActionsDropdown' is defined but never used.", "'Input' is defined but never used.", "'api' is defined but never used.", "'Settings' is defined but never used.", "'Building2' is defined but never used.", "'Search' is defined but never used.", "'Plus' is defined but never used.", "'Trash2' is defined but never used.", "'CheckCircle' is defined but never used.", "'Clock' is defined but never used.", "'AlertCircle' is defined but never used.", "'Eye' is defined but never used.", "'Shield' is defined but never used.", "'Key' is defined but never used.", "'RefreshCw' is defined but never used.", "'setSearchTerm' is assigned a value but never used.", "'cantieri' is assigned a value but never used.", "'notification' is assigned a value but never used.", "'setNotification' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", "ArrayExpression", ["1527"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1528", "1529"], ["1530", "1531"], ["1532", "1533"], "'savedUser' is defined but never used.", ["1534", "1535"], "'filteredUsers' is assigned a value but never used.", "'id' is defined but never used.", "'error' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Badge' is defined but never used.", "'Copy' is defined but never used.", "'Calendar' is defined but never used.", "'MapPin' is defined but never used.", "'User' is defined but never used.", "'user' is assigned a value but never used.", "'copyToClipboard' is assigned a value but never used.", "'err' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1536", "1537", "1538", "1539"], ["1540", "1541", "1542", "1543"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCantiere'. Either include it or remove the dependency array.", ["1544"], "'title' is defined but never used.", "'description' is defined but never used.", "'variant' is defined but never used.", "'setSelectionEnabled' is assigned a value but never used.", "'stats' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadCavi' and 'loadRevisioneCorrente'. Either include them or remove the dependency array.", ["1545"], ["1546", "1547"], ["1548", "1549"], ["1550", "1551"], "'debugError' is defined but never used.", ["1552", "1553"], "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1554"], ["1555", "1556"], "'Progress' is defined but never used.", "'Play' is defined but never used.", "'Pause' is defined but never used.", "'Square' is defined but never used.", "'Filter' is defined but never used.", "'setSelectedTab' is assigned a value but never used.", "'responsabili' is assigned a value but never used.", "'setSelectedResponsabile' is assigned a value but never used.", "'setSelectedTipo' is assigned a value but never used.", ["1557"], ["1558", "1559"], ["1560", "1561"], "'handleCambiaStato' is assigned a value but never used.", ["1562", "1563"], "'validationErrors' is assigned a value but never used.", ["1564", "1565"], "'getReelStateColor' is defined but never used.", "'Package' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1566"], ["1567", "1568"], "'data' is defined but never used.", ["1569", "1570"], "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'cantieriApi' is defined but never used.", "'ReportAvanzamento' is defined but never used.", "'ReportBOQ' is defined but never used.", "'Cantiere' is defined but never used.", "'BarChart3' is defined but never used.", "'Users' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Area' is defined but never used.", "'AreaChart' is defined but never used.", "'selectedPeriod' is assigned a value but never used.", "'setSelectedPeriod' is assigned a value but never used.", "'reportAvanzamento' is assigned a value but never used.", "'setReportAvanzamento' is assigned a value but never used.", ["1571", "1572"], ["1573", "1574"], ["1575", "1576"], ["1577", "1578"], "'token' is assigned a value but never used.", ["1579", "1580"], "'format' is assigned a value but never used.", "'calculateIAP' is assigned a value but never used.", ["1581", "1582"], ["1583", "1584"], ["1585", "1586"], ["1587", "1588"], ["1589", "1590"], ["1591", "1592"], ["1593", "1594"], ["1595", "1596"], ["1597", "1598"], ["1599", "1600"], "'userData' is assigned a value but never used.", ["1601", "1602"], ["1603", "1604", "1605", "1606"], ["1607", "1608", "1609", "1610"], "'Loader2' is defined but never used.", ["1611", "1612"], ["1613", "1614", "1615", "1616"], ["1617", "1618", "1619", "1620"], ["1621", "1622", "1623", "1624"], ["1625", "1626", "1627", "1628"], ["1629", "1630", "1631", "1632"], ["1633", "1634", "1635", "1636"], ["1637", "1638"], ["1639", "1640"], ["1641", "1642"], "'getPasswordStrengthColor' is assigned a value but never used.", ["1643", "1644", "1645", "1646"], ["1647", "1648", "1649", "1650"], ["1651", "1652", "1653", "1654"], ["1655", "1656", "1657", "1658"], "'validatePassword' is defined but never used.", ["1659", "1660"], "'Label' is defined but never used.", "'Checkbox' is defined but never used.", "'Alert' is defined but never used.", "'AlertDescription' is defined but never used.", "'CheckSquare' is defined but never used.", "'ArrowUpDown' is defined but never used.", "'Calculator' is defined but never used.", "'Zap' is defined but never used.", "'useAuth' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setSortOrder' is assigned a value but never used.", "'setFilterTipologia' is assigned a value but never used.", "'setFilterFormazione' is assigned a value but never used.", "'setFilterMetriMin' is assigned a value but never used.", "'setFilterMetriMax' is assigned a value but never used.", "'setItemsPerPage' is assigned a value but never used.", ["1661", "1662"], ["1663", "1664"], "'cavoId' is defined but never used.", "'tipologieUniche' is assigned a value but never used.", "'formazioniUniche' is assigned a value but never used.", ["1665", "1666"], "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1667"], "'metriResiduiBobina' is assigned a value but never used.", "'metriInseriti' is assigned a value but never used.", "'isIncompatible' is assigned a value but never used.", "'handleSelectAll' is assigned a value but never used.", "'handleSelectOptimal' is assigned a value but never used.", ["1668", "1669"], ["1670", "1671"], "'renderPagination' is assigned a value but never used.", "'pages' is assigned a value but never used.", "prefer-const", "'endPage' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1672", "text": "1673"}, "'index' is defined but never used.", "'e' is defined but never used.", "'nextBobinaNumber' is assigned a value but never used.", "'isFirstInsertion' is assigned a value but never used.", "'configurazioneFixed' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'checkFirstInsertion'. Either include it or remove the dependency array.", ["1674"], ["1675", "1676"], ["1677", "1678"], "React Hook useEffect has a missing dependency: 'validateForm'. Either include it or remove the dependency array.", ["1679"], ["1680", "1681"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1682", "1683", "1684", "1685"], ["1686", "1687", "1688", "1689"], ["1690", "1691", "1692", "1693"], "'useState' is defined but never used.", "'ContextMenuSub' is defined but never used.", "'ContextMenuSubContent' is defined but never used.", "'ContextMenuSubTrigger' is defined but never used.", "'Cable' is defined but never used.", "'formazioni' is assigned a value but never used.", "'bobine' is assigned a value but never used.", ["1694", "1695"], "'MoreHorizontal' is defined but never used.", "React Hook useMemo has missing dependencies: 'getCertificationButton', 'getConnectionButton', and 'getStatusButton'. Either include them or remove the dependency array.", ["1696"], "'getStatusBadge' is assigned a value but never used.", "'FileText' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadResponsabili'. Either include it or remove the dependency array.", ["1697"], ["1698", "1699"], ["1700", "1701"], ["1702"], ["1703", "1704"], ["1705", "1706"], ["1707", "1708"], ["1709"], ["1710", "1711"], ["1712", "1713"], ["1714", "1715"], ["1716", "1717"], ["1718", "1719", "1720", "1721"], ["1722", "1723"], "'incompatibilities' is never reassigned. Use 'const' instead.", {"range": "1724", "text": "1725"}, ["1726", "1727", "1728", "1729"], ["1730", "1731", "1732", "1733"], ["1734"], "React Hook useEffect has a missing dependency: 'validateMetriP<PERSON>ti'. Either include it or remove the dependency array.", ["1735"], "'errors' is never reassigned. Use 'const' instead.", {"range": "1736", "text": "1737"}, "'warnings' is never reassigned. Use 'const' instead.", {"range": "1738", "text": "1739"}, ["1740", "1741"], ["1742", "1743"], ["1744", "1745"], ["1746", "1747"], ["1748", "1749"], ["1750", "1751"], ["1752", "1753"], ["1754", "1755"], "React Hook useEffect has a missing dependency: 'loadBobineCompatibili'. Either include it or remove the dependency array.", ["1756"], ["1757", "1758"], "'COMMAND_TYPES' is defined but never used.", ["1759"], ["1760", "1761"], ["1762", "1763"], ["1764", "1765"], "'onError' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComandaDettagli'. Either include it or remove the dependency array.", ["1766"], ["1767", "1768"], "'getProgressColor' is assigned a value but never used.", ["1769"], ["1770", "1771"], ["1772", "1773"], ["1774", "1775"], ["1776", "1777"], ["1778", "1779"], ["1780"], ["1781", "1782"], ["1783", "1784"], ["1785", "1786"], ["1787", "1788"], ["1789", "1790"], "'AlertTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCaviComanda'. Either include it or remove the dependency array.", ["1791"], ["1792"], ["1793", "1794"], ["1795", "1796"], ["1797", "1798"], "'ChevronDown' is defined but never used.", "'ChevronUp' is defined but never used.", ["1799", "1800"], ["1801", "1802"], ["1803", "1804"], ["1805", "1806"], ["1807", "1808"], ["1809", "1810"], "'cavi' is assigned a value but never used.", ["1811", "1812"], "React Hook useEffect has a missing dependency: 'loadCaviForDebug'. Either include it or remove the dependency array.", ["1813"], ["1814", "1815", "1816", "1817"], ["1818", "1819", "1820", "1821"], ["1822", "1823", "1824", "1825"], ["1826", "1827", "1828", "1829"], ["1830", "1831", "1832", "1833"], ["1834", "1835", "1836", "1837"], ["1838", "1839", "1840", "1841"], ["1842", "1843", "1844", "1845"], ["1846", "1847", "1848", "1849"], ["1850", "1851", "1852", "1853"], ["1854", "1855", "1856", "1857"], ["1858", "1859", "1860", "1861"], ["1862", "1863", "1864", "1865"], ["1866", "1867", "1868", "1869"], ["1870", "1871", "1872", "1873"], ["1874", "1875", "1876", "1877"], ["1878", "1879", "1880", "1881"], ["1882", "1883", "1884", "1885"], "'Database' is defined but never used.", ["1886", "1887"], ["1888", "1889"], ["1890", "1891"], "React Hook useEffect has a missing dependency: 'loadDebugData'. Either include it or remove the dependency array.", ["1892"], ["1893", "1894"], ["1895", "1896"], ["1897", "1898"], "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'caviApi' is defined but never used.", "'Cavo' is defined but never used.", ["1899", "1900"], "'cantiere' is assigned a value but never used.", ["1901", "1902"], "React Hook useEffect has a missing dependency: 'analyzeMetriPosati'. Either include it or remove the dependency array.", ["1903"], ["1904", "1905"], ["1906", "1907", "1908", "1909"], ["1910", "1911", "1912", "1913"], ["1914", "1915", "1916", "1917"], ["1918", "1919", "1920", "1921"], ["1922", "1923", "1924", "1925"], ["1926", "1927"], "'createPortal' is defined but never used.", "'dropdownPosition' is assigned a value but never used.", "'actionName' is defined but never used.", ["1928", "1929"], ["1930", "1931"], "'rect' is assigned a value but never used.", "'onClose' is defined but never used.", "'ref' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["1932"], ["1933", "1934"], ["1935", "1936"], ["1937", "1938"], "'tokenError' is defined but never used.", ["1939", "1940"], ["1941", "1942"], ["1943", "1944"], ["1945", "1946"], ["1947", "1948"], ["1949", "1950"], ["1951", "1952"], ["1953", "1954"], ["1955", "1956"], ["1957", "1958"], ["1959", "1960"], ["1961", "1962"], ["1963", "1964"], ["1965", "1966"], ["1967", "1968"], ["1969", "1970"], ["1971", "1972"], ["1973", "1974"], ["1975", "1976"], ["1977", "1978"], ["1979", "1980"], ["1981", "1982"], ["1983", "1984"], ["1985", "1986"], ["1987", "1988"], ["1989", "1990"], ["1991", "1992"], ["1993", "1994"], ["1995", "1996"], ["1997", "1998"], ["1999", "2000"], ["2001", "2002"], ["2003", "2004"], ["2005", "2006"], ["2007", "2008"], ["2009", "2010"], ["2011", "2012"], ["2013", "2014"], ["2015", "2016"], ["2017", "2018"], ["2019", "2020"], ["2021", "2022"], ["2023", "2024"], ["2025", "2026"], ["2027", "2028"], ["2029", "2030"], ["2031", "2032"], ["2033", "2034"], ["2035", "2036"], ["2037", "2038"], ["2039", "2040"], ["2041", "2042"], ["2043", "2044"], ["2045", "2046"], ["2047", "2048"], ["2049", "2050"], ["2051", "2052"], ["2053", "2054"], ["2055", "2056"], ["2057", "2058"], ["2059", "2060"], ["2061", "2062"], ["2063", "2064"], ["2065", "2066"], ["2067", "2068"], ["2069", "2070"], ["2071", "2072"], ["2073", "2074"], ["2075", "2076"], ["2077", "2078"], ["2079", "2080"], ["2081", "2082"], ["2083", "2084"], ["2085", "2086"], ["2087", "2088"], ["2089", "2090"], ["2091", "2092"], ["2093", "2094"], ["2095", "2096"], ["2097", "2098"], ["2099", "2100"], ["2101", "2102"], ["2103", "2104"], ["2105", "2106"], ["2107", "2108"], ["2109", "2110"], ["2111", "2112"], ["2113", "2114"], ["2115", "2116"], ["2117", "2118"], ["2119", "2120"], "'ip' is assigned a value but never used.", ["2121", "2122"], ["2123", "2124"], ["2125", "2126"], ["2127", "2128"], ["2129", "2130"], ["2131", "2132"], ["2133", "2134"], ["2135", "2136"], ["2137", "2138"], ["2139", "2140"], ["2141", "2142"], ["2143", "2144"], ["2145", "2146"], ["2147", "2148"], ["2149", "2150"], {"desc": "2151", "fix": "2152"}, {"messageId": "2153", "fix": "2154", "desc": "2155"}, {"messageId": "2156", "fix": "2157", "desc": "2158"}, {"messageId": "2153", "fix": "2159", "desc": "2155"}, {"messageId": "2156", "fix": "2160", "desc": "2158"}, {"messageId": "2153", "fix": "2161", "desc": "2155"}, {"messageId": "2156", "fix": "2162", "desc": "2158"}, {"messageId": "2153", "fix": "2163", "desc": "2155"}, {"messageId": "2156", "fix": "2164", "desc": "2158"}, {"messageId": "2165", "data": "2166", "fix": "2167", "desc": "2168"}, {"messageId": "2165", "data": "2169", "fix": "2170", "desc": "2171"}, {"messageId": "2165", "data": "2172", "fix": "2173", "desc": "2174"}, {"messageId": "2165", "data": "2175", "fix": "2176", "desc": "2177"}, {"messageId": "2165", "data": "2178", "fix": "2179", "desc": "2168"}, {"messageId": "2165", "data": "2180", "fix": "2181", "desc": "2171"}, {"messageId": "2165", "data": "2182", "fix": "2183", "desc": "2174"}, {"messageId": "2165", "data": "2184", "fix": "2185", "desc": "2177"}, {"desc": "2186", "fix": "2187"}, {"desc": "2188", "fix": "2189"}, {"messageId": "2153", "fix": "2190", "desc": "2155"}, {"messageId": "2156", "fix": "2191", "desc": "2158"}, {"messageId": "2153", "fix": "2192", "desc": "2155"}, {"messageId": "2156", "fix": "2193", "desc": "2158"}, {"messageId": "2153", "fix": "2194", "desc": "2155"}, {"messageId": "2156", "fix": "2195", "desc": "2158"}, {"messageId": "2153", "fix": "2196", "desc": "2155"}, {"messageId": "2156", "fix": "2197", "desc": "2158"}, {"desc": "2198", "fix": "2199"}, {"messageId": "2153", "fix": "2200", "desc": "2155"}, {"messageId": "2156", "fix": "2201", "desc": "2158"}, {"desc": "2202", "fix": "2203"}, {"messageId": "2153", "fix": "2204", "desc": "2155"}, {"messageId": "2156", "fix": "2205", "desc": "2158"}, {"messageId": "2153", "fix": "2206", "desc": "2155"}, {"messageId": "2156", "fix": "2207", "desc": "2158"}, {"messageId": "2153", "fix": "2208", "desc": "2155"}, {"messageId": "2156", "fix": "2209", "desc": "2158"}, {"messageId": "2153", "fix": "2210", "desc": "2155"}, {"messageId": "2156", "fix": "2211", "desc": "2158"}, {"desc": "2212", "fix": "2213"}, {"messageId": "2153", "fix": "2214", "desc": "2155"}, {"messageId": "2156", "fix": "2215", "desc": "2158"}, {"messageId": "2153", "fix": "2216", "desc": "2155"}, {"messageId": "2156", "fix": "2217", "desc": "2158"}, {"messageId": "2153", "fix": "2218", "desc": "2155"}, {"messageId": "2156", "fix": "2219", "desc": "2158"}, {"messageId": "2153", "fix": "2220", "desc": "2155"}, {"messageId": "2156", "fix": "2221", "desc": "2158"}, {"messageId": "2153", "fix": "2222", "desc": "2155"}, {"messageId": "2156", "fix": "2223", "desc": "2158"}, {"messageId": "2153", "fix": "2224", "desc": "2155"}, {"messageId": "2156", "fix": "2225", "desc": "2158"}, {"messageId": "2153", "fix": "2226", "desc": "2155"}, {"messageId": "2156", "fix": "2227", "desc": "2158"}, {"messageId": "2153", "fix": "2228", "desc": "2155"}, {"messageId": "2156", "fix": "2229", "desc": "2158"}, {"messageId": "2153", "fix": "2230", "desc": "2155"}, {"messageId": "2156", "fix": "2231", "desc": "2158"}, {"messageId": "2153", "fix": "2232", "desc": "2155"}, {"messageId": "2156", "fix": "2233", "desc": "2158"}, {"messageId": "2153", "fix": "2234", "desc": "2155"}, {"messageId": "2156", "fix": "2235", "desc": "2158"}, {"messageId": "2153", "fix": "2236", "desc": "2155"}, {"messageId": "2156", "fix": "2237", "desc": "2158"}, {"messageId": "2153", "fix": "2238", "desc": "2155"}, {"messageId": "2156", "fix": "2239", "desc": "2158"}, {"messageId": "2153", "fix": "2240", "desc": "2155"}, {"messageId": "2156", "fix": "2241", "desc": "2158"}, {"messageId": "2153", "fix": "2242", "desc": "2155"}, {"messageId": "2156", "fix": "2243", "desc": "2158"}, {"messageId": "2153", "fix": "2244", "desc": "2155"}, {"messageId": "2156", "fix": "2245", "desc": "2158"}, {"messageId": "2153", "fix": "2246", "desc": "2155"}, {"messageId": "2156", "fix": "2247", "desc": "2158"}, {"messageId": "2153", "fix": "2248", "desc": "2155"}, {"messageId": "2156", "fix": "2249", "desc": "2158"}, {"messageId": "2165", "data": "2250", "fix": "2251", "desc": "2168"}, {"messageId": "2165", "data": "2252", "fix": "2253", "desc": "2171"}, {"messageId": "2165", "data": "2254", "fix": "2255", "desc": "2174"}, {"messageId": "2165", "data": "2256", "fix": "2257", "desc": "2177"}, {"messageId": "2165", "data": "2258", "fix": "2259", "desc": "2168"}, {"messageId": "2165", "data": "2260", "fix": "2261", "desc": "2171"}, {"messageId": "2165", "data": "2262", "fix": "2263", "desc": "2174"}, {"messageId": "2165", "data": "2264", "fix": "2265", "desc": "2177"}, {"messageId": "2153", "fix": "2266", "desc": "2155"}, {"messageId": "2156", "fix": "2267", "desc": "2158"}, {"messageId": "2165", "data": "2268", "fix": "2269", "desc": "2168"}, {"messageId": "2165", "data": "2270", "fix": "2271", "desc": "2171"}, {"messageId": "2165", "data": "2272", "fix": "2273", "desc": "2174"}, {"messageId": "2165", "data": "2274", "fix": "2275", "desc": "2177"}, {"messageId": "2165", "data": "2276", "fix": "2277", "desc": "2168"}, {"messageId": "2165", "data": "2278", "fix": "2279", "desc": "2171"}, {"messageId": "2165", "data": "2280", "fix": "2281", "desc": "2174"}, {"messageId": "2165", "data": "2282", "fix": "2283", "desc": "2177"}, {"messageId": "2165", "data": "2284", "fix": "2285", "desc": "2168"}, {"messageId": "2165", "data": "2286", "fix": "2287", "desc": "2171"}, {"messageId": "2165", "data": "2288", "fix": "2289", "desc": "2174"}, {"messageId": "2165", "data": "2290", "fix": "2291", "desc": "2177"}, {"messageId": "2165", "data": "2292", "fix": "2293", "desc": "2168"}, {"messageId": "2165", "data": "2294", "fix": "2295", "desc": "2171"}, {"messageId": "2165", "data": "2296", "fix": "2297", "desc": "2174"}, {"messageId": "2165", "data": "2298", "fix": "2299", "desc": "2177"}, {"messageId": "2165", "data": "2300", "fix": "2301", "desc": "2168"}, {"messageId": "2165", "data": "2302", "fix": "2303", "desc": "2171"}, {"messageId": "2165", "data": "2304", "fix": "2305", "desc": "2174"}, {"messageId": "2165", "data": "2306", "fix": "2307", "desc": "2177"}, {"messageId": "2165", "data": "2308", "fix": "2309", "desc": "2168"}, {"messageId": "2165", "data": "2310", "fix": "2311", "desc": "2171"}, {"messageId": "2165", "data": "2312", "fix": "2313", "desc": "2174"}, {"messageId": "2165", "data": "2314", "fix": "2315", "desc": "2177"}, {"messageId": "2153", "fix": "2316", "desc": "2155"}, {"messageId": "2156", "fix": "2317", "desc": "2158"}, {"messageId": "2153", "fix": "2318", "desc": "2155"}, {"messageId": "2156", "fix": "2319", "desc": "2158"}, {"messageId": "2153", "fix": "2320", "desc": "2155"}, {"messageId": "2156", "fix": "2321", "desc": "2158"}, {"messageId": "2165", "data": "2322", "fix": "2323", "desc": "2168"}, {"messageId": "2165", "data": "2324", "fix": "2325", "desc": "2171"}, {"messageId": "2165", "data": "2326", "fix": "2327", "desc": "2174"}, {"messageId": "2165", "data": "2328", "fix": "2329", "desc": "2177"}, {"messageId": "2165", "data": "2330", "fix": "2331", "desc": "2168"}, {"messageId": "2165", "data": "2332", "fix": "2333", "desc": "2171"}, {"messageId": "2165", "data": "2334", "fix": "2335", "desc": "2174"}, {"messageId": "2165", "data": "2336", "fix": "2337", "desc": "2177"}, {"messageId": "2165", "data": "2338", "fix": "2339", "desc": "2168"}, {"messageId": "2165", "data": "2340", "fix": "2341", "desc": "2171"}, {"messageId": "2165", "data": "2342", "fix": "2343", "desc": "2174"}, {"messageId": "2165", "data": "2344", "fix": "2345", "desc": "2177"}, {"messageId": "2165", "data": "2346", "fix": "2347", "desc": "2168"}, {"messageId": "2165", "data": "2348", "fix": "2349", "desc": "2171"}, {"messageId": "2165", "data": "2350", "fix": "2351", "desc": "2174"}, {"messageId": "2165", "data": "2352", "fix": "2353", "desc": "2177"}, {"messageId": "2153", "fix": "2354", "desc": "2155"}, {"messageId": "2156", "fix": "2355", "desc": "2158"}, {"messageId": "2153", "fix": "2356", "desc": "2155"}, {"messageId": "2156", "fix": "2357", "desc": "2158"}, {"messageId": "2153", "fix": "2358", "desc": "2155"}, {"messageId": "2156", "fix": "2359", "desc": "2158"}, {"messageId": "2153", "fix": "2360", "desc": "2155"}, {"messageId": "2156", "fix": "2361", "desc": "2158"}, {"desc": "2362", "fix": "2363"}, {"messageId": "2153", "fix": "2364", "desc": "2155"}, {"messageId": "2156", "fix": "2365", "desc": "2158"}, {"messageId": "2153", "fix": "2366", "desc": "2155"}, {"messageId": "2156", "fix": "2367", "desc": "2158"}, [22626, 22707], "const endPage = Math.min(paginatedCavi.totalPages, startPage + maxVisiblePages - 1)", {"desc": "2368", "fix": "2369"}, {"messageId": "2153", "fix": "2370", "desc": "2155"}, {"messageId": "2156", "fix": "2371", "desc": "2158"}, {"messageId": "2153", "fix": "2372", "desc": "2155"}, {"messageId": "2156", "fix": "2373", "desc": "2158"}, {"desc": "2374", "fix": "2375"}, {"messageId": "2153", "fix": "2376", "desc": "2155"}, {"messageId": "2156", "fix": "2377", "desc": "2158"}, {"messageId": "2165", "data": "2378", "fix": "2379", "desc": "2380"}, {"messageId": "2165", "data": "2381", "fix": "2382", "desc": "2383"}, {"messageId": "2165", "data": "2384", "fix": "2385", "desc": "2386"}, {"messageId": "2165", "data": "2387", "fix": "2388", "desc": "2389"}, {"messageId": "2165", "data": "2390", "fix": "2391", "desc": "2380"}, {"messageId": "2165", "data": "2392", "fix": "2393", "desc": "2383"}, {"messageId": "2165", "data": "2394", "fix": "2395", "desc": "2386"}, {"messageId": "2165", "data": "2396", "fix": "2397", "desc": "2389"}, {"messageId": "2165", "data": "2398", "fix": "2399", "desc": "2168"}, {"messageId": "2165", "data": "2400", "fix": "2401", "desc": "2171"}, {"messageId": "2165", "data": "2402", "fix": "2403", "desc": "2174"}, {"messageId": "2165", "data": "2404", "fix": "2405", "desc": "2177"}, {"messageId": "2153", "fix": "2406", "desc": "2155"}, {"messageId": "2156", "fix": "2407", "desc": "2158"}, {"desc": "2408", "fix": "2409"}, {"desc": "2410", "fix": "2411"}, {"messageId": "2153", "fix": "2412", "desc": "2155"}, {"messageId": "2156", "fix": "2413", "desc": "2158"}, {"messageId": "2153", "fix": "2414", "desc": "2155"}, {"messageId": "2156", "fix": "2415", "desc": "2158"}, {"desc": "2410", "fix": "2416"}, {"messageId": "2153", "fix": "2417", "desc": "2155"}, {"messageId": "2156", "fix": "2418", "desc": "2158"}, {"messageId": "2153", "fix": "2419", "desc": "2155"}, {"messageId": "2156", "fix": "2420", "desc": "2158"}, {"messageId": "2153", "fix": "2421", "desc": "2155"}, {"messageId": "2156", "fix": "2422", "desc": "2158"}, {"desc": "2423", "fix": "2424"}, {"messageId": "2153", "fix": "2425", "desc": "2155"}, {"messageId": "2156", "fix": "2426", "desc": "2158"}, {"messageId": "2153", "fix": "2427", "desc": "2155"}, {"messageId": "2156", "fix": "2428", "desc": "2158"}, {"messageId": "2153", "fix": "2429", "desc": "2155"}, {"messageId": "2156", "fix": "2430", "desc": "2158"}, {"messageId": "2153", "fix": "2431", "desc": "2155"}, {"messageId": "2156", "fix": "2432", "desc": "2158"}, {"messageId": "2165", "data": "2433", "fix": "2434", "desc": "2168"}, {"messageId": "2165", "data": "2435", "fix": "2436", "desc": "2171"}, {"messageId": "2165", "data": "2437", "fix": "2438", "desc": "2174"}, {"messageId": "2165", "data": "2439", "fix": "2440", "desc": "2177"}, {"messageId": "2153", "fix": "2441", "desc": "2155"}, {"messageId": "2156", "fix": "2442", "desc": "2158"}, [1542, 1593], "const incompatibilities = propIncompatibilities || []", {"messageId": "2165", "data": "2443", "fix": "2444", "desc": "2168"}, {"messageId": "2165", "data": "2445", "fix": "2446", "desc": "2171"}, {"messageId": "2165", "data": "2447", "fix": "2448", "desc": "2174"}, {"messageId": "2165", "data": "2449", "fix": "2450", "desc": "2177"}, {"messageId": "2165", "data": "2451", "fix": "2452", "desc": "2168"}, {"messageId": "2165", "data": "2453", "fix": "2454", "desc": "2171"}, {"messageId": "2165", "data": "2455", "fix": "2456", "desc": "2174"}, {"messageId": "2165", "data": "2457", "fix": "2458", "desc": "2177"}, {"desc": "2459", "fix": "2460"}, {"desc": "2461", "fix": "2462"}, [3372, 3414], "const errors: FormErrors = { ...formErrors }", [3419, 3467], "const warnings: FormWarnings = { ...formWarnings }", {"messageId": "2153", "fix": "2463", "desc": "2155"}, {"messageId": "2156", "fix": "2464", "desc": "2158"}, {"messageId": "2153", "fix": "2465", "desc": "2155"}, {"messageId": "2156", "fix": "2466", "desc": "2158"}, {"messageId": "2153", "fix": "2467", "desc": "2155"}, {"messageId": "2156", "fix": "2468", "desc": "2158"}, {"messageId": "2153", "fix": "2469", "desc": "2155"}, {"messageId": "2156", "fix": "2470", "desc": "2158"}, {"messageId": "2153", "fix": "2471", "desc": "2155"}, {"messageId": "2156", "fix": "2472", "desc": "2158"}, {"messageId": "2153", "fix": "2473", "desc": "2155"}, {"messageId": "2156", "fix": "2474", "desc": "2158"}, {"messageId": "2153", "fix": "2475", "desc": "2155"}, {"messageId": "2156", "fix": "2476", "desc": "2158"}, {"messageId": "2153", "fix": "2477", "desc": "2155"}, {"messageId": "2156", "fix": "2478", "desc": "2158"}, {"desc": "2479", "fix": "2480"}, {"messageId": "2153", "fix": "2481", "desc": "2155"}, {"messageId": "2156", "fix": "2482", "desc": "2158"}, {"desc": "2483", "fix": "2484"}, {"messageId": "2153", "fix": "2485", "desc": "2155"}, {"messageId": "2156", "fix": "2486", "desc": "2158"}, {"messageId": "2153", "fix": "2487", "desc": "2155"}, {"messageId": "2156", "fix": "2488", "desc": "2158"}, {"messageId": "2153", "fix": "2489", "desc": "2155"}, {"messageId": "2156", "fix": "2490", "desc": "2158"}, {"desc": "2491", "fix": "2492"}, {"messageId": "2153", "fix": "2493", "desc": "2155"}, {"messageId": "2156", "fix": "2494", "desc": "2158"}, {"desc": "2483", "fix": "2495"}, {"messageId": "2153", "fix": "2496", "desc": "2155"}, {"messageId": "2156", "fix": "2497", "desc": "2158"}, {"messageId": "2153", "fix": "2498", "desc": "2155"}, {"messageId": "2156", "fix": "2499", "desc": "2158"}, {"messageId": "2153", "fix": "2500", "desc": "2155"}, {"messageId": "2156", "fix": "2501", "desc": "2158"}, {"messageId": "2153", "fix": "2502", "desc": "2155"}, {"messageId": "2156", "fix": "2503", "desc": "2158"}, {"messageId": "2153", "fix": "2504", "desc": "2155"}, {"messageId": "2156", "fix": "2505", "desc": "2158"}, {"desc": "2506", "fix": "2507"}, {"messageId": "2153", "fix": "2508", "desc": "2155"}, {"messageId": "2156", "fix": "2509", "desc": "2158"}, {"messageId": "2153", "fix": "2510", "desc": "2155"}, {"messageId": "2156", "fix": "2511", "desc": "2158"}, {"messageId": "2153", "fix": "2512", "desc": "2155"}, {"messageId": "2156", "fix": "2513", "desc": "2158"}, {"messageId": "2153", "fix": "2514", "desc": "2155"}, {"messageId": "2156", "fix": "2515", "desc": "2158"}, {"messageId": "2153", "fix": "2516", "desc": "2155"}, {"messageId": "2156", "fix": "2517", "desc": "2158"}, {"desc": "2518", "fix": "2519"}, {"desc": "2520", "fix": "2521"}, {"messageId": "2153", "fix": "2522", "desc": "2155"}, {"messageId": "2156", "fix": "2523", "desc": "2158"}, {"messageId": "2153", "fix": "2524", "desc": "2155"}, {"messageId": "2156", "fix": "2525", "desc": "2158"}, {"messageId": "2153", "fix": "2526", "desc": "2155"}, {"messageId": "2156", "fix": "2527", "desc": "2158"}, {"messageId": "2153", "fix": "2528", "desc": "2155"}, {"messageId": "2156", "fix": "2529", "desc": "2158"}, {"messageId": "2153", "fix": "2530", "desc": "2155"}, {"messageId": "2156", "fix": "2531", "desc": "2158"}, {"messageId": "2153", "fix": "2532", "desc": "2155"}, {"messageId": "2156", "fix": "2533", "desc": "2158"}, {"messageId": "2153", "fix": "2534", "desc": "2155"}, {"messageId": "2156", "fix": "2535", "desc": "2158"}, {"messageId": "2153", "fix": "2536", "desc": "2155"}, {"messageId": "2156", "fix": "2537", "desc": "2158"}, {"messageId": "2153", "fix": "2538", "desc": "2155"}, {"messageId": "2156", "fix": "2539", "desc": "2158"}, {"messageId": "2153", "fix": "2540", "desc": "2155"}, {"messageId": "2156", "fix": "2541", "desc": "2158"}, {"desc": "2542", "fix": "2543"}, {"messageId": "2165", "data": "2544", "fix": "2545", "desc": "2380"}, {"messageId": "2165", "data": "2546", "fix": "2547", "desc": "2383"}, {"messageId": "2165", "data": "2548", "fix": "2549", "desc": "2386"}, {"messageId": "2165", "data": "2550", "fix": "2551", "desc": "2389"}, {"messageId": "2165", "data": "2552", "fix": "2553", "desc": "2380"}, {"messageId": "2165", "data": "2554", "fix": "2555", "desc": "2383"}, {"messageId": "2165", "data": "2556", "fix": "2557", "desc": "2386"}, {"messageId": "2165", "data": "2558", "fix": "2559", "desc": "2389"}, {"messageId": "2165", "data": "2560", "fix": "2561", "desc": "2380"}, {"messageId": "2165", "data": "2562", "fix": "2563", "desc": "2383"}, {"messageId": "2165", "data": "2564", "fix": "2565", "desc": "2386"}, {"messageId": "2165", "data": "2566", "fix": "2567", "desc": "2389"}, {"messageId": "2165", "data": "2568", "fix": "2569", "desc": "2380"}, {"messageId": "2165", "data": "2570", "fix": "2571", "desc": "2383"}, {"messageId": "2165", "data": "2572", "fix": "2573", "desc": "2386"}, {"messageId": "2165", "data": "2574", "fix": "2575", "desc": "2389"}, {"messageId": "2165", "data": "2576", "fix": "2577", "desc": "2380"}, {"messageId": "2165", "data": "2578", "fix": "2579", "desc": "2383"}, {"messageId": "2165", "data": "2580", "fix": "2581", "desc": "2386"}, {"messageId": "2165", "data": "2582", "fix": "2583", "desc": "2389"}, {"messageId": "2165", "data": "2584", "fix": "2585", "desc": "2380"}, {"messageId": "2165", "data": "2586", "fix": "2587", "desc": "2383"}, {"messageId": "2165", "data": "2588", "fix": "2589", "desc": "2386"}, {"messageId": "2165", "data": "2590", "fix": "2591", "desc": "2389"}, {"messageId": "2165", "data": "2592", "fix": "2593", "desc": "2380"}, {"messageId": "2165", "data": "2594", "fix": "2595", "desc": "2383"}, {"messageId": "2165", "data": "2596", "fix": "2597", "desc": "2386"}, {"messageId": "2165", "data": "2598", "fix": "2599", "desc": "2389"}, {"messageId": "2165", "data": "2600", "fix": "2601", "desc": "2380"}, {"messageId": "2165", "data": "2602", "fix": "2603", "desc": "2383"}, {"messageId": "2165", "data": "2604", "fix": "2605", "desc": "2386"}, {"messageId": "2165", "data": "2606", "fix": "2607", "desc": "2389"}, {"messageId": "2165", "data": "2608", "fix": "2609", "desc": "2380"}, {"messageId": "2165", "data": "2610", "fix": "2611", "desc": "2383"}, {"messageId": "2165", "data": "2612", "fix": "2613", "desc": "2386"}, {"messageId": "2165", "data": "2614", "fix": "2615", "desc": "2389"}, {"messageId": "2165", "data": "2616", "fix": "2617", "desc": "2380"}, {"messageId": "2165", "data": "2618", "fix": "2619", "desc": "2383"}, {"messageId": "2165", "data": "2620", "fix": "2621", "desc": "2386"}, {"messageId": "2165", "data": "2622", "fix": "2623", "desc": "2389"}, {"messageId": "2165", "data": "2624", "fix": "2625", "desc": "2380"}, {"messageId": "2165", "data": "2626", "fix": "2627", "desc": "2383"}, {"messageId": "2165", "data": "2628", "fix": "2629", "desc": "2386"}, {"messageId": "2165", "data": "2630", "fix": "2631", "desc": "2389"}, {"messageId": "2165", "data": "2632", "fix": "2633", "desc": "2380"}, {"messageId": "2165", "data": "2634", "fix": "2635", "desc": "2383"}, {"messageId": "2165", "data": "2636", "fix": "2637", "desc": "2386"}, {"messageId": "2165", "data": "2638", "fix": "2639", "desc": "2389"}, {"messageId": "2165", "data": "2640", "fix": "2641", "desc": "2380"}, {"messageId": "2165", "data": "2642", "fix": "2643", "desc": "2383"}, {"messageId": "2165", "data": "2644", "fix": "2645", "desc": "2386"}, {"messageId": "2165", "data": "2646", "fix": "2647", "desc": "2389"}, {"messageId": "2165", "data": "2648", "fix": "2649", "desc": "2380"}, {"messageId": "2165", "data": "2650", "fix": "2651", "desc": "2383"}, {"messageId": "2165", "data": "2652", "fix": "2653", "desc": "2386"}, {"messageId": "2165", "data": "2654", "fix": "2655", "desc": "2389"}, {"messageId": "2165", "data": "2656", "fix": "2657", "desc": "2380"}, {"messageId": "2165", "data": "2658", "fix": "2659", "desc": "2383"}, {"messageId": "2165", "data": "2660", "fix": "2661", "desc": "2386"}, {"messageId": "2165", "data": "2662", "fix": "2663", "desc": "2389"}, {"messageId": "2165", "data": "2664", "fix": "2665", "desc": "2380"}, {"messageId": "2165", "data": "2666", "fix": "2667", "desc": "2383"}, {"messageId": "2165", "data": "2668", "fix": "2669", "desc": "2386"}, {"messageId": "2165", "data": "2670", "fix": "2671", "desc": "2389"}, {"messageId": "2165", "data": "2672", "fix": "2673", "desc": "2380"}, {"messageId": "2165", "data": "2674", "fix": "2675", "desc": "2383"}, {"messageId": "2165", "data": "2676", "fix": "2677", "desc": "2386"}, {"messageId": "2165", "data": "2678", "fix": "2679", "desc": "2389"}, {"messageId": "2165", "data": "2680", "fix": "2681", "desc": "2380"}, {"messageId": "2165", "data": "2682", "fix": "2683", "desc": "2383"}, {"messageId": "2165", "data": "2684", "fix": "2685", "desc": "2386"}, {"messageId": "2165", "data": "2686", "fix": "2687", "desc": "2389"}, {"messageId": "2153", "fix": "2688", "desc": "2155"}, {"messageId": "2156", "fix": "2689", "desc": "2158"}, {"messageId": "2153", "fix": "2690", "desc": "2155"}, {"messageId": "2156", "fix": "2691", "desc": "2158"}, {"messageId": "2153", "fix": "2692", "desc": "2155"}, {"messageId": "2156", "fix": "2693", "desc": "2158"}, {"desc": "2694", "fix": "2695"}, {"messageId": "2153", "fix": "2696", "desc": "2155"}, {"messageId": "2156", "fix": "2697", "desc": "2158"}, {"messageId": "2153", "fix": "2698", "desc": "2155"}, {"messageId": "2156", "fix": "2699", "desc": "2158"}, {"messageId": "2153", "fix": "2700", "desc": "2155"}, {"messageId": "2156", "fix": "2701", "desc": "2158"}, {"messageId": "2153", "fix": "2702", "desc": "2155"}, {"messageId": "2156", "fix": "2703", "desc": "2158"}, {"messageId": "2153", "fix": "2704", "desc": "2155"}, {"messageId": "2156", "fix": "2705", "desc": "2158"}, {"desc": "2706", "fix": "2707"}, {"messageId": "2153", "fix": "2708", "desc": "2155"}, {"messageId": "2156", "fix": "2709", "desc": "2158"}, {"messageId": "2165", "data": "2710", "fix": "2711", "desc": "2168"}, {"messageId": "2165", "data": "2712", "fix": "2713", "desc": "2171"}, {"messageId": "2165", "data": "2714", "fix": "2715", "desc": "2174"}, {"messageId": "2165", "data": "2716", "fix": "2717", "desc": "2177"}, {"messageId": "2165", "data": "2718", "fix": "2719", "desc": "2380"}, {"messageId": "2165", "data": "2720", "fix": "2721", "desc": "2383"}, {"messageId": "2165", "data": "2722", "fix": "2723", "desc": "2386"}, {"messageId": "2165", "data": "2724", "fix": "2725", "desc": "2389"}, {"messageId": "2165", "data": "2726", "fix": "2727", "desc": "2380"}, {"messageId": "2165", "data": "2728", "fix": "2729", "desc": "2383"}, {"messageId": "2165", "data": "2730", "fix": "2731", "desc": "2386"}, {"messageId": "2165", "data": "2732", "fix": "2733", "desc": "2389"}, {"messageId": "2165", "data": "2734", "fix": "2735", "desc": "2168"}, {"messageId": "2165", "data": "2736", "fix": "2737", "desc": "2171"}, {"messageId": "2165", "data": "2738", "fix": "2739", "desc": "2174"}, {"messageId": "2165", "data": "2740", "fix": "2741", "desc": "2177"}, {"messageId": "2165", "data": "2742", "fix": "2743", "desc": "2168"}, {"messageId": "2165", "data": "2744", "fix": "2745", "desc": "2171"}, {"messageId": "2165", "data": "2746", "fix": "2747", "desc": "2174"}, {"messageId": "2165", "data": "2748", "fix": "2749", "desc": "2177"}, {"messageId": "2153", "fix": "2750", "desc": "2155"}, {"messageId": "2156", "fix": "2751", "desc": "2158"}, {"messageId": "2153", "fix": "2752", "desc": "2155"}, {"messageId": "2156", "fix": "2753", "desc": "2158"}, {"messageId": "2153", "fix": "2754", "desc": "2155"}, {"messageId": "2156", "fix": "2755", "desc": "2158"}, {"messageId": "2756", "fix": "2757", "desc": "2758"}, {"messageId": "2153", "fix": "2759", "desc": "2155"}, {"messageId": "2156", "fix": "2760", "desc": "2158"}, {"messageId": "2153", "fix": "2761", "desc": "2155"}, {"messageId": "2156", "fix": "2762", "desc": "2158"}, {"messageId": "2153", "fix": "2763", "desc": "2155"}, {"messageId": "2156", "fix": "2764", "desc": "2158"}, {"messageId": "2153", "fix": "2765", "desc": "2155"}, {"messageId": "2156", "fix": "2766", "desc": "2158"}, {"messageId": "2153", "fix": "2767", "desc": "2155"}, {"messageId": "2156", "fix": "2768", "desc": "2158"}, {"messageId": "2153", "fix": "2769", "desc": "2155"}, {"messageId": "2156", "fix": "2770", "desc": "2158"}, {"messageId": "2153", "fix": "2771", "desc": "2155"}, {"messageId": "2156", "fix": "2772", "desc": "2158"}, {"messageId": "2153", "fix": "2773", "desc": "2155"}, {"messageId": "2156", "fix": "2774", "desc": "2158"}, {"messageId": "2153", "fix": "2775", "desc": "2155"}, {"messageId": "2156", "fix": "2776", "desc": "2158"}, {"messageId": "2153", "fix": "2777", "desc": "2155"}, {"messageId": "2156", "fix": "2778", "desc": "2158"}, {"messageId": "2153", "fix": "2779", "desc": "2155"}, {"messageId": "2156", "fix": "2780", "desc": "2158"}, {"messageId": "2153", "fix": "2781", "desc": "2155"}, {"messageId": "2156", "fix": "2782", "desc": "2158"}, {"messageId": "2153", "fix": "2783", "desc": "2155"}, {"messageId": "2156", "fix": "2784", "desc": "2158"}, {"messageId": "2153", "fix": "2785", "desc": "2155"}, {"messageId": "2156", "fix": "2786", "desc": "2158"}, {"messageId": "2153", "fix": "2787", "desc": "2155"}, {"messageId": "2156", "fix": "2788", "desc": "2158"}, {"messageId": "2153", "fix": "2789", "desc": "2155"}, {"messageId": "2156", "fix": "2790", "desc": "2158"}, {"messageId": "2153", "fix": "2791", "desc": "2155"}, {"messageId": "2156", "fix": "2792", "desc": "2158"}, {"messageId": "2153", "fix": "2793", "desc": "2155"}, {"messageId": "2156", "fix": "2794", "desc": "2158"}, {"messageId": "2153", "fix": "2795", "desc": "2155"}, {"messageId": "2156", "fix": "2796", "desc": "2158"}, {"messageId": "2153", "fix": "2797", "desc": "2155"}, {"messageId": "2156", "fix": "2798", "desc": "2158"}, {"messageId": "2153", "fix": "2799", "desc": "2155"}, {"messageId": "2156", "fix": "2800", "desc": "2158"}, {"messageId": "2153", "fix": "2801", "desc": "2155"}, {"messageId": "2156", "fix": "2802", "desc": "2158"}, {"messageId": "2153", "fix": "2803", "desc": "2155"}, {"messageId": "2156", "fix": "2804", "desc": "2158"}, {"messageId": "2153", "fix": "2805", "desc": "2155"}, {"messageId": "2156", "fix": "2806", "desc": "2158"}, {"messageId": "2153", "fix": "2807", "desc": "2155"}, {"messageId": "2156", "fix": "2808", "desc": "2158"}, {"messageId": "2153", "fix": "2809", "desc": "2155"}, {"messageId": "2156", "fix": "2810", "desc": "2158"}, {"messageId": "2153", "fix": "2811", "desc": "2155"}, {"messageId": "2156", "fix": "2812", "desc": "2158"}, {"messageId": "2153", "fix": "2813", "desc": "2155"}, {"messageId": "2156", "fix": "2814", "desc": "2158"}, {"messageId": "2153", "fix": "2815", "desc": "2155"}, {"messageId": "2156", "fix": "2816", "desc": "2158"}, {"messageId": "2153", "fix": "2817", "desc": "2155"}, {"messageId": "2156", "fix": "2818", "desc": "2158"}, {"messageId": "2153", "fix": "2819", "desc": "2155"}, {"messageId": "2156", "fix": "2820", "desc": "2158"}, {"messageId": "2153", "fix": "2821", "desc": "2155"}, {"messageId": "2156", "fix": "2822", "desc": "2158"}, {"messageId": "2153", "fix": "2823", "desc": "2155"}, {"messageId": "2156", "fix": "2824", "desc": "2158"}, {"messageId": "2153", "fix": "2825", "desc": "2155"}, {"messageId": "2156", "fix": "2826", "desc": "2158"}, {"messageId": "2153", "fix": "2827", "desc": "2155"}, {"messageId": "2156", "fix": "2828", "desc": "2158"}, {"messageId": "2153", "fix": "2829", "desc": "2155"}, {"messageId": "2156", "fix": "2830", "desc": "2158"}, {"messageId": "2153", "fix": "2831", "desc": "2155"}, {"messageId": "2156", "fix": "2832", "desc": "2158"}, {"messageId": "2153", "fix": "2833", "desc": "2155"}, {"messageId": "2156", "fix": "2834", "desc": "2158"}, {"messageId": "2153", "fix": "2835", "desc": "2155"}, {"messageId": "2156", "fix": "2836", "desc": "2158"}, {"messageId": "2153", "fix": "2837", "desc": "2155"}, {"messageId": "2156", "fix": "2838", "desc": "2158"}, {"messageId": "2153", "fix": "2839", "desc": "2155"}, {"messageId": "2156", "fix": "2840", "desc": "2158"}, {"messageId": "2153", "fix": "2841", "desc": "2155"}, {"messageId": "2156", "fix": "2842", "desc": "2158"}, {"messageId": "2153", "fix": "2843", "desc": "2155"}, {"messageId": "2156", "fix": "2844", "desc": "2158"}, {"messageId": "2153", "fix": "2845", "desc": "2155"}, {"messageId": "2156", "fix": "2846", "desc": "2158"}, {"messageId": "2153", "fix": "2847", "desc": "2155"}, {"messageId": "2156", "fix": "2848", "desc": "2158"}, {"messageId": "2153", "fix": "2849", "desc": "2155"}, {"messageId": "2156", "fix": "2850", "desc": "2158"}, {"messageId": "2153", "fix": "2851", "desc": "2155"}, {"messageId": "2156", "fix": "2852", "desc": "2158"}, {"messageId": "2153", "fix": "2853", "desc": "2155"}, {"messageId": "2156", "fix": "2854", "desc": "2158"}, {"messageId": "2153", "fix": "2855", "desc": "2155"}, {"messageId": "2156", "fix": "2856", "desc": "2158"}, {"messageId": "2153", "fix": "2857", "desc": "2155"}, {"messageId": "2156", "fix": "2858", "desc": "2158"}, {"messageId": "2153", "fix": "2859", "desc": "2155"}, {"messageId": "2156", "fix": "2860", "desc": "2158"}, {"messageId": "2153", "fix": "2861", "desc": "2155"}, {"messageId": "2156", "fix": "2862", "desc": "2158"}, {"messageId": "2153", "fix": "2863", "desc": "2155"}, {"messageId": "2156", "fix": "2864", "desc": "2158"}, {"messageId": "2153", "fix": "2865", "desc": "2155"}, {"messageId": "2156", "fix": "2866", "desc": "2158"}, {"messageId": "2153", "fix": "2867", "desc": "2155"}, {"messageId": "2156", "fix": "2868", "desc": "2158"}, {"messageId": "2153", "fix": "2869", "desc": "2155"}, {"messageId": "2156", "fix": "2870", "desc": "2158"}, {"messageId": "2153", "fix": "2871", "desc": "2155"}, {"messageId": "2156", "fix": "2872", "desc": "2158"}, {"messageId": "2153", "fix": "2873", "desc": "2155"}, {"messageId": "2156", "fix": "2874", "desc": "2158"}, {"messageId": "2153", "fix": "2875", "desc": "2155"}, {"messageId": "2156", "fix": "2876", "desc": "2158"}, {"messageId": "2153", "fix": "2877", "desc": "2155"}, {"messageId": "2156", "fix": "2878", "desc": "2158"}, {"messageId": "2153", "fix": "2879", "desc": "2155"}, {"messageId": "2156", "fix": "2880", "desc": "2158"}, {"messageId": "2153", "fix": "2881", "desc": "2155"}, {"messageId": "2156", "fix": "2882", "desc": "2158"}, {"messageId": "2153", "fix": "2883", "desc": "2155"}, {"messageId": "2156", "fix": "2884", "desc": "2158"}, {"messageId": "2153", "fix": "2885", "desc": "2155"}, {"messageId": "2156", "fix": "2886", "desc": "2158"}, {"messageId": "2153", "fix": "2887", "desc": "2155"}, {"messageId": "2156", "fix": "2888", "desc": "2158"}, {"messageId": "2153", "fix": "2889", "desc": "2155"}, {"messageId": "2156", "fix": "2890", "desc": "2158"}, {"messageId": "2153", "fix": "2891", "desc": "2155"}, {"messageId": "2156", "fix": "2892", "desc": "2158"}, {"messageId": "2153", "fix": "2893", "desc": "2155"}, {"messageId": "2156", "fix": "2894", "desc": "2158"}, {"messageId": "2153", "fix": "2895", "desc": "2155"}, {"messageId": "2156", "fix": "2896", "desc": "2158"}, {"messageId": "2153", "fix": "2897", "desc": "2155"}, {"messageId": "2156", "fix": "2898", "desc": "2158"}, {"messageId": "2153", "fix": "2899", "desc": "2155"}, {"messageId": "2156", "fix": "2900", "desc": "2158"}, {"messageId": "2153", "fix": "2901", "desc": "2155"}, {"messageId": "2156", "fix": "2902", "desc": "2158"}, {"messageId": "2153", "fix": "2903", "desc": "2155"}, {"messageId": "2156", "fix": "2904", "desc": "2158"}, {"messageId": "2153", "fix": "2905", "desc": "2155"}, {"messageId": "2156", "fix": "2906", "desc": "2158"}, {"messageId": "2153", "fix": "2907", "desc": "2155"}, {"messageId": "2156", "fix": "2908", "desc": "2158"}, {"messageId": "2153", "fix": "2909", "desc": "2155"}, {"messageId": "2156", "fix": "2910", "desc": "2158"}, {"messageId": "2153", "fix": "2911", "desc": "2155"}, {"messageId": "2156", "fix": "2912", "desc": "2158"}, {"messageId": "2153", "fix": "2913", "desc": "2155"}, {"messageId": "2156", "fix": "2914", "desc": "2158"}, {"messageId": "2153", "fix": "2915", "desc": "2155"}, {"messageId": "2156", "fix": "2916", "desc": "2158"}, {"messageId": "2153", "fix": "2917", "desc": "2155"}, {"messageId": "2156", "fix": "2918", "desc": "2158"}, {"messageId": "2153", "fix": "2919", "desc": "2155"}, {"messageId": "2156", "fix": "2920", "desc": "2158"}, {"messageId": "2153", "fix": "2921", "desc": "2155"}, {"messageId": "2156", "fix": "2922", "desc": "2158"}, {"messageId": "2153", "fix": "2923", "desc": "2155"}, {"messageId": "2156", "fix": "2924", "desc": "2158"}, {"messageId": "2153", "fix": "2925", "desc": "2155"}, {"messageId": "2156", "fix": "2926", "desc": "2158"}, {"messageId": "2153", "fix": "2927", "desc": "2155"}, {"messageId": "2156", "fix": "2928", "desc": "2158"}, {"messageId": "2153", "fix": "2929", "desc": "2155"}, {"messageId": "2156", "fix": "2930", "desc": "2158"}, {"messageId": "2153", "fix": "2931", "desc": "2155"}, {"messageId": "2156", "fix": "2932", "desc": "2158"}, {"messageId": "2153", "fix": "2933", "desc": "2155"}, {"messageId": "2156", "fix": "2934", "desc": "2158"}, {"messageId": "2153", "fix": "2935", "desc": "2155"}, {"messageId": "2156", "fix": "2936", "desc": "2158"}, {"messageId": "2153", "fix": "2937", "desc": "2155"}, {"messageId": "2156", "fix": "2938", "desc": "2158"}, {"messageId": "2153", "fix": "2939", "desc": "2155"}, {"messageId": "2156", "fix": "2940", "desc": "2158"}, {"messageId": "2153", "fix": "2941", "desc": "2155"}, {"messageId": "2156", "fix": "2942", "desc": "2158"}, {"messageId": "2153", "fix": "2943", "desc": "2155"}, {"messageId": "2156", "fix": "2944", "desc": "2158"}, {"messageId": "2153", "fix": "2945", "desc": "2155"}, {"messageId": "2156", "fix": "2946", "desc": "2158"}, {"messageId": "2153", "fix": "2947", "desc": "2155"}, {"messageId": "2156", "fix": "2948", "desc": "2158"}, {"messageId": "2153", "fix": "2949", "desc": "2155"}, {"messageId": "2156", "fix": "2950", "desc": "2158"}, {"messageId": "2153", "fix": "2951", "desc": "2155"}, {"messageId": "2156", "fix": "2952", "desc": "2158"}, {"messageId": "2153", "fix": "2953", "desc": "2155"}, {"messageId": "2156", "fix": "2954", "desc": "2158"}, {"messageId": "2153", "fix": "2955", "desc": "2155"}, {"messageId": "2156", "fix": "2956", "desc": "2158"}, {"messageId": "2153", "fix": "2957", "desc": "2155"}, {"messageId": "2156", "fix": "2958", "desc": "2158"}, {"messageId": "2153", "fix": "2959", "desc": "2155"}, {"messageId": "2156", "fix": "2960", "desc": "2158"}, {"messageId": "2153", "fix": "2961", "desc": "2155"}, {"messageId": "2156", "fix": "2962", "desc": "2158"}, {"messageId": "2153", "fix": "2963", "desc": "2155"}, {"messageId": "2156", "fix": "2964", "desc": "2158"}, {"messageId": "2153", "fix": "2965", "desc": "2155"}, {"messageId": "2156", "fix": "2966", "desc": "2158"}, {"messageId": "2153", "fix": "2967", "desc": "2155"}, {"messageId": "2156", "fix": "2968", "desc": "2158"}, {"messageId": "2153", "fix": "2969", "desc": "2155"}, {"messageId": "2156", "fix": "2970", "desc": "2158"}, {"messageId": "2153", "fix": "2971", "desc": "2155"}, {"messageId": "2156", "fix": "2972", "desc": "2158"}, {"messageId": "2153", "fix": "2973", "desc": "2155"}, {"messageId": "2156", "fix": "2974", "desc": "2158"}, {"messageId": "2153", "fix": "2975", "desc": "2155"}, {"messageId": "2156", "fix": "2976", "desc": "2158"}, "Update the dependencies array to be: [activeTab, loadData]", {"range": "2977", "text": "2978"}, "suggestUnknown", {"range": "2979", "text": "2980"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2981", "text": "2982"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2983", "text": "2980"}, {"range": "2984", "text": "2982"}, {"range": "2985", "text": "2980"}, {"range": "2986", "text": "2982"}, {"range": "2987", "text": "2980"}, {"range": "2988", "text": "2982"}, "replaceWithAlt", {"alt": "2989"}, {"range": "2990", "text": "2991"}, "Replace with `&apos;`.", {"alt": "2992"}, {"range": "2993", "text": "2994"}, "Replace with `&lsquo;`.", {"alt": "2995"}, {"range": "2996", "text": "2997"}, "Replace with `&#39;`.", {"alt": "2998"}, {"range": "2999", "text": "3000"}, "Replace with `&rsquo;`.", {"alt": "2989"}, {"range": "3001", "text": "3002"}, {"alt": "2992"}, {"range": "3003", "text": "3004"}, {"alt": "2995"}, {"range": "3005", "text": "3006"}, {"alt": "2998"}, {"range": "3007", "text": "3008"}, "Update the dependencies array to be: [isAuthenticated, cantiereId, loadCantiere]", {"range": "3009", "text": "3010"}, "Update the dependencies array to be: [cantiereId, loadCavi, loadRevisioneCorrente]", {"range": "3011", "text": "3012"}, {"range": "3013", "text": "2980"}, {"range": "3014", "text": "2982"}, {"range": "3015", "text": "2980"}, {"range": "3016", "text": "2982"}, {"range": "3017", "text": "2980"}, {"range": "3018", "text": "2982"}, {"range": "3019", "text": "2980"}, {"range": "3020", "text": "2982"}, "Update the dependencies array to be: [loadCertificazioni]", {"range": "3021", "text": "3022"}, {"range": "3023", "text": "2980"}, {"range": "3024", "text": "2982"}, "Update the dependencies array to be: [cantiereId, loadData]", {"range": "3025", "text": "3026"}, {"range": "3027", "text": "2980"}, {"range": "3028", "text": "2982"}, {"range": "3029", "text": "2980"}, {"range": "3030", "text": "2982"}, {"range": "3031", "text": "2980"}, {"range": "3032", "text": "2982"}, {"range": "3033", "text": "2980"}, {"range": "3034", "text": "2982"}, "Update the dependencies array to be: [cantiereId, authLoading, loadBobine]", {"range": "3035", "text": "3036"}, {"range": "3037", "text": "2980"}, {"range": "3038", "text": "2982"}, {"range": "3039", "text": "2980"}, {"range": "3040", "text": "2982"}, {"range": "3041", "text": "2980"}, {"range": "3042", "text": "2982"}, {"range": "3043", "text": "2980"}, {"range": "3044", "text": "2982"}, {"range": "3045", "text": "2980"}, {"range": "3046", "text": "2982"}, {"range": "3047", "text": "2980"}, {"range": "3048", "text": "2982"}, {"range": "3049", "text": "2980"}, {"range": "3050", "text": "2982"}, {"range": "3051", "text": "2980"}, {"range": "3052", "text": "2982"}, {"range": "3053", "text": "2980"}, {"range": "3054", "text": "2982"}, {"range": "3055", "text": "2980"}, {"range": "3056", "text": "2982"}, {"range": "3057", "text": "2980"}, {"range": "3058", "text": "2982"}, {"range": "3059", "text": "2980"}, {"range": "3060", "text": "2982"}, {"range": "3061", "text": "2980"}, {"range": "3062", "text": "2982"}, {"range": "3063", "text": "2980"}, {"range": "3064", "text": "2982"}, {"range": "3065", "text": "2980"}, {"range": "3066", "text": "2982"}, {"range": "3067", "text": "2980"}, {"range": "3068", "text": "2982"}, {"range": "3069", "text": "2980"}, {"range": "3070", "text": "2982"}, {"range": "3071", "text": "2980"}, {"range": "3072", "text": "2982"}, {"alt": "2989"}, {"range": "3073", "text": "3074"}, {"alt": "2992"}, {"range": "3075", "text": "3076"}, {"alt": "2995"}, {"range": "3077", "text": "3078"}, {"alt": "2998"}, {"range": "3079", "text": "3080"}, {"alt": "2989"}, {"range": "3081", "text": "3082"}, {"alt": "2992"}, {"range": "3083", "text": "3084"}, {"alt": "2995"}, {"range": "3085", "text": "3086"}, {"alt": "2998"}, {"range": "3087", "text": "3088"}, {"range": "3089", "text": "2980"}, {"range": "3090", "text": "2982"}, {"alt": "2989"}, {"range": "3091", "text": "3092"}, {"alt": "2992"}, {"range": "3093", "text": "3094"}, {"alt": "2995"}, {"range": "3095", "text": "3096"}, {"alt": "2998"}, {"range": "3097", "text": "3098"}, {"alt": "2989"}, {"range": "3099", "text": "3100"}, {"alt": "2992"}, {"range": "3101", "text": "3102"}, {"alt": "2995"}, {"range": "3103", "text": "3104"}, {"alt": "2998"}, {"range": "3105", "text": "3106"}, {"alt": "2989"}, {"range": "3107", "text": "3108"}, {"alt": "2992"}, {"range": "3109", "text": "3110"}, {"alt": "2995"}, {"range": "3111", "text": "3112"}, {"alt": "2998"}, {"range": "3113", "text": "3114"}, {"alt": "2989"}, {"range": "3115", "text": "3116"}, {"alt": "2992"}, {"range": "3117", "text": "3118"}, {"alt": "2995"}, {"range": "3119", "text": "3120"}, {"alt": "2998"}, {"range": "3121", "text": "3122"}, {"alt": "2989"}, {"range": "3123", "text": "3124"}, {"alt": "2992"}, {"range": "3125", "text": "3126"}, {"alt": "2995"}, {"range": "3127", "text": "3128"}, {"alt": "2998"}, {"range": "3129", "text": "3130"}, {"alt": "2989"}, {"range": "3131", "text": "3132"}, {"alt": "2992"}, {"range": "3133", "text": "3134"}, {"alt": "2995"}, {"range": "3135", "text": "3136"}, {"alt": "2998"}, {"range": "3137", "text": "3138"}, {"range": "3139", "text": "2980"}, {"range": "3140", "text": "2982"}, {"range": "3141", "text": "2980"}, {"range": "3142", "text": "2982"}, {"range": "3143", "text": "2980"}, {"range": "3144", "text": "2982"}, {"alt": "2989"}, {"range": "3145", "text": "3146"}, {"alt": "2992"}, {"range": "3147", "text": "3148"}, {"alt": "2995"}, {"range": "3149", "text": "3150"}, {"alt": "2998"}, {"range": "3151", "text": "3152"}, {"alt": "2989"}, {"range": "3153", "text": "3154"}, {"alt": "2992"}, {"range": "3155", "text": "3156"}, {"alt": "2995"}, {"range": "3157", "text": "3158"}, {"alt": "2998"}, {"range": "3159", "text": "3160"}, {"alt": "2989"}, {"range": "3161", "text": "3162"}, {"alt": "2992"}, {"range": "3163", "text": "3164"}, {"alt": "2995"}, {"range": "3165", "text": "3166"}, {"alt": "2998"}, {"range": "3167", "text": "3168"}, {"alt": "2989"}, {"range": "3169", "text": "3170"}, {"alt": "2992"}, {"range": "3171", "text": "3172"}, {"alt": "2995"}, {"range": "3173", "text": "3174"}, {"alt": "2998"}, {"range": "3175", "text": "3176"}, {"range": "3177", "text": "2980"}, {"range": "3178", "text": "2982"}, {"range": "3179", "text": "2980"}, {"range": "3180", "text": "2982"}, {"range": "3181", "text": "2980"}, {"range": "3182", "text": "2982"}, {"range": "3183", "text": "2980"}, {"range": "3184", "text": "2982"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "3185", "text": "3186"}, {"range": "3187", "text": "2980"}, {"range": "3188", "text": "2982"}, {"range": "3189", "text": "2980"}, {"range": "3190", "text": "2982"}, "Update the dependencies array to be: [open, cantiereId, checkFirstInsertion]", {"range": "3191", "text": "3192"}, {"range": "3193", "text": "2980"}, {"range": "3194", "text": "2982"}, {"range": "3195", "text": "2980"}, {"range": "3196", "text": "2982"}, "Update the dependencies array to be: [formData, validateForm]", {"range": "3197", "text": "3198"}, {"range": "3199", "text": "2980"}, {"range": "3200", "text": "2982"}, {"alt": "3201"}, {"range": "3202", "text": "3203"}, "Replace with `&quot;`.", {"alt": "3204"}, {"range": "3205", "text": "3206"}, "Replace with `&ldquo;`.", {"alt": "3207"}, {"range": "3208", "text": "3209"}, "Replace with `&#34;`.", {"alt": "3210"}, {"range": "3211", "text": "3212"}, "Replace with `&rdquo;`.", {"alt": "3201"}, {"range": "3213", "text": "3214"}, {"alt": "3204"}, {"range": "3215", "text": "3216"}, {"alt": "3207"}, {"range": "3217", "text": "3218"}, {"alt": "3210"}, {"range": "3219", "text": "3220"}, {"alt": "2989"}, {"range": "3221", "text": "3222"}, {"alt": "2992"}, {"range": "3223", "text": "3224"}, {"alt": "2995"}, {"range": "3225", "text": "3226"}, {"alt": "2998"}, {"range": "3227", "text": "3228"}, {"range": "3229", "text": "2980"}, {"range": "3230", "text": "2982"}, "Update the dependencies array to be: [internalSelectionEnabled, getStatusButton, getConnectionButton, getCertificationButton, selectedCavi, filteredCavi.length, handleSelectAll, handleSelectCavo]", {"range": "3231", "text": "3232"}, "Update the dependencies array to be: [open, cavo, loadResponsabili]", {"range": "3233", "text": "3234"}, {"range": "3235", "text": "2980"}, {"range": "3236", "text": "2982"}, {"range": "3237", "text": "2980"}, {"range": "3238", "text": "2982"}, {"range": "3239", "text": "3234"}, {"range": "3240", "text": "2980"}, {"range": "3241", "text": "2982"}, {"range": "3242", "text": "2980"}, {"range": "3243", "text": "2982"}, {"range": "3244", "text": "2980"}, {"range": "3245", "text": "2982"}, "Update the dependencies array to be: [loadResponsabili, open, tipoComanda]", {"range": "3246", "text": "3247"}, {"range": "3248", "text": "2980"}, {"range": "3249", "text": "2982"}, {"range": "3250", "text": "2980"}, {"range": "3251", "text": "2982"}, {"range": "3252", "text": "2980"}, {"range": "3253", "text": "2982"}, {"range": "3254", "text": "2980"}, {"range": "3255", "text": "2982"}, {"alt": "2989"}, {"range": "3256", "text": "3257"}, {"alt": "2992"}, {"range": "3258", "text": "3259"}, {"alt": "2995"}, {"range": "3260", "text": "3261"}, {"alt": "2998"}, {"range": "3262", "text": "3263"}, {"range": "3264", "text": "2980"}, {"range": "3265", "text": "2982"}, {"alt": "2989"}, {"range": "3266", "text": "3267"}, {"alt": "2992"}, {"range": "3268", "text": "3269"}, {"alt": "2995"}, {"range": "3270", "text": "3271"}, {"alt": "2998"}, {"range": "3272", "text": "3273"}, {"alt": "2989"}, {"range": "3274", "text": "3275"}, {"alt": "2992"}, {"range": "3276", "text": "3277"}, {"alt": "2995"}, {"range": "3278", "text": "3279"}, {"alt": "2998"}, {"range": "3280", "text": "3281"}, "Update the dependencies array to be: [open, cavo, cantiere, loadBobine]", {"range": "3282", "text": "3283"}, "Update the dependencies array to be: [formData.metri_posati, cavo, validateMetriPosati]", {"range": "3284", "text": "3285"}, {"range": "3286", "text": "2980"}, {"range": "3287", "text": "2982"}, {"range": "3288", "text": "2980"}, {"range": "3289", "text": "2982"}, {"range": "3290", "text": "2980"}, {"range": "3291", "text": "2982"}, {"range": "3292", "text": "2980"}, {"range": "3293", "text": "2982"}, {"range": "3294", "text": "2980"}, {"range": "3295", "text": "2982"}, {"range": "3296", "text": "2980"}, {"range": "3297", "text": "2982"}, {"range": "3298", "text": "2980"}, {"range": "3299", "text": "2982"}, {"range": "3300", "text": "2980"}, {"range": "3301", "text": "2982"}, "Update the dependencies array to be: [open, cavo, cantiere, cantiereProp, cantiereAuth, loadBobineCompatibili]", {"range": "3302", "text": "3303"}, {"range": "3304", "text": "2980"}, {"range": "3305", "text": "2982"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabili]", {"range": "3306", "text": "3307"}, {"range": "3308", "text": "2980"}, {"range": "3309", "text": "2982"}, {"range": "3310", "text": "2980"}, {"range": "3311", "text": "2982"}, {"range": "3312", "text": "2980"}, {"range": "3313", "text": "2982"}, "Update the dependencies array to be: [open, codiceComanda, cantiereId, loadComandaDettagli]", {"range": "3314", "text": "3315"}, {"range": "3316", "text": "2980"}, {"range": "3317", "text": "2982"}, {"range": "3318", "text": "3307"}, {"range": "3319", "text": "2980"}, {"range": "3320", "text": "2982"}, {"range": "3321", "text": "2980"}, {"range": "3322", "text": "2982"}, {"range": "3323", "text": "2980"}, {"range": "3324", "text": "2982"}, {"range": "3325", "text": "2980"}, {"range": "3326", "text": "2982"}, {"range": "3327", "text": "2980"}, {"range": "3328", "text": "2982"}, "Update the dependencies array to be: [open, codiceComanda, loadCavi]", {"range": "3329", "text": "3330"}, {"range": "3331", "text": "2980"}, {"range": "3332", "text": "2982"}, {"range": "3333", "text": "2980"}, {"range": "3334", "text": "2982"}, {"range": "3335", "text": "2980"}, {"range": "3336", "text": "2982"}, {"range": "3337", "text": "2980"}, {"range": "3338", "text": "2982"}, {"range": "3339", "text": "2980"}, {"range": "3340", "text": "2982"}, "Update the dependencies array to be: [open, codiceComanda, loadCaviComanda]", {"range": "3341", "text": "3342"}, "Update the dependencies array to be: [open, cantiere?.id_cantiere, loadBobine]", {"range": "3343", "text": "3344"}, {"range": "3345", "text": "2980"}, {"range": "3346", "text": "2982"}, {"range": "3347", "text": "2980"}, {"range": "3348", "text": "2982"}, {"range": "3349", "text": "2980"}, {"range": "3350", "text": "2982"}, {"range": "3351", "text": "2980"}, {"range": "3352", "text": "2982"}, {"range": "3353", "text": "2980"}, {"range": "3354", "text": "2982"}, {"range": "3355", "text": "2980"}, {"range": "3356", "text": "2982"}, {"range": "3357", "text": "2980"}, {"range": "3358", "text": "2982"}, {"range": "3359", "text": "2980"}, {"range": "3360", "text": "2982"}, {"range": "3361", "text": "2980"}, {"range": "3362", "text": "2982"}, {"range": "3363", "text": "2980"}, {"range": "3364", "text": "2982"}, "Update the dependencies array to be: [open, bobina, cantiere, loadCaviForDebug]", {"range": "3365", "text": "3366"}, {"alt": "3201"}, {"range": "3367", "text": "3368"}, {"alt": "3204"}, {"range": "3369", "text": "3370"}, {"alt": "3207"}, {"range": "3371", "text": "3372"}, {"alt": "3210"}, {"range": "3373", "text": "3374"}, {"alt": "3201"}, {"range": "3375", "text": "3376"}, {"alt": "3204"}, {"range": "3377", "text": "3378"}, {"alt": "3207"}, {"range": "3379", "text": "3380"}, {"alt": "3210"}, {"range": "3381", "text": "3382"}, {"alt": "3201"}, {"range": "3383", "text": "3368"}, {"alt": "3204"}, {"range": "3384", "text": "3370"}, {"alt": "3207"}, {"range": "3385", "text": "3372"}, {"alt": "3210"}, {"range": "3386", "text": "3374"}, {"alt": "3201"}, {"range": "3387", "text": "3376"}, {"alt": "3204"}, {"range": "3388", "text": "3378"}, {"alt": "3207"}, {"range": "3389", "text": "3380"}, {"alt": "3210"}, {"range": "3390", "text": "3382"}, {"alt": "3201"}, {"range": "3391", "text": "3368"}, {"alt": "3204"}, {"range": "3392", "text": "3370"}, {"alt": "3207"}, {"range": "3393", "text": "3372"}, {"alt": "3210"}, {"range": "3394", "text": "3374"}, {"alt": "3201"}, {"range": "3395", "text": "3201"}, {"alt": "3204"}, {"range": "3396", "text": "3204"}, {"alt": "3207"}, {"range": "3397", "text": "3207"}, {"alt": "3210"}, {"range": "3398", "text": "3210"}, {"alt": "3201"}, {"range": "3399", "text": "3368"}, {"alt": "3204"}, {"range": "3400", "text": "3370"}, {"alt": "3207"}, {"range": "3401", "text": "3372"}, {"alt": "3210"}, {"range": "3402", "text": "3374"}, {"alt": "3201"}, {"range": "3403", "text": "3404"}, {"alt": "3204"}, {"range": "3405", "text": "3406"}, {"alt": "3207"}, {"range": "3407", "text": "3408"}, {"alt": "3210"}, {"range": "3409", "text": "3410"}, {"alt": "3201"}, {"range": "3411", "text": "3368"}, {"alt": "3204"}, {"range": "3412", "text": "3370"}, {"alt": "3207"}, {"range": "3413", "text": "3372"}, {"alt": "3210"}, {"range": "3414", "text": "3374"}, {"alt": "3201"}, {"range": "3415", "text": "3404"}, {"alt": "3204"}, {"range": "3416", "text": "3406"}, {"alt": "3207"}, {"range": "3417", "text": "3408"}, {"alt": "3210"}, {"range": "3418", "text": "3410"}, {"alt": "3201"}, {"range": "3419", "text": "3368"}, {"alt": "3204"}, {"range": "3420", "text": "3370"}, {"alt": "3207"}, {"range": "3421", "text": "3372"}, {"alt": "3210"}, {"range": "3422", "text": "3374"}, {"alt": "3201"}, {"range": "3423", "text": "3404"}, {"alt": "3204"}, {"range": "3424", "text": "3406"}, {"alt": "3207"}, {"range": "3425", "text": "3408"}, {"alt": "3210"}, {"range": "3426", "text": "3410"}, {"alt": "3201"}, {"range": "3427", "text": "3368"}, {"alt": "3204"}, {"range": "3428", "text": "3370"}, {"alt": "3207"}, {"range": "3429", "text": "3372"}, {"alt": "3210"}, {"range": "3430", "text": "3374"}, {"alt": "3201"}, {"range": "3431", "text": "3404"}, {"alt": "3204"}, {"range": "3432", "text": "3406"}, {"alt": "3207"}, {"range": "3433", "text": "3408"}, {"alt": "3210"}, {"range": "3434", "text": "3410"}, {"alt": "3201"}, {"range": "3435", "text": "3368"}, {"alt": "3204"}, {"range": "3436", "text": "3370"}, {"alt": "3207"}, {"range": "3437", "text": "3372"}, {"alt": "3210"}, {"range": "3438", "text": "3374"}, {"alt": "3201"}, {"range": "3439", "text": "3201"}, {"alt": "3204"}, {"range": "3440", "text": "3204"}, {"alt": "3207"}, {"range": "3441", "text": "3207"}, {"alt": "3210"}, {"range": "3442", "text": "3210"}, {"alt": "3201"}, {"range": "3443", "text": "3368"}, {"alt": "3204"}, {"range": "3444", "text": "3370"}, {"alt": "3207"}, {"range": "3445", "text": "3372"}, {"alt": "3210"}, {"range": "3446", "text": "3374"}, {"alt": "3201"}, {"range": "3447", "text": "3201"}, {"alt": "3204"}, {"range": "3448", "text": "3204"}, {"alt": "3207"}, {"range": "3449", "text": "3207"}, {"alt": "3210"}, {"range": "3450", "text": "3210"}, {"range": "3451", "text": "2980"}, {"range": "3452", "text": "2982"}, {"range": "3453", "text": "2980"}, {"range": "3454", "text": "2982"}, {"range": "3455", "text": "2980"}, {"range": "3456", "text": "2982"}, "Update the dependencies array to be: [open, cantiere, bobina, loadDebugData]", {"range": "3457", "text": "3458"}, {"range": "3459", "text": "2980"}, {"range": "3460", "text": "2982"}, {"range": "3461", "text": "2980"}, {"range": "3462", "text": "2982"}, {"range": "3463", "text": "2980"}, {"range": "3464", "text": "2982"}, {"range": "3465", "text": "2980"}, {"range": "3466", "text": "2982"}, {"range": "3467", "text": "2980"}, {"range": "3468", "text": "2982"}, "Update the dependencies array to be: [open, bobina, caviSelezionati, caviMetri, forceOver, analyzeMetriPosati]", {"range": "3469", "text": "3470"}, {"range": "3471", "text": "2980"}, {"range": "3472", "text": "2982"}, {"alt": "2989"}, {"range": "3473", "text": "3474"}, {"alt": "2992"}, {"range": "3475", "text": "3476"}, {"alt": "2995"}, {"range": "3477", "text": "3478"}, {"alt": "2998"}, {"range": "3479", "text": "3480"}, {"alt": "3201"}, {"range": "3481", "text": "3482"}, {"alt": "3204"}, {"range": "3483", "text": "3484"}, {"alt": "3207"}, {"range": "3485", "text": "3486"}, {"alt": "3210"}, {"range": "3487", "text": "3488"}, {"alt": "3201"}, {"range": "3489", "text": "3490"}, {"alt": "3204"}, {"range": "3491", "text": "3492"}, {"alt": "3207"}, {"range": "3493", "text": "3494"}, {"alt": "3210"}, {"range": "3495", "text": "3496"}, {"alt": "2989"}, {"range": "3497", "text": "3498"}, {"alt": "2992"}, {"range": "3499", "text": "3500"}, {"alt": "2995"}, {"range": "3501", "text": "3502"}, {"alt": "2998"}, {"range": "3503", "text": "3504"}, {"alt": "2989"}, {"range": "3505", "text": "3506"}, {"alt": "2992"}, {"range": "3507", "text": "3508"}, {"alt": "2995"}, {"range": "3509", "text": "3510"}, {"alt": "2998"}, {"range": "3511", "text": "3512"}, {"range": "3513", "text": "2980"}, {"range": "3514", "text": "2982"}, {"range": "3515", "text": "2980"}, {"range": "3516", "text": "2982"}, {"range": "3517", "text": "2980"}, {"range": "3518", "text": "2982"}, "replaceEmptyInterfaceWithSuper", {"range": "3519", "text": "3520"}, "Replace empty interface with a type alias.", {"range": "3521", "text": "2980"}, {"range": "3522", "text": "2982"}, {"range": "3523", "text": "2980"}, {"range": "3524", "text": "2982"}, {"range": "3525", "text": "2980"}, {"range": "3526", "text": "2982"}, {"range": "3527", "text": "2980"}, {"range": "3528", "text": "2982"}, {"range": "3529", "text": "2980"}, {"range": "3530", "text": "2982"}, {"range": "3531", "text": "2980"}, {"range": "3532", "text": "2982"}, {"range": "3533", "text": "2980"}, {"range": "3534", "text": "2982"}, {"range": "3535", "text": "2980"}, {"range": "3536", "text": "2982"}, {"range": "3537", "text": "2980"}, {"range": "3538", "text": "2982"}, {"range": "3539", "text": "2980"}, {"range": "3540", "text": "2982"}, {"range": "3541", "text": "2980"}, {"range": "3542", "text": "2982"}, {"range": "3543", "text": "2980"}, {"range": "3544", "text": "2982"}, {"range": "3545", "text": "2980"}, {"range": "3546", "text": "2982"}, {"range": "3547", "text": "2980"}, {"range": "3548", "text": "2982"}, {"range": "3549", "text": "2980"}, {"range": "3550", "text": "2982"}, {"range": "3551", "text": "2980"}, {"range": "3552", "text": "2982"}, {"range": "3553", "text": "2980"}, {"range": "3554", "text": "2982"}, {"range": "3555", "text": "2980"}, {"range": "3556", "text": "2982"}, {"range": "3557", "text": "2980"}, {"range": "3558", "text": "2982"}, {"range": "3559", "text": "2980"}, {"range": "3560", "text": "2982"}, {"range": "3561", "text": "2980"}, {"range": "3562", "text": "2982"}, {"range": "3563", "text": "2980"}, {"range": "3564", "text": "2982"}, {"range": "3565", "text": "2980"}, {"range": "3566", "text": "2982"}, {"range": "3567", "text": "2980"}, {"range": "3568", "text": "2982"}, {"range": "3569", "text": "2980"}, {"range": "3570", "text": "2982"}, {"range": "3571", "text": "2980"}, {"range": "3572", "text": "2982"}, {"range": "3573", "text": "2980"}, {"range": "3574", "text": "2982"}, {"range": "3575", "text": "2980"}, {"range": "3576", "text": "2982"}, {"range": "3577", "text": "2980"}, {"range": "3578", "text": "2982"}, {"range": "3579", "text": "2980"}, {"range": "3580", "text": "2982"}, {"range": "3581", "text": "2980"}, {"range": "3582", "text": "2982"}, {"range": "3583", "text": "2980"}, {"range": "3584", "text": "2982"}, {"range": "3585", "text": "2980"}, {"range": "3586", "text": "2982"}, {"range": "3587", "text": "2980"}, {"range": "3588", "text": "2982"}, {"range": "3589", "text": "2980"}, {"range": "3590", "text": "2982"}, {"range": "3591", "text": "2980"}, {"range": "3592", "text": "2982"}, {"range": "3593", "text": "2980"}, {"range": "3594", "text": "2982"}, {"range": "3595", "text": "2980"}, {"range": "3596", "text": "2982"}, {"range": "3597", "text": "2980"}, {"range": "3598", "text": "2982"}, {"range": "3599", "text": "2980"}, {"range": "3600", "text": "2982"}, {"range": "3601", "text": "2980"}, {"range": "3602", "text": "2982"}, {"range": "3603", "text": "2980"}, {"range": "3604", "text": "2982"}, {"range": "3605", "text": "2980"}, {"range": "3606", "text": "2982"}, {"range": "3607", "text": "2980"}, {"range": "3608", "text": "2982"}, {"range": "3609", "text": "2980"}, {"range": "3610", "text": "2982"}, {"range": "3611", "text": "2980"}, {"range": "3612", "text": "2982"}, {"range": "3613", "text": "2980"}, {"range": "3614", "text": "2982"}, {"range": "3615", "text": "2980"}, {"range": "3616", "text": "2982"}, {"range": "3617", "text": "2980"}, {"range": "3618", "text": "2982"}, {"range": "3619", "text": "2980"}, {"range": "3620", "text": "2982"}, {"range": "3621", "text": "2980"}, {"range": "3622", "text": "2982"}, {"range": "3623", "text": "2980"}, {"range": "3624", "text": "2982"}, {"range": "3625", "text": "2980"}, {"range": "3626", "text": "2982"}, {"range": "3627", "text": "2980"}, {"range": "3628", "text": "2982"}, {"range": "3629", "text": "2980"}, {"range": "3630", "text": "2982"}, {"range": "3631", "text": "2980"}, {"range": "3632", "text": "2982"}, {"range": "3633", "text": "2980"}, {"range": "3634", "text": "2982"}, {"range": "3635", "text": "2980"}, {"range": "3636", "text": "2982"}, {"range": "3637", "text": "2980"}, {"range": "3638", "text": "2982"}, {"range": "3639", "text": "2980"}, {"range": "3640", "text": "2982"}, {"range": "3641", "text": "2980"}, {"range": "3642", "text": "2982"}, {"range": "3643", "text": "2980"}, {"range": "3644", "text": "2982"}, {"range": "3645", "text": "2980"}, {"range": "3646", "text": "2982"}, {"range": "3647", "text": "2980"}, {"range": "3648", "text": "2982"}, {"range": "3649", "text": "2980"}, {"range": "3650", "text": "2982"}, {"range": "3651", "text": "2980"}, {"range": "3652", "text": "2982"}, {"range": "3653", "text": "2980"}, {"range": "3654", "text": "2982"}, {"range": "3655", "text": "2980"}, {"range": "3656", "text": "2982"}, {"range": "3657", "text": "2980"}, {"range": "3658", "text": "2982"}, {"range": "3659", "text": "2980"}, {"range": "3660", "text": "2982"}, {"range": "3661", "text": "2980"}, {"range": "3662", "text": "2982"}, {"range": "3663", "text": "2980"}, {"range": "3664", "text": "2982"}, {"range": "3665", "text": "2980"}, {"range": "3666", "text": "2982"}, {"range": "3667", "text": "2980"}, {"range": "3668", "text": "2982"}, {"range": "3669", "text": "2980"}, {"range": "3670", "text": "2982"}, {"range": "3671", "text": "2980"}, {"range": "3672", "text": "2982"}, {"range": "3673", "text": "2980"}, {"range": "3674", "text": "2982"}, {"range": "3675", "text": "2980"}, {"range": "3676", "text": "2982"}, {"range": "3677", "text": "2980"}, {"range": "3678", "text": "2982"}, {"range": "3679", "text": "2980"}, {"range": "3680", "text": "2982"}, {"range": "3681", "text": "2980"}, {"range": "3682", "text": "2982"}, {"range": "3683", "text": "2980"}, {"range": "3684", "text": "2982"}, {"range": "3685", "text": "2980"}, {"range": "3686", "text": "2982"}, {"range": "3687", "text": "2980"}, {"range": "3688", "text": "2982"}, {"range": "3689", "text": "2980"}, {"range": "3690", "text": "2982"}, {"range": "3691", "text": "2980"}, {"range": "3692", "text": "2982"}, {"range": "3693", "text": "2980"}, {"range": "3694", "text": "2982"}, {"range": "3695", "text": "2980"}, {"range": "3696", "text": "2982"}, {"range": "3697", "text": "2980"}, {"range": "3698", "text": "2982"}, {"range": "3699", "text": "2980"}, {"range": "3700", "text": "2982"}, {"range": "3701", "text": "2980"}, {"range": "3702", "text": "2982"}, {"range": "3703", "text": "2980"}, {"range": "3704", "text": "2982"}, {"range": "3705", "text": "2980"}, {"range": "3706", "text": "2982"}, {"range": "3707", "text": "2980"}, {"range": "3708", "text": "2982"}, {"range": "3709", "text": "2980"}, {"range": "3710", "text": "2982"}, {"range": "3711", "text": "2980"}, {"range": "3712", "text": "2982"}, {"range": "3713", "text": "2980"}, {"range": "3714", "text": "2982"}, {"range": "3715", "text": "2980"}, {"range": "3716", "text": "2982"}, {"range": "3717", "text": "2980"}, {"range": "3718", "text": "2982"}, {"range": "3719", "text": "2980"}, {"range": "3720", "text": "2982"}, {"range": "3721", "text": "2980"}, {"range": "3722", "text": "2982"}, {"range": "3723", "text": "2980"}, {"range": "3724", "text": "2982"}, {"range": "3725", "text": "2980"}, {"range": "3726", "text": "2982"}, {"range": "3727", "text": "2980"}, {"range": "3728", "text": "2982"}, {"range": "3729", "text": "2980"}, {"range": "3730", "text": "2982"}, {"range": "3731", "text": "2980"}, {"range": "3732", "text": "2982"}, {"range": "3733", "text": "2980"}, {"range": "3734", "text": "2982"}, {"range": "3735", "text": "2980"}, {"range": "3736", "text": "2982"}, {"range": "3737", "text": "2980"}, {"range": "3738", "text": "2982"}, [2161, 2172], "[activeTab, loadData]", [2627, 2630], "unknown", [2627, 2630], "never", [3088, 3091], [3088, 3091], [3438, 3441], [3438, 3441], [4230, 4233], [4230, 4233], "&apos;", [31262, 31376], "\n                      Invia la password all&apos;indirizzo email dell'amministratore del cantiere\n                    ", "&lsquo;", [31262, 31376], "\n                      Invia la password all&lsquo;indirizzo email dell'amministratore del cantiere\n                    ", "&#39;", [31262, 31376], "\n                      Invia la password all&#39;indirizzo email dell'amministratore del cantiere\n                    ", "&rsquo;", [31262, 31376], "\n                      Invia la password all&rsquo;indirizzo email dell'amministratore del cantiere\n                    ", [31262, 31376], "\n                      Invia la password all'indirizzo email dell&apos;amministratore del cantiere\n                    ", [31262, 31376], "\n                      Invia la password all'indirizzo email dell&lsquo;amministratore del cantiere\n                    ", [31262, 31376], "\n                      Invia la password all'indirizzo email dell&#39;amministratore del cantiere\n                    ", [31262, 31376], "\n                      Invia la password all'indirizzo email dell&rsquo;amministratore del cantiere\n                    ", [1096, 1125], "[isAuthenticated, cantiereId, loadCantiere]", [4722, 4734], "[cantiereId, loadCavi, loadRevisioneCorrente]", [5840, 5843], [5840, 5843], [6200, 6203], [6200, 6203], [6288, 6291], [6288, 6291], [6733, 6736], [6733, 6736], [1221, 1223], "[loadCertificazioni]", [1606, 1609], [1606, 1609], [3000, 3012], "[cantiereId, loadData]", [3928, 3931], [3928, 3931], [4777, 4780], [4777, 4780], [5201, 5204], [5201, 5204], [4127, 4130], [4127, 4130], [3121, 3146], "[cantiereId, authLoading, loadBobine]", [3622, 3625], [3622, 3625], [5449, 5452], [5449, 5452], [1282, 1285], [1282, 1285], [1338, 1341], [1338, 1341], [1416, 1419], [1416, 1419], [1482, 1485], [1482, 1485], [2552, 2555], [2552, 2555], [22785, 22788], [22785, 22788], [28293, 28296], [28293, 28296], [31307, 31310], [31307, 31310], [34730, 34733], [34730, 34733], [35669, 35672], [35669, 35672], [37356, 37359], [37356, 37359], [536, 539], [536, 539], [857, 860], [857, 860], [1174, 1177], [1174, 1177], [1395, 1398], [1395, 1398], [2713, 2716], [2713, 2716], [3968, 4004], "Informazioni sull&apos;impersonificazione", [3968, 4004], "Informazioni sull&lsquo;impersonificazione", [3968, 4004], "Informazioni sull&#39;impersonificazione", [3968, 4004], "Informazioni sull&rsquo;impersonificazione", [5047, 5129], "\n                Nessun utente disponibile per l&apos;impersonificazione\n              ", [5047, 5129], "\n                Nessun utente disponibile per l&lsquo;impersonificazione\n              ", [5047, 5129], "\n                Nessun utente disponibile per l&#39;impersonificazione\n              ", [5047, 5129], "\n                Nessun utente disponibile per l&rsquo;impersonificazione\n              ", [1252, 1255], [1252, 1255], [2424, 2478], "<PERSON><PERSON> gli utenti (eccetto l&apos;amministratore principale)", [2424, 2478], "<PERSON><PERSON> gli utenti (eccetto l&lsquo;amministratore principale)", [2424, 2478], "<PERSON><PERSON> gli utenti (eccetto l&#39;amministratore principale)", [2424, 2478], "<PERSON><PERSON> gli utenti (eccetto l&rsquo;amministratore principale)", [3692, 3802], "\n              Per procedere con il reset, devi confermare l&apos;operazione seguendo questi passaggi:\n            ", [3692, 3802], "\n              Per procedere con il reset, devi confermare l&lsquo;operazione seguendo questi passaggi:\n            ", [3692, 3802], "\n              Per procedere con il reset, devi confermare l&#39;operazione seguendo questi passaggi:\n            ", [3692, 3802], "\n              Per procedere con il reset, devi confermare l&rsquo;operazione seguendo questi passaggi:\n            ", [7136, 7187], "• L&apos;utente amministratore principale verrà ricreato", [7136, 7187], "• L&lsquo;utente amministratore principale verrà ricreato", [7136, 7187], "• L&#39;utente amministratore principale verrà ricreato", [7136, 7187], "• L&rsquo;utente amministratore principale verrà ricreato", [7304, 7363], "• L&apos;operazione può richiedere alcuni minuti per completarsi", [7304, 7363], "• L&lsquo;operazione può richiedere alcuni minuti per completarsi", [7304, 7363], "• L&#39;operazione può richiedere alcuni minuti per completarsi", [7304, 7363], "• L&rsquo;operazione può richiedere alcuni minuti per completarsi", [3500, 3620], "\n                  Gestisci l&apos;elenco dei produttori di cavi (<PERSON><PERSON> <PERSON><PERSON>, Nexans, General Cable, ecc.)\n                ", [3500, 3620], "\n                  Gestisci l&lsquo;elenco dei produttori di cavi (<PERSON><PERSON>, Nexans, General Cable, ecc.)\n                ", [3500, 3620], "\n                  Gestisci l&#39;elenco dei produttori di cavi (<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, General Cable, ecc.)\n                ", [3500, 3620], "\n                  Gestisci l&rsquo;elenco dei produttori di cavi (<PERSON><PERSON>, Nexans, General Cable, ecc.)\n                ", [4049, 4143], "\n                Qui sarà possibile gestire l&apos;anagrafica dei produttori di cavi\n              ", [4049, 4143], "\n                Qui sarà possibile gestire l&lsquo;anagrafica dei produttori di cavi\n              ", [4049, 4143], "\n                Qui sarà possibile gestire l&#39;anagrafica dei produttori di cavi\n              ", [4049, 4143], "\n                Qui sarà possibile gestire l&rsquo;anagrafica dei produttori di cavi\n              ", [2278, 2281], [2278, 2281], [3979, 3982], [3979, 3982], [4447, 4450], [4447, 4450], [4742, 4817], "\n          Aggiorna la tua password per mantenere l&apos;account sicuro\n        ", [4742, 4817], "\n          Aggiorna la tua password per mantenere l&lsquo;account sicuro\n        ", [4742, 4817], "\n          Aggiorna la tua password per mantenere l&#39;account sicuro\n        ", [4742, 4817], "\n          Aggiorna la tua password per mantenere l&rsquo;account sicuro\n        ", [16845, 16896], "• Se non ricevi l&apos;email, controlla la cartella spam", [16845, 16896], "• Se non ricevi l&lsquo;email, controlla la cartella spam", [16845, 16896], "• Se non ricevi l&#39;email, controlla la cartella spam", [16845, 16896], "• Se non ricevi l&rsquo;email, controlla la cartella spam", [16922, 16987], "• Per motivi di sicurezza, non riveleremo se l&apos;email è registrata", [16922, 16987], "• Per motivi di sicurezza, non riveleremo se l&lsquo;email è registrata", [16922, 16987], "• Per motivi di sicurezza, non riveleremo se l&#39;email è registrata", [16922, 16987], "• Per motivi di sicurezza, non riveleremo se l&rsquo;email è registrata", [13040, 13083], "• Considera l&apos;uso di un gestore di password", [13040, 13083], "• Considera l&lsquo;uso di un gestore di password", [13040, 13083], "• Considera l&#39;uso di un gestore di password", [13040, 13083], "• Considera l&rsquo;uso di un gestore di password", [5951, 5954], [5951, 5954], [5114, 5117], [5114, 5117], [5127, 5130], [5127, 5130], [10989, 10992], [10989, 10992], [11529, 11555], "[open, bobina, cantiereId, loadCavi]", [18998, 19001], [18998, 19001], [20962, 20965], [20962, 20965], [2111, 2129], "[open, cantiereId, checkFirstInsertion]", [7734, 7737], [7734, 7737], [1660, 1663], [1660, 1663], [3085, 3095], "[formData, validateForm]", [6444, 6447], [6444, 6447], "&quot;", [8013, 8060], "La bobina deve essere nello stato &quot;Disponibile\"", "&ldquo;", [8013, 8060], "La bobina deve essere nello stato &ldquo;Disponibile\"", "&#34;", [8013, 8060], "La bobina deve essere nello stato &#34;Disponibile\"", "&rdquo;", [8013, 8060], "La bobina deve essere nello stato &rdquo;Disponibile\"", [8013, 8060], "La bobina deve essere nello stato \"Disponibile&quot;", [8013, 8060], "La bobina deve essere nello stato \"Disponibile&ldquo;", [8013, 8060], "La bobina deve essere nello stato \"Disponibile&#34;", [8013, 8060], "La bobina deve essere nello stato \"Disponibile&rdquo;", [7564, 7767], "), \n                  indicando che sono stati posati più metri di quelli disponibili. \n                  Verificare l&apos;accuratezza delle misurazioni e considerare una revisione dei dati.\n                ", [7564, 7767], "), \n                  indicando che sono stati posati più metri di quelli disponibili. \n                  Verificare l&lsquo;accuratezza delle misurazioni e considerare una revisione dei dati.\n                ", [7564, 7767], "), \n                  indicando che sono stati posati più metri di quelli disponibili. \n                  Verificare l&#39;accuratezza delle misurazioni e considerare una revisione dei dati.\n                ", [7564, 7767], "), \n                  indicando che sono stati posati più metri di quelli disponibili. \n                  Verificare l&rsquo;accuratezza delle misurazioni e considerare una revisione dei dati.\n                ", [4749, 4752], [4749, 4752], [10984, 11073], "[internalSelectionEnabled, getStatusButton, getConnectionButton, getCertificationButton, selectedCavi, filteredCavi.length, handleSelectAll, handleSelectCavo]", [2040, 2052], "[open, cavo, loadResponsabili]", [3568, 3571], [3568, 3571], [4454, 4457], [4454, 4457], [1591, 1603], [2957, 2960], [2957, 2960], [3545, 3548], [3545, 3548], [4174, 4177], [4174, 4177], [1918, 1937], "[loadResponsabili, open, tipoComanda]", [3512, 3515], [3512, 3515], [1954, 1957], [1954, 1957], [2826, 2829], [2826, 2829], [3791, 3794], [3791, 3794], [8095, 8135], "• L&apos;export non modifica i dati originali", [8095, 8135], "• L&lsquo;export non modifica i dati originali", [8095, 8135], "• L&#39;export non modifica i dati originali", [8095, 8135], "• L&rsquo;export non modifica i dati originali", [3020, 3023], [3020, 3023], [4432, 4454], "Annullare l&apos;operazione", [4432, 4454], "Annullare l&lsquo;operazione", [4432, 4454], "Annullare l&#39;operazione", [4432, 4454], "Annullare l&rsquo;operazione", [4463, 4493], " e selezionare un&apos;altra bobina", [4463, 4493], " e selezionare un&lsquo;altra bobina", [4463, 4493], " e selezionare un&#39;altra bobina", [4463, 4493], " e selezionare un&rsquo;altra bobina", [2909, 2931], "[open, cavo, cantiere, loadBobine]", [3262, 3291], "[formData.metri_posati, cavo, validateMetri<PERSON>osati]", [6379, 6382], [6379, 6382], [11769, 11772], [11769, 11772], [2930, 2933], [2930, 2933], [3064, 3067], [3064, 3067], [3112, 3115], [3112, 3115], [3178, 3181], [3178, 3181], [3228, 3231], [3228, 3231], [3384, 3387], [3384, 3387], [5236, 5286], "[open, cavo, cantiere, cantiereProp, cantiereAuth, loadBobineCompatibili]", [9502, 9505], [9502, 9505], [2698, 2716], "[open, cantiereId, loadResponsabili]", [3362, 3365], [3362, 3365], [6243, 6246], [6243, 6246], [1290, 1293], [1290, 1293], [2325, 2358], "[open, codiceComanda, cantiereId, loadComandaDettagli]", [2821, 2824], [2821, 2824], [2246, 2264], [2855, 2858], [2855, 2858], [3824, 3827], [3824, 3827], [5277, 5280], [5277, 5280], [5831, 5834], [5831, 5834], [1161, 1164], [1161, 1164], [1477, 1498], "[open, codiceComanda, loadCavi]", [1986, 1989], [1986, 1989], [2310, 2313], [2310, 2313], [2590, 2593], [2590, 2593], [2919, 2922], [2919, 2922], [3640, 3643], [3640, 3643], [2567, 2588], "[open, codiceComanda, loadCaviComanda]", [2726, 2755], "[open, cantiere?.id_cantiere, loadBobine]", [4006, 4009], [4006, 4009], [4624, 4627], [4624, 4627], [10757, 10760], [10757, 10760], [1185, 1188], [1185, 1188], [1253, 1256], [1253, 1256], [1366, 1369], [1366, 1369], [1401, 1404], [1401, 1404], [17349, 17352], [17349, 17352], [17519, 17522], [17519, 17522], [794, 797], [794, 797], [900, 924], "[open, bobina, cantiere, loadCaviForDebug]", [3190, 3192], " &quot;", [3190, 3192], " &ldquo;", [3190, 3192], " &#34;", [3190, 3192], " &rdquo;", [3210, 3219], "&quot; (type: ", [3210, 3219], "&ldquo; (type: ", [3210, 3219], "&#34; (type: ", [3210, 3219], "&rdquo; (type: ", [3296, 3298], [3296, 3298], [3296, 3298], [3296, 3298], [3314, 3323], [3314, 3323], [3314, 3323], [3314, 3323], [3405, 3407], [3405, 3407], [3405, 3407], [3405, 3407], [3431, 3432], [3431, 3432], [3431, 3432], [3431, 3432], [4487, 4489], [4487, 4489], [4487, 4489], [4487, 4489], [4510, 4513], "&quot; (", [4510, 4513], "&ldquo; (", [4510, 4513], "&#34; (", [4510, 4513], "&rdquo; (", [4610, 4612], [4610, 4612], [4610, 4612], [4610, 4612], [4635, 4638], [4635, 4638], [4635, 4638], [4635, 4638], [4884, 4886], [4884, 4886], [4884, 4886], [4884, 4886], [4905, 4908], [4905, 4908], [4905, 4908], [4905, 4908], [5001, 5003], [5001, 5003], [5001, 5003], [5001, 5003], [5024, 5027], [5024, 5027], [5024, 5027], [5024, 5027], [5276, 5278], [5276, 5278], [5276, 5278], [5276, 5278], [5304, 5305], [5304, 5305], [5304, 5305], [5304, 5305], [5380, 5382], [5380, 5382], [5380, 5382], [5380, 5382], [5410, 5411], [5410, 5411], [5410, 5411], [5410, 5411], [1082, 1085], [1082, 1085], [1108, 1111], [1108, 1111], [1136, 1139], [1136, 1139], [1294, 1318], "[open, cantiere, bobina, loadDebugData]", [1579, 1582], [1579, 1582], [1618, 1621], [1618, 1621], [1659, 1662], [1659, 1662], [751, 754], [751, 754], [1163, 1166], [1163, 1166], [1381, 1434], "[open, bobina, caviSelezionati, caviMetri, forceOver, analyzeMetri<PERSON>osati]", [1647, 1650], [1647, 1650], [4750, 4827], "\n            Analisi dettagliata dell&apos;operazione di aggiunta metri\n          ", [4750, 4827], "\n            Analisi dettagliata dell&lsquo;operazione di aggiunta metri\n          ", [4750, 4827], "\n            Analisi dettagliata dell&#39;operazione di aggiunta metri\n          ", [4750, 4827], "\n            Analisi dettagliata dell&rsquo;operazione di aggiunta metri\n          ", [12112, 12207], "\n                    ⚠️ Attivare &quot;Force Over\" per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivar<PERSON> &ldquo;Force Over\" per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare &#34;Force Over\" per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare &rdquo;Force Over\" per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over&quot; per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over&ldquo; per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over&#34; per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over&rdquo; per procedere con l'operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over\" per procedere con l&apos;operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over\" per procedere con l&lsquo;operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over\" per procedere con l&#39;operazione\n                  ", [12112, 12207], "\n                    ⚠️ Attivare \"Force Over\" per procedere con l&rsquo;operazione\n                  ", [12366, 12456], "\n                    ⚠️ La bobina andrà in stato OVER dopo l&apos;operazione\n                  ", [12366, 12456], "\n                    ⚠️ La bobina andrà in stato OVER dopo l&lsquo;operazione\n                  ", [12366, 12456], "\n                    ⚠️ La bobina andrà in stato OVER dopo l&#39;operazione\n                  ", [12366, 12456], "\n                    ⚠️ La bobina andrà in stato OVER dopo l&rsquo;operazione\n                  ", [665, 668], [665, 668], [228, 231], [228, 231], [820, 823], [820, 823], [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [362, 365], [362, 365], [624, 627], [624, 627], [1493, 1496], [1493, 1496], [839, 842], [839, 842], [2077, 2080], [2077, 2080], [218, 221], [218, 221], [855, 858], [855, 858], [6964, 6967], [6964, 6967], [7260, 7263], [7260, 7263], [7494, 7497], [7494, 7497], [7745, 7748], [7745, 7748], [1425, 1428], [1425, 1428], [1694, 1697], [1694, 1697], [1883, 1886], [1883, 1886], [1908, 1911], [1908, 1911], [2089, 2092], [2089, 2092], [2114, 2117], [2114, 2117], [2298, 2301], [2298, 2301], [2323, 2326], [2323, 2326], [2511, 2514], [2511, 2514], [3415, 3418], [3415, 3418], [3620, 3623], [3620, 3623], [4053, 4056], [4053, 4056], [4192, 4195], [4192, 4195], [4330, 4333], [4330, 4333], [4442, 4445], [4442, 4445], [4463, 4466], [4463, 4466], [4582, 4585], [4582, 4585], [4602, 4605], [4602, 4605], [4734, 4737], [4734, 4737], [4971, 4974], [4971, 4974], [5257, 5260], [5257, 5260], [5515, 5518], [5515, 5518], [5726, 5729], [5726, 5729], [6130, 6133], [6130, 6133], [6286, 6289], [6286, 6289], [6378, 6381], [6378, 6381], [6688, 6691], [6688, 6691], [6839, 6842], [6839, 6842], [7113, 7116], [7113, 7116], [7247, 7250], [7247, 7250], [7268, 7271], [7268, 7271], [7414, 7417], [7414, 7417], [7434, 7437], [7434, 7437], [7595, 7598], [7595, 7598], [7954, 7957], [7954, 7957], [7974, 7977], [7974, 7977], [8309, 8312], [8309, 8312], [8544, 8547], [8544, 8547], [8696, 8699], [8696, 8699], [8826, 8829], [8826, 8829], [8942, 8945], [8942, 8945], [8963, 8966], [8963, 8966], [9100, 9103], [9100, 9103], [9140, 9143], [9140, 9143], [9392, 9395], [9392, 9395], [9412, 9415], [9412, 9415], [9562, 9565], [9562, 9565], [9582, 9585], [9582, 9585], [9934, 9937], [9934, 9937], [10359, 10362], [10359, 10362], [10537, 10540], [10537, 10540], [10769, 10772], [10769, 10772], [10908, 10911], [10908, 10911], [10929, 10932], [10929, 10932], [11076, 11079], [11076, 11079], [11096, 11099], [11096, 11099], [11417, 11420], [11417, 11420], [11558, 11561], [11558, 11561], [11579, 11582], [11579, 11582], [12094, 12097], [12094, 12097], [12393, 12396], [12393, 12396], [12944, 12947], [12944, 12947], [13061, 13064], [13061, 13064], [13210, 13213], [13210, 13213], [13340, 13343], [13340, 13343], [13723, 13726], [13723, 13726], [13910, 13913], [13910, 13913], [14011, 14014], [14011, 14014], [14095, 14098], [14095, 14098], [14116, 14119], [14116, 14119], [14213, 14216], [14213, 14216], [14233, 14236], [14233, 14236], [14373, 14376], [14373, 14376], [14465, 14468], [14465, 14468], [14536, 14539], [14536, 14539], [14557, 14560], [14557, 14560], [14641, 14644], [14641, 14644], [14661, 14664], [14661, 14664], [14863, 14866], [14863, 14866], [14965, 14968], [14965, 14968], [15074, 15077], [15074, 15077], [15195, 15198], [15195, 15198], [15279, 15282], [15279, 15282], [2996, 2999], [2996, 2999], [6365, 6368], [6365, 6368], [820, 823], [820, 823], [1166, 1169], [1166, 1169], [1540, 1543], [1540, 1543], [1553, 1556], [1553, 1556], [320, 323], [320, 323], [364, 367], [364, 367], [1158, 1161], [1158, 1161], [2455, 2458], [2455, 2458], [3754, 3757], [3754, 3757], [5426, 5429], [5426, 5429], [6540, 6543], [6540, 6543], [7495, 7498], [7495, 7498], [8505, 8508], [8505, 8508]]