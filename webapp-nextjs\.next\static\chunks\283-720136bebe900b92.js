"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{25731:(e,a,t)=>{t.d(a,{AR:()=>l,At:()=>n,CV:()=>s,FH:()=>c,Fw:()=>r,ZQ:()=>i,_I:()=>u,dG:()=>g,km:()=>p,mg:()=>d,ug:()=>m});let o=t(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{{let a=localStorage.getItem("token");a&&(e.headers.Authorization="Bearer ".concat(a))}return e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),window.location.href="/login"),Promise.reject(e)});let c={get:async(e,a)=>(await o.get(e,a)).data,post:async(e,a,t)=>(await o.post(e,a,t)).data,put:async(e,a,t)=>(await o.put(e,a,t)).data,patch:async(e,a,t)=>(await o.patch(e,a,t)).data,delete:async(e,a)=>(await o.delete(e,a)).data},i={login:async e=>{let a=new FormData;return a.append("username",e.username),a.append("password",e.password),(await o.post("/api/auth/login",a,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>c.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>c.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,a)=>c.get("/api/cavi/".concat(e),{params:a}),getCavo:(e,a)=>c.get("/api/cavi/".concat(e,"/").concat(a)),checkCavo:(e,a)=>c.get("/api/cavi/".concat(e,"/check/").concat(a)),createCavo:(e,a)=>c.post("/api/cavi/".concat(e),a),updateCavo:(e,a,t)=>c.put("/api/cavi/".concat(e,"/").concat(a),t),deleteCavo:(e,a,t)=>c.delete("/api/cavi/".concat(e,"/").concat(a),{data:t}),updateMetriPosati:(e,a,t,o,i)=>c.post("/api/cavi/".concat(e,"/").concat(a,"/metri-posati"),{metri_posati:t,id_bobina:o,force_over:i||!1}),updateBobina:(e,a,t,o)=>c.post("/api/cavi/".concat(e,"/").concat(a,"/bobina"),{id_bobina:t,force_over:o||!1}),cancelInstallation:(e,a)=>c.post("/api/cavi/".concat(e,"/").concat(a,"/cancel-installation")),collegaCavo:(e,a,t,o)=>c.post("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{lato:t,responsabile:o}),scollegaCavo:(e,a,t)=>c.delete("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{data:{lato:t}}),markAsSpare:(e,a,t)=>c.put("/api/cavi/".concat(e,"/").concat(a,"/spare"),{spare:+!!t}),debugCavi:e=>c.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>c.get("/api/cavi/debug/raw/".concat(e))},r={getBobine:(e,a)=>c.get("/api/parco-cavi/".concat(e),{params:a}),getBobina:(e,a)=>c.get("/api/parco-cavi/".concat(e,"/").concat(a)),getBobineCompatibili:(e,a)=>c.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:a}),createBobina:(e,a)=>c.post("/api/parco-cavi/".concat(e),a),updateBobina:(e,a,t)=>c.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>c.delete("/api/parco-cavi/".concat(e,"/").concat(a)),isFirstBobinaInsertion:e=>c.get("/api/parco-cavi/".concat(e,"/is-first-insertion")),updateBobina:(e,a,t)=>c.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>c.delete("/api/parco-cavi/".concat(e,"/").concat(a)),checkDisponibilita:(e,a,t)=>c.get("/api/parco-cavi/".concat(e,"/").concat(a,"/disponibilita"),{params:{metri_richiesti:t}})},s={getComande:e=>c.get("/api/comande/cantiere/".concat(e)),getComanda:(e,a)=>c.get("/api/comande/".concat(a)),getCaviComanda:e=>c.get("/api/comande/".concat(e,"/cavi")),createComanda:(e,a)=>c.post("/api/comande/cantiere/".concat(e),a),createComandaWithCavi:(e,a,t)=>c.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),a,{params:{lista_id_cavi:t}}),updateDatiComanda:(e,a,t)=>c.put("/api/comande/".concat(e,"/").concat(a),t),updateComanda:(e,a,t)=>c.put("/api/comande/cantiere/".concat(e,"/").concat(a),t),deleteComanda:(e,a)=>c.delete("/api/comande/cantiere/".concat(e,"/").concat(a)),assegnaCavi:(e,a,t)=>c.post("/api/comande/cantiere/".concat(e,"/").concat(a,"/assegna-cavi"),{cavi_ids:t}),rimuoviCavi:(e,a,t)=>c.delete("/api/comande/cantiere/".concat(e,"/").concat(a,"/rimuovi-cavi"),{data:{cavi_ids:t}}),getStatistiche:e=>c.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,a,t)=>c.put("/api/comande/cantiere/".concat(e,"/").concat(a,"/stato"),{nuovo_stato:t})},l={getResponsabili:e=>c.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,a)=>c.post("/api/responsabili/".concat(e),a),updateResponsabile:(e,a,t)=>c.put("/api/responsabili/".concat(e,"/").concat(a),t),deleteResponsabile:(e,a)=>c.delete("/api/responsabili/".concat(e,"/").concat(a))},p={getCertificazioni:e=>c.get("/api/cantieri/".concat(e,"/certificazioni")),createCertificazione:(e,a)=>c.post("/api/cantieri/".concat(e,"/certificazioni"),a),generatePDF:(e,a)=>c.get("/api/cantieri/".concat(e,"/pdf-cei-64-8/").concat(a),{responseType:"blob"})},d={importCavi:(e,a,t)=>{let o=new FormData;return o.append("file",a),o.append("revisione",t),c.post("/api/excel/".concat(e,"/import-cavi"),o,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,a)=>{let t=new FormData;return t.append("file",a),c.post("/api/excel/".concat(e,"/import-parco-bobine"),t,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>c.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>c.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},m={getReportAvanzamento:e=>c.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>c.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>c.get("/api/reports/".concat(e,"/storico-bobine")),getReportProgress:e=>c.get("/api/reports/".concat(e,"/progress")),getReportPosaPeriodo:(e,a,t)=>{let o=new URLSearchParams;a&&o.append("data_inizio",a),t&&o.append("data_fine",t);let i=o.toString();return c.get("/api/reports/".concat(e,"/posa-periodo").concat(i?"?".concat(i):""))}},u={getCantieri:()=>c.get("/api/cantieri"),getCantiere:e=>c.get("/api/cantieri/".concat(e)),createCantiere:e=>c.post("/api/cantieri",e),updateCantiere:(e,a)=>c.put("/api/cantieri/".concat(e),a)},g={getUsers:()=>c.get("/api/users"),getUser:e=>c.get("/api/users/".concat(e)),createUser:e=>c.post("/api/users",e),updateUser:(e,a)=>c.put("/api/users/".concat(e),a),deleteUser:e=>c.delete("/api/users/".concat(e)),toggleUserStatus:e=>c.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>c.get("/api/users/check-expired"),impersonateUser:e=>c.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>c.get("/api/users/db-raw"),resetDatabase:()=>c.post("/api/admin/reset-database")}},40283:(e,a,t)=>{t.d(a,{A:()=>r,AuthProvider:()=>s});var o=t(95155),c=t(12115),i=t(25731);let n=(0,c.createContext)(void 0);function r(){let e=(0,c.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function s(e){let{children:a}=e,[t,r]=(0,c.useState)(null),[s,l]=(0,c.useState)(null),[p,d]=(0,c.useState)(!0),[m,u]=(0,c.useState)(()=>"true"===localStorage.getItem("isImpersonating")),[g,v]=(0,c.useState)(()=>{{let e=localStorage.getItem("impersonatedUser");return e?JSON.parse(e):null}}),_=!!t||!!s;(0,c.useEffect)(()=>{b()},[]),(0,c.useEffect)(()=>{{let e=localStorage.getItem("selectedCantiereId"),a=localStorage.getItem("selectedCantiereName");e&&!s&&l({id_cantiere:parseInt(e,10),commessa:a||"Cantiere ".concat(e),codice_univoco:"",id_utente:(null==t?void 0:t.id_utente)||0})}},[t,s]);let b=async()=>{try{if(d(!0),localStorage.getItem("token"))try{let e=await i.ZQ.verifyToken(),a={id_utente:e.user_id,username:e.username,ruolo:e.role};r(a);let t=!0===e.is_impersonated;if(u(t),t&&e.impersonated_id){let a={id:e.impersonated_id,username:e.impersonated_username,role:e.impersonated_role};v(a),localStorage.setItem("impersonatedUser",JSON.stringify(a)),localStorage.setItem("isImpersonating","true")}else v(null),localStorage.removeItem("impersonatedUser"),localStorage.removeItem("isImpersonating");if("cantieri_user"===e.role&&e.cantiere_id){let a={id_cantiere:e.cantiere_id,commessa:e.cantiere_name||"Cantiere ".concat(e.cantiere_id),codice_univoco:"",id_utente:e.user_id};l(a)}else{let e=localStorage.getItem("cantiere_data");if(e)try{let a=JSON.parse(e);l(a)}catch(e){localStorage.removeItem("cantiere_data")}}}catch(e){localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),r(null),l(null)}else r(null),l(null)}catch(e){localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),r(null),l(null)}finally{setTimeout(()=>{d(!1)},500)}},S=async(e,a)=>{try{d(!0);let t=await i.ZQ.login({username:e,password:a});{localStorage.setItem("token",t.access_token);let e={id_utente:t.user_id,username:t.username,ruolo:t.role};return r(e),l(null),e}}catch(e){throw e}finally{d(!1)}},C=async(e,a)=>{try{d(!0);let t=await i.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:a});{localStorage.setItem("token",t.access_token);let a={id_cantiere:t.cantiere_id,commessa:t.cantiere_name,codice_univoco:e,id_utente:t.user_id};return localStorage.setItem("cantiere_data",JSON.stringify(a)),l(a),r(null),a}}catch(e){throw e}finally{d(!1)}},h=async e=>{try{let a=await i.dG.impersonateUser(e);{localStorage.setItem("token",a.access_token);let e={id:a.impersonated_id,username:a.impersonated_username,role:a.impersonated_role};return localStorage.setItem("impersonatedUser",JSON.stringify(e)),v(e),u(!0),localStorage.setItem("isImpersonating","true"),{impersonatedUser:e}}}catch(e){throw e}};return(0,o.jsx)(n.Provider,{value:{user:t,cantiere:s,isAuthenticated:_,isLoading:p,isImpersonating:m,impersonatedUser:g,login:S,loginCantiere:C,logout:()=>{localStorage.clear(),sessionStorage.clear(),r(null),l(null),u(!1),v(null),window.location.replace("/login")},checkAuth:b,impersonateUser:h,selectCantiere:e=>{if(e&&e.id_cantiere){let a=e.commessa||"Cantiere ".concat(e.id_cantiere);localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",a),l({...e,commessa:a})}}},children:a})}}}]);