{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WYBMyxw1tWdETJn1nLodZFNDqP9pUhSE6OeYS65imRQ=", "__NEXT_PREVIEW_MODE_ID": "0840d76800bc1783cdadc35cea224c4b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9294529351ddc12c721363018e216428e8ce5a6c5bffb6ec5197f7cdea1d2316", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "542a9b546b69b3b12da5530382b545adbdbebf26f82e709b58d665cd23abf32f"}}}, "sortedMiddleware": ["/"], "functions": {}}