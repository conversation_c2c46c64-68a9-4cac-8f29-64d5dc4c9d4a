"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[674],{4229:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},12318:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},30064:(e,t,r)=>{r.d(t,{UC:()=>J,B8:()=>X,bL:()=>O,l9:()=>Z});var a=r(12115),n=r(85185),o=r(46081),l=r(37328),i=r(6101),s=r(61285),d=r(63655),u=r(39033),c=r(5845),h=r(94315),f=r(95155),p="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[k,m,b]=(0,l.N)(v),[x,w]=(0,o.A)(v,[b]),[M,A]=x(v),g=a.forwardRef((e,t)=>(0,f.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:t})})}));g.displayName=v;var j=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:l=!1,dir:s,currentTabStopId:k,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:A=!1,...g}=e,j=a.useRef(null),C=(0,i.s)(t,j),R=(0,h.jH)(s),[E,D]=(0,c.i)({prop:k,defaultProp:null!=b?b:null,onChange:x,caller:v}),[F,P]=a.useState(!1),T=(0,u.c)(w),H=m(r),L=a.useRef(!1),[S,G]=a.useState(0);return a.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(p,T),()=>e.removeEventListener(p,T)},[T]),(0,f.jsx)(M,{scope:r,orientation:o,dir:R,loop:l,currentTabStopId:E,onItemFocus:a.useCallback(e=>D(e),[D]),onItemShiftTab:a.useCallback(()=>P(!0),[]),onFocusableItemAdd:a.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>G(e=>e-1),[]),children:(0,f.jsx)(d.sG.div,{tabIndex:F||0===S?-1:0,"data-orientation":o,...g,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!F){let t=new CustomEvent(p,y);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=H().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),A)}}L.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>P(!1))})})}),C="RovingFocusGroupItem",R=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:l=!1,tabStopId:i,children:u,...c}=e,h=(0,s.B)(),p=i||h,y=A(C,r),v=y.currentTabStopId===p,b=m(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:M}=y;return a.useEffect(()=>{if(o)return x(),()=>w()},[o,x,w]),(0,f.jsx)(k.ItemSlot,{scope:r,id:p,focusable:o,active:l,children:(0,f.jsx)(d.sG.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...c,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?y.onItemFocus(p):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>y.onItemFocus(p)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void y.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return E[n]}(e,y.orientation,y.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=y.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>I(r))}}),children:"function"==typeof u?u({isCurrentTabStop:v,hasTabStop:null!=M}):u})})});R.displayName=C;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var D=r(28905),F="Tabs",[P,T]=(0,o.A)(F,[w]),H=w(),[L,S]=P(F),G=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:u="automatic",...p}=e,y=(0,h.jH)(i),[v,k]=(0,c.i)({prop:a,onChange:n,defaultProp:null!=o?o:"",caller:F});return(0,f.jsx)(L,{scope:r,baseId:(0,s.B)(),value:v,onValueChange:k,orientation:l,dir:y,activationMode:u,children:(0,f.jsx)(d.sG.div,{dir:y,"data-orientation":l,...p,ref:t})})});G.displayName=F;var N="TabsList",_=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=S(N,r),l=H(r);return(0,f.jsx)(g,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:a,children:(0,f.jsx)(d.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});_.displayName=N;var z="TabsTrigger",K=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...l}=e,i=S(z,r),s=H(r),u=B(i.baseId,a),c=U(i.baseId,a),h=a===i.value;return(0,f.jsx)(R,{asChild:!0,...s,focusable:!o,active:h,children:(0,f.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":c,"data-state":h?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...l,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;h||o||!e||i.onValueChange(a)})})})});K.displayName=z;var V="TabsContent",q=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:l,...i}=e,s=S(V,r),u=B(s.baseId,n),c=U(s.baseId,n),h=n===s.value,p=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(D.C,{present:o||h,children:r=>{let{present:a}=r;return(0,f.jsx)(d.sG.div,{"data-state":h?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:c,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})}})});function B(e,t){return"".concat(e,"-trigger-").concat(t)}function U(e,t){return"".concat(e,"-content-").concat(t)}q.displayName=V;var O=G,X=_,Z=K,J=q},35695:(e,t,r)=>{var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40133:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},43332:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},48136:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},53904:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54213:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},57434:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62525:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},70306:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>A,bL:()=>w});var a=r(12115),n=r(6101),o=r(46081),l=r(85185),i=r(5845),s=r(45503),d=r(11275),u=r(28905),c=r(63655),h=r(95155),f="Checkbox",[p,y]=(0,o.A)(f),[v,k]=p(f);function m(e){let{__scopeCheckbox:t,checked:r,children:n,defaultChecked:o,disabled:l,form:s,name:d,onCheckedChange:u,required:c,value:p="on",internal_do_not_use_render:y}=e,[k,m]=(0,i.i)({prop:r,defaultProp:null!=o&&o,onChange:u,caller:f}),[b,x]=a.useState(null),[w,M]=a.useState(null),A=a.useRef(!1),g=!b||!!s||!!b.closest("form"),j={checked:k,disabled:l,setChecked:m,control:b,setControl:x,name:d,form:s,value:p,hasConsumerStoppedPropagationRef:A,required:c,defaultChecked:!C(o)&&o,isFormControl:g,bubbleInput:w,setBubbleInput:M};return(0,h.jsx)(v,{scope:t,...j,children:"function"==typeof y?y(j):n})}var b="CheckboxTrigger",x=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:o,onClick:i,...s}=e,{control:d,value:u,disabled:f,checked:p,required:y,setControl:v,setChecked:m,hasConsumerStoppedPropagationRef:x,isFormControl:w,bubbleInput:M}=k(b,r),A=(0,n.s)(t,v),g=a.useRef(p);return a.useEffect(()=>{let e=null==d?void 0:d.form;if(e){let t=()=>m(g.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,m]),(0,h.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":C(p)?"mixed":p,"aria-required":y,"data-state":R(p),"data-disabled":f?"":void 0,disabled:f,value:u,...s,ref:A,onKeyDown:(0,l.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(i,e=>{m(e=>!!C(e)||!e),M&&w&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});x.displayName=b;var w=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:n,defaultChecked:o,required:l,disabled:i,value:s,onCheckedChange:d,form:u,...c}=e;return(0,h.jsx)(m,{__scopeCheckbox:r,checked:n,defaultChecked:o,disabled:i,required:l,onCheckedChange:d,name:a,form:u,value:s,internal_do_not_use_render:e=>{let{isFormControl:a}=e;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(x,{...c,ref:t,__scopeCheckbox:r}),a&&(0,h.jsx)(j,{__scopeCheckbox:r})]})}})});w.displayName=f;var M="CheckboxIndicator",A=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,o=k(M,r);return(0,h.jsx)(u.C,{present:a||C(o.checked)||!0===o.checked,children:(0,h.jsx)(c.sG.span,{"data-state":R(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});A.displayName=M;var g="CheckboxBubbleInput",j=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,...o}=e,{control:l,hasConsumerStoppedPropagationRef:i,checked:u,defaultChecked:f,required:p,disabled:y,name:v,value:m,form:b,bubbleInput:x,setBubbleInput:w}=k(g,r),M=(0,n.s)(t,w),A=(0,s.Z)(u),j=(0,d.X)(l);a.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(A!==u&&e){let r=new Event("click",{bubbles:t});x.indeterminate=C(u),e.call(x,!C(u)&&u),x.dispatchEvent(r)}},[x,A,u,i]);let R=a.useRef(!C(u)&&u);return(0,h.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:R.current,required:p,disabled:y,name:v,value:m,form:b,...o,tabIndex:-1,ref:M,style:{...o.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function R(e){return C(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=g},78749:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);