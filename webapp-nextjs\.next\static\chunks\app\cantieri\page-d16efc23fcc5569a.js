(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[222],{30285:(e,a,r)=>{"use strict";r.d(a,{$:()=>l});var s=r(95155);r(12115);var t=r(99708),n=r(74466),o=r(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:r,size:n,asChild:l=!1,...d}=e,c=l?t.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:n,className:a})),...d})}},40321:(e,a,r)=>{Promise.resolve().then(r.bind(r,42712))},42712:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>C});var s=r(95155),t=r(12115),n=r(35695),o=r(66695),i=r(30285),l=r(62523),d=r(85057),c=r(85127),m=r(54165),u=r(40283),h=r(25731),x=r(51154),f=r(47924),g=r(84616),p=r(85339),b=r(23227),v=r(32919),w=r(381),j=r(92657),N=r(28883),y=r(40646),_=r(75525);function C(){let{user:e,isAuthenticated:a,isLoading:r}=(0,u.A)(),C=(0,n.useRouter)(),[z,P]=(0,t.useState)([]),[k,A]=(0,t.useState)(!0),[E,S]=(0,t.useState)(""),[I,F]=(0,t.useState)(""),[J,L]=(0,t.useState)(!1),[$,O]=(0,t.useState)(!1),[D,G]=(0,t.useState)(!1),[T,B]=(0,t.useState)(null),[R,M]=(0,t.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[Z,H]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[X,V]=(0,t.useState)("change"),[W,Q]=(0,t.useState)(""),[U,Y]=(0,t.useState)(!1);(0,t.useEffect)(()=>{r||a||C.push("/login")},[a,r,C]),(0,t.useEffect)(()=>{a&&q()},[a]);let q=async()=>{try{A(!0);let e=await h._I.getCantieri();P(e)}catch(e){S("Errore nel caricamento dei cantieri")}finally{A(!1)}},K=async()=>{try{await h._I.createCantiere(R),L(!1),M({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),q()}catch(e){S("Errore nella creazione del cantiere")}},ee=async()=>{if(T)try{await h._I.updateCantiere(T.id_cantiere,R),O(!1),B(null),q()}catch(e){S("Errore nella modifica del cantiere")}},ea=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),C.push("/cantieri/".concat(e.id_cantiere))},er=e=>{B(e),M({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),O(!0)},es=async()=>{if(T)try{A(!0),S("");let e=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(T.id_cantiere,"/view-password"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nel recupero password")}let a=await e.json();Q(a.password_cantiere),Y(!0),S("")}catch(e){S(e instanceof Error?e.message:"Errore nel recupero password"),Y(!1)}finally{A(!1)}},et=async()=>{if(T)try{A(!0),S("");let e=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(T.id_cantiere,"/send-password-email"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nell'invio email")}let a=await e.json();alert(a.message||"Password inviata via email con successo"),S("")}catch(e){S(e instanceof Error?e.message:"Errore nell'invio email")}finally{A(!1)}},en=async()=>{if(T){if(Z.newPassword!==Z.confirmPassword)return void S("Le password non coincidono");if(!Z.currentPassword)return void S("Inserisci la password attuale per confermare il cambio");if(!Z.newPassword||Z.newPassword.length<6)return void S("La nuova password deve essere di almeno 6 caratteri");try{A(!0),S("");let e=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(T.id_cantiere,"/change-password"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({password_attuale:Z.currentPassword,password_nuova:Z.newPassword,conferma_password:Z.confirmPassword})});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nel cambio password")}let a=await e.json();if(a.success)H({currentPassword:"",newPassword:"",confirmPassword:""}),G(!1),S(""),alert(a.message||"Password cambiata con successo");else throw Error(a.message||"Errore nel cambio password")}catch(e){S(e instanceof Error?e.message:"Errore nel cambio password")}finally{A(!1)}}},eo=z.filter(e=>{var a,r;return e.commessa.toLowerCase().includes(I.toLowerCase())||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(I.toLowerCase()))||(null==(r=e.nome_cliente)?void 0:r.toLowerCase().includes(I.toLowerCase()))});return r?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(x.A,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(f.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(l.p,{placeholder:"Cerca cantieri...",value:I,onChange:e=>F(e.target.value),className:"pl-8 w-full"})]})}),(0,s.jsxs)(m.lG,{open:J,onOpenChange:L,children:[(0,s.jsx)(m.zM,{asChild:!0,children:(0,s.jsxs)(i.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,s.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,s.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,s.jsx)(l.p,{id:"commessa",value:R.commessa,onChange:e=>M({...R,commessa:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"descrizione",value:R.descrizione,onChange:e=>M({...R,descrizione:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"nome_cliente",value:R.nome_cliente,onChange:e=>M({...R,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,s.jsx)(l.p,{id:"password_cantiere",type:"password",value:R.password_cantiere,onChange:e=>M({...R,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(i.$,{onClick:K,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),E&&(0,s.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,s.jsx)("span",{className:"text-red-800",children:E})]})}),k?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)(x.A,{className:"h-8 w-8 animate-spin"})}):0===eo.length?(0,s.jsx)(o.Zp,{children:(0,s.jsxs)(o.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(b.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:I?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!I&&(0,s.jsxs)(i.$,{onClick:()=>L(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,s.jsx)(o.Zp,{children:(0,s.jsxs)(c.XI,{children:[(0,s.jsx)(c.A0,{children:(0,s.jsxs)(c.Hj,{children:[(0,s.jsx)(c.nd,{children:"Commessa"}),(0,s.jsx)(c.nd,{children:"Descrizione"}),(0,s.jsx)(c.nd,{children:"Cliente"}),(0,s.jsx)(c.nd,{children:"Data Creazione"}),(0,s.jsx)(c.nd,{children:"Codice"}),(0,s.jsx)(c.nd,{children:"Password"}),(0,s.jsx)(c.nd,{className:"text-right",children:"Azioni"})]})}),(0,s.jsx)(c.BF,{children:eo.map(e=>(0,s.jsxs)(c.Hj,{children:[(0,s.jsx)(c.nA,{className:"font-medium",children:e.commessa}),(0,s.jsx)(c.nA,{children:e.descrizione}),(0,s.jsx)(c.nA,{children:e.nome_cliente}),(0,s.jsx)(c.nA,{children:new Date(e.data_creazione).toLocaleDateString()}),(0,s.jsx)(c.nA,{children:(0,s.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:e.codice_univoco})}),(0,s.jsx)(c.nA,{children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-green-100 text-green-800 px-2 py-1 rounded",children:e.password_cantiere?"••••••••":"Non impostata"}),(0,s.jsx)(i.$,{size:"sm",variant:"ghost",className:"text-blue-600 hover:bg-blue-50 p-1",title:"Gestisci password cantiere",onClick:()=>{B(e),G(!0)},children:(0,s.jsx)(v.A,{className:"h-3 w-3"})})]})}),(0,s.jsx)(c.nA,{className:"text-right",children:(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,s.jsx)(i.$,{size:"sm",onClick:()=>er(e),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",title:"Modifica cantiere",children:(0,s.jsx)(w.A,{className:"h-3 w-3"})}),(0,s.jsxs)(i.$,{size:"sm",onClick:()=>ea(e),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:["Gestisci",(0,s.jsx)(j.A,{className:"ml-2 h-3 w-3"})]})]})})]},e.id_cantiere))})]})}),(0,s.jsx)(m.lG,{open:$,onOpenChange:O,children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,s.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,s.jsx)(l.p,{id:"edit-commessa",value:R.commessa,onChange:e=>M({...R,commessa:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"edit-descrizione",value:R.descrizione,onChange:e=>M({...R,descrizione:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"edit-nome_cliente",value:R.nome_cliente,onChange:e=>M({...R,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,s.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:R.indirizzo_cantiere,onChange:e=>M({...R,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,s.jsx)(l.p,{id:"edit-citta_cantiere",value:R.citta_cantiere,onChange:e=>M({...R,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,s.jsx)(l.p,{id:"edit-nazione_cantiere",value:R.nazione_cantiere,onChange:e=>M({...R,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(d.J,{htmlFor:"edit-password_cantiere",className:"text-right",children:"Password"}),(0,s.jsx)(l.p,{id:"edit-password_cantiere",type:"password",value:R.password_cantiere,onChange:e=>M({...R,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,s.jsxs)(m.Es,{children:[(0,s.jsx)(i.$,{onClick:()=>O(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,s.jsx)(i.$,{onClick:ee,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,s.jsx)(m.lG,{open:D,onOpenChange:e=>{G(e),e||(V("change"),Y(!1),Q(""),H({currentPassword:"",newPassword:"",confirmPassword:""}))},children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(v.A,{className:"h-5 w-5"}),"Gestione Password - ",null==T?void 0:T.commessa]}),(0,s.jsx)(m.rr,{children:"Scegli come gestire la password del cantiere"})]}),(0,s.jsxs)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:[(0,s.jsxs)("button",{onClick:()=>V("change"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("change"===X?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,s.jsx)(w.A,{className:"inline mr-2 h-4 w-4"}),"Cambia"]}),(0,s.jsxs)("button",{onClick:()=>V("recover"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("recover"===X?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"),children:[(0,s.jsx)(N.A,{className:"inline mr-2 h-4 w-4"}),"Recupera"]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:["change"===X&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"h-5 w-5"}),"Cambia Password"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,s.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:Z.currentPassword,onChange:e=>H({...Z,currentPassword:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,s.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:Z.newPassword,onChange:e=>H({...Z,newPassword:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,s.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:Z.confirmPassword,onChange:e=>H({...Z,confirmPassword:e.target.value})})]}),(0,s.jsxs)(i.$,{onClick:en,disabled:k||!Z.currentPassword||!Z.newPassword||!Z.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[k?(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),"recover"===X&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-5 w-5"}),"Recupera Password"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Opzioni per recuperare una password dimenticata"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Recupero Diretto"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mb-3",children:"Tenta di recuperare la password dal sistema (funziona solo se la password \xe8 stata salvata in formato recuperabile)"}),U&&(0,s.jsxs)("div",{className:"mb-3 p-3 bg-green-50 border border-green-200 rounded",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"font-medium text-green-800",children:"Password Recuperata"})]}),(0,s.jsx)("code",{className:"text-lg font-mono bg-white p-2 rounded border block",children:W})]}),(0,s.jsxs)(i.$,{onClick:es,disabled:k,className:"w-full relative overflow-hidden bg-orange-600 hover:bg-orange-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-orange-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[k?(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Recupera Password"]})]}),(0,s.jsxs)("div",{className:"p-4 border border-green-200 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"Invio via Email"}),(0,s.jsx)("p",{className:"text-sm text-green-700 mb-3",children:"Invia la password all'indirizzo email dell'amministratore del cantiere"}),(0,s.jsxs)(i.$,{onClick:et,disabled:k,className:"w-full relative overflow-hidden bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-green-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[k?(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Invia Password via Email"]})]})]})]}),E&&(0,s.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:E})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(i.$,{variant:"outline",onClick:()=>G(!1),children:"Chiudi"})})]})})]})}},54165:(e,a,r)=>{"use strict";r.d(a,{Cf:()=>m,Es:()=>h,L3:()=>x,c7:()=>u,lG:()=>i,rr:()=>f,zM:()=>l});var s=r(95155);r(12115);var t=r(15452),n=r(54416),o=r(59434);function i(e){let{...a}=e;return(0,s.jsx)(t.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,s.jsx)(t.l9,{"data-slot":"dialog-trigger",...a})}function d(e){let{...a}=e;return(0,s.jsx)(t.ZL,{"data-slot":"dialog-portal",...a})}function c(e){let{className:a,...r}=e;return(0,s.jsx)(t.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...r})}function m(e){let{className:a,children:r,showCloseButton:i=!0,...l}=e;return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(c,{}),(0,s.jsxs)(t.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...l,children:[r,i&&(0,s.jsxs)(t.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",a),...r})}function h(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...r})}function x(e){let{className:a,...r}=e;return(0,s.jsx)(t.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",a),...r})}function f(e){let{className:a,...r}=e;return(0,s.jsx)(t.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",a),...r})}},59434:(e,a,r)=>{"use strict";r.d(a,{cn:()=>n});var s=r(52596),t=r(39688);function n(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,t.QP)((0,s.$)(a))}},62523:(e,a,r)=>{"use strict";r.d(a,{p:()=>n});var s=r(95155);r(12115);var t=r(59434);function n(e){let{className:a,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n})}},66695:(e,a,r)=>{"use strict";r.d(a,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o});var s=r(95155);r(12115);var t=r(59434);function n(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function o(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function i(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",a),...r})}function l(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",a),...r})}function d(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",a),...r})}},85057:(e,a,r)=>{"use strict";r.d(a,{J:()=>o});var s=r(95155);r(12115);var t=r(40968),n=r(59434);function o(e){let{className:a,...r}=e;return(0,s.jsx)(t.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...r})}},85127:(e,a,r)=>{"use strict";r.d(a,{A0:()=>o,BF:()=>i,Hj:()=>l,XI:()=>n,nA:()=>c,nd:()=>d});var s=r(95155);r(12115);var t=r(59434);function n(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,t.cn)("w-full caption-bottom text-sm border-collapse",a),...r})})}function o(e){let{className:a,...r}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,t.cn)("[&_tr]:border-b",a),...r})}function i(e){let{className:a,...r}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,t.cn)("[&_tr:last-child]:border-0",a),...r})}function l(e){let{className:a,...r}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,t.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...r})}function d(e){let{className:a,...r}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,t.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...r})}function c(e){let{className:a,...r}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,t.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...r})}}},e=>{var a=a=>e(e.s=a);e.O(0,[455,464,346,774,283,441,684,358],()=>a(40321)),_N_E=e.O()}]);