"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5731],{25731:(a,t,e)=>{e.d(t,{AR:()=>u,At:()=>d,CV:()=>g,Fw:()=>l,ZQ:()=>s,_I:()=>_,dG:()=>w,km:()=>m,kw:()=>v,l9:()=>b,mg:()=>f,om:()=>C,ug:()=>h});var i=e(23464);let c=e(49509).env.NEXT_PUBLIC_FRONTEND_URL||"http://localhost:3001",o=i.A.create({baseURL:c,timeout:3e4,headers:{"Content-Type":"application/json"}}),n=()=>localStorage.getItem("token")||localStorage.getItem("access_token"),r=()=>{["token","access_token","user_data","cantiere_data","selectedCantiereId","selectedCantiereName","isImpersonating","impersonatedUser"].forEach(a=>{localStorage.removeItem(a)})};o.interceptors.request.use(a=>{let t=n();return t&&(a.headers.Authorization="Bearer ".concat(t)),a},a=>Promise.reject(a)),o.interceptors.response.use(a=>a,a=>{var t;return(null==(t=a.response)?void 0:t.status)===401&&(console.log("\uD83D\uDEA8 API: Token non valido o scaduto, pulizia dati e reindirizzamento"),r(),window.location.pathname.includes("/login")||(window.location.href="/login")),Promise.reject(a)});let p={get:async(a,t)=>(await o.get(a,t)).data,post:async(a,t,e)=>(await o.post(a,t,e)).data,put:async(a,t,e)=>(await o.put(a,t,e)).data,delete:async(a,t)=>(await o.delete(a,t)).data},s={login:async a=>{let t=new FormData;return t.append("username",a.username),t.append("password",a.password),(await o.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:a=>p.post("/api/auth/login/cantiere",{codice_univoco:a.codice_cantiere,password:a.password_cantiere}),verifyToken:()=>p.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},d={getCavi:(a,t)=>p.get("/api/cavi/".concat(a),{params:t}),getCavo:(a,t)=>p.get("/api/cavi/".concat(a,"/").concat(t)),checkCavo:(a,t)=>p.get("/api/cavi/".concat(a,"/check/").concat(t)),createCavo:(a,t)=>p.post("/api/cavi/".concat(a),t),updateCavo:(a,t,e)=>p.put("/api/cavi/".concat(a,"/").concat(t),e),deleteCavo:(a,t,e)=>p.delete("/api/cavi/".concat(a,"/").concat(t),{data:e}),updateMetriPosati:(a,t,e,i,c)=>p.post("/api/cavi/".concat(a,"/").concat(t,"/metri-posati"),{metri_posati:e,id_bobina:i,force_over:c||!1}),updateBobina:(a,t,e,i)=>p.post("/api/cavi/".concat(a,"/").concat(t,"/bobina"),{id_bobina:e,force_over:i||!1}),cancelInstallation:(a,t)=>p.post("/api/cavi/".concat(a,"/").concat(t,"/cancel-installation")),collegaCavo:(a,t,e,i)=>p.post("/api/cavi/".concat(a,"/").concat(t,"/collegamento"),{lato:e,responsabile:i}),scollegaCavo:(a,t,e)=>{let i={};return e&&(i.data={lato:e}),p.delete("/api/cavi/".concat(a,"/").concat(t,"/collegamento"),i)},markAsSpare:function(a,t,e){let i=!(arguments.length>3)||void 0===arguments[3]||arguments[3];return e?p.post("/api/cavi/".concat(a,"/").concat(t,"/mark-as-spare"),{force:i}):p.post("/api/cavi/".concat(a,"/").concat(t,"/reactivate-spare"),{})},debugCavi:a=>p.get("/api/cavi/debug/".concat(a)),debugCaviRaw:a=>p.get("/api/cavi/debug/raw/".concat(a))},l={getBobine:(a,t)=>p.get("/api/parco-cavi/".concat(a),{params:t}),getBobina:(a,t)=>p.get("/api/parco-cavi/".concat(a,"/").concat(t)),getBobineCompatibili:(a,t)=>p.get("/api/parco-cavi/".concat(a,"/compatibili"),{params:t}),createBobina:(a,t)=>p.post("/api/parco-cavi/".concat(a),t),updateBobina:(a,t,e)=>p.put("/api/parco-cavi/".concat(a,"/").concat(t),e),deleteBobina:(a,t)=>p.delete("/api/parco-cavi/".concat(a,"/").concat(t)),isFirstBobinaInsertion:a=>p.get("/api/parco-cavi/".concat(a,"/is-first-insertion")),updateBobina:(a,t,e)=>p.put("/api/parco-cavi/".concat(a,"/").concat(t),e),deleteBobina:(a,t)=>p.delete("/api/parco-cavi/".concat(a,"/").concat(t)),checkDisponibilita:(a,t,e)=>p.get("/api/parco-cavi/".concat(a,"/").concat(t,"/disponibilita"),{params:{metri_richiesti:e}})},g={getComande:a=>p.get("/api/comande/cantiere/".concat(a)),getComanda:(a,t)=>p.get("/api/comande/".concat(t)),getCaviComanda:a=>p.get("/api/comande/".concat(a,"/cavi")),createComanda:(a,t)=>p.post("/api/comande/cantiere/".concat(a),t),createComandaWithCavi:(a,t,e)=>p.post("/api/comande/cantiere/".concat(a,"/crea-con-cavi"),t,{params:{lista_id_cavi:e}}),updateDatiComanda:(a,t,e)=>p.put("/api/comande/".concat(a,"/").concat(t),e),updateComanda:(a,t,e)=>p.put("/api/comande/cantiere/".concat(a,"/").concat(t),e),deleteComanda:(a,t)=>p.delete("/api/comande/cantiere/".concat(a,"/").concat(t)),assegnaCavi:(a,t,e)=>p.post("/api/comande/cantiere/".concat(a,"/").concat(t,"/assegna-cavi"),{cavi_ids:e}),rimuoviCavi:(a,t,e)=>p.delete("/api/comande/cantiere/".concat(a,"/").concat(t,"/rimuovi-cavi"),{data:{cavi_ids:e}}),getStatistiche:a=>p.get("/api/comande/cantiere/".concat(a,"/statistiche")),cambiaStato:(a,t,e)=>p.put("/api/comande/cantiere/".concat(a,"/").concat(t,"/stato"),{nuovo_stato:e})},u={getResponsabili:a=>p.get("/api/responsabili/cantiere/".concat(a)),createResponsabile:(a,t)=>p.post("/api/responsabili/".concat(a),t),updateResponsabile:(a,t,e)=>p.put("/api/responsabili/".concat(a,"/").concat(t),e),deleteResponsabile:(a,t)=>p.delete("/api/responsabili/".concat(a,"/").concat(t))},m={getCertificazioni:(a,t)=>p.get("/api/cantieri/".concat(a,"/certificazioni"),{params:t?{filtro_cavo:t}:{}}),createCertificazione:(a,t)=>p.post("/api/cantieri/".concat(a,"/certificazioni"),t),getCertificazione:(a,t)=>p.get("/api/cantieri/".concat(a,"/certificazioni/").concat(t)),updateCertificazione:(a,t,e)=>p.put("/api/cantieri/".concat(a,"/certificazioni/").concat(t),e),deleteCertificazione:(a,t)=>p.delete("/api/cantieri/".concat(a,"/certificazioni/").concat(t)),generatePDF:(a,t)=>p.get("/api/cantieri/".concat(a,"/certificazioni/").concat(t,"/pdf"),{responseType:"blob"}),getStatistiche:a=>p.get("/api/cantieri/".concat(a,"/certificazioni/statistiche")),exportCertificazioni:(a,t)=>p.get("/api/cantieri/".concat(a,"/certificazioni/export"),{params:t,responseType:"blob"}),generateReport:function(a){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"completo";return p.get("/api/cantieri/".concat(a,"/certificazioni/report/").concat(t))},bulkDelete:(a,t)=>p.post("/api/cantieri/".concat(a,"/certificazioni/bulk-delete"),{ids:t}),generateBulkPdf:(a,t)=>p.post("/api/cantieri/".concat(a,"/certificazioni/bulk-pdf"),{ids:t},{responseType:"blob"}),validateCertificazione:(a,t)=>p.post("/api/cantieri/".concat(a,"/certificazioni/validate"),t)},v={getStrumenti:a=>p.get("/api/cantieri/".concat(a,"/strumenti")),createStrumento:(a,t)=>p.post("/api/cantieri/".concat(a,"/strumenti"),t),updateStrumento:(a,t,e)=>p.put("/api/cantieri/".concat(a,"/strumenti/").concat(t),e),deleteStrumento:(a,t)=>p.delete("/api/cantieri/".concat(a,"/strumenti/").concat(t))},b={getRapporti:function(a){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return p.get("/api/cantieri/".concat(a,"/rapporti"),{params:{skip:t,limit:e}})},createRapporto:(a,t)=>p.post("/api/cantieri/".concat(a,"/rapporti"),t),getRapporto:(a,t)=>p.get("/api/cantieri/".concat(a,"/rapporti/").concat(t)),updateRapporto:(a,t,e)=>p.put("/api/cantieri/".concat(a,"/rapporti/").concat(t),e),deleteRapporto:(a,t)=>p.delete("/api/cantieri/".concat(a,"/rapporti/").concat(t)),aggiornaStatistiche:(a,t)=>p.post("/api/cantieri/".concat(a,"/rapporti/").concat(t,"/aggiorna-statistiche"))},C={getNonConformita:a=>p.get("/api/cantieri/".concat(a,"/non-conformita")),createNonConformita:(a,t)=>p.post("/api/cantieri/".concat(a,"/non-conformita"),t),updateNonConformita:(a,t,e)=>p.put("/api/cantieri/".concat(a,"/non-conformita/").concat(t),e),deleteNonConformita:(a,t)=>p.delete("/api/cantieri/".concat(a,"/non-conformita/").concat(t))},f={importCavi:(a,t,e)=>{let i=new FormData;return i.append("file",t),i.append("revisione",e),p.post("/api/excel/".concat(a,"/import-cavi"),i,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(a,t)=>{let e=new FormData;return e.append("file",t),p.post("/api/excel/".concat(a,"/import-parco-bobine"),e,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:a=>p.get("/api/excel/".concat(a,"/export-cavi"),{responseType:"blob"}),exportBobine:a=>p.get("/api/excel/".concat(a,"/export-parco-bobine"),{responseType:"blob"})},h={getReportAvanzamento:a=>p.get("/api/reports/".concat(a,"/avanzamento")),getReportBOQ:a=>p.get("/api/reports/".concat(a,"/boq")),getReportUtilizzoBobine:a=>p.get("/api/reports/".concat(a,"/storico-bobine")),getReportProgress:a=>p.get("/api/reports/".concat(a,"/progress")),getReportPosaPeriodo:(a,t,e)=>{let i=new URLSearchParams;t&&i.append("data_inizio",t),e&&i.append("data_fine",e);let c=i.toString();return p.get("/api/reports/".concat(a,"/posa-periodo").concat(c?"?".concat(c):""))}},_={getCantieri:()=>p.get("/api/cantieri"),getCantiere:a=>p.get("/api/cantieri/".concat(a)),createCantiere:a=>p.post("/api/cantieri",a),updateCantiere:(a,t)=>p.put("/api/cantieri/".concat(a),t),getCantiereStatistics:a=>p.get("/api/cantieri/".concat(a,"/statistics")),getWeatherData:a=>p.get("/api/cantieri/".concat(a,"/weather"))},w={getUsers:()=>p.get("/api/users"),getUser:a=>p.get("/api/users/".concat(a)),createUser:a=>p.post("/api/users",a),updateUser:(a,t)=>p.put("/api/users/".concat(a),t),deleteUser:a=>p.delete("/api/users/".concat(a)),toggleUserStatus:a=>p.get("/api/users/toggle/".concat(a)),checkExpiredUsers:()=>p.get("/api/users/check-expired"),impersonateUser:a=>p.post("/api/auth/impersonate",{user_id:a}),getDatabaseData:()=>p.get("/api/users/db-raw"),resetDatabase:()=>p.post("/api/admin/reset-database")}}}]);