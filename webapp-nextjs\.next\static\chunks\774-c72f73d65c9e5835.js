"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[774],{381:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>en,ZL:()=>ee,bL:()=>X,bm:()=>eo,hE:()=>ea,hJ:()=>et,l9:()=>$});var a=r(12115),n=r(85185),o=r(6101),l=r(46081),i=r(61285),s=r(5845),u=r(19178),d=r(25519),c=r(34378),p=r(28905),f=r(63655),h=r(92293),y=r(93795),g=r(38168),m=r(99708),v=r(95155),x="Dialog",[k,D]=(0,l.A)(x),[j,A]=k(x),b=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:l,modal:u=!0}=e,d=a.useRef(null),c=a.useRef(null),[p,f]=(0,s.i)({prop:n,defaultProp:null!=o&&o,onChange:l,caller:x});return(0,v.jsx)(j,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:a.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};b.displayName=x;var C="DialogTrigger",R=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,l=A(C,r),i=(0,o.s)(t,l.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...a,ref:i,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})});R.displayName=C;var w="DialogPortal",[I,M]=k(w,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,l=A(w,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:a.Children.map(n,e=>(0,v.jsx)(p.C,{present:r||l.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:e})}))})};P.displayName=w;var N="DialogOverlay",O=a.forwardRef((e,t)=>{let r=M(N,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=A(N,e.__scopeDialog);return o.modal?(0,v.jsx)(p.C,{present:a||o.open,children:(0,v.jsx)(E,{...n,ref:t})}):null});O.displayName=N;var _=(0,m.TL)("DialogOverlay.RemoveScroll"),E=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=A(N,r);return(0,v.jsx)(y.A,{as:_,allowPinchZoom:!0,shards:[n.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":H(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),F="DialogContent",q=a.forwardRef((e,t)=>{let r=M(F,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,o=A(F,e.__scopeDialog);return(0,v.jsx)(p.C,{present:a||o.open,children:o.modal?(0,v.jsx)(z,{...n,ref:t}):(0,v.jsx)(V,{...n,ref:t})})});q.displayName=F;var z=a.forwardRef((e,t)=>{let r=A(F,e.__scopeDialog),l=a.useRef(null),i=(0,o.s)(t,r.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(S,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),V=a.forwardRef((e,t)=>{let r=A(F,e.__scopeDialog),n=a.useRef(!1),o=a.useRef(!1);return(0,v.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(n.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{var a,l;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),S=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=A(F,r),p=a.useRef(null),f=(0,o.s)(t,p);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(d.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,v.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:c.titleId}),(0,v.jsx)(Q,{contentRef:p,descriptionId:c.descriptionId})]})]})}),T="DialogTitle",B=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=A(T,r);return(0,v.jsx)(f.sG.h2,{id:n.titleId,...a,ref:t})});B.displayName=T;var G="DialogDescription",L=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=A(G,r);return(0,v.jsx)(f.sG.p,{id:n.descriptionId,...a,ref:t})});L.displayName=G;var W="DialogClose",Z=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=A(W,r);return(0,v.jsx)(f.sG.button,{type:"button",...a,ref:t,onClick:(0,n.m)(e.onClick,()=>o.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=W;var U="DialogTitleWarning",[J,K]=(0,l.q)(U,{contentName:F,titleName:T,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=K(U),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},Q=e=>{let{contentRef:t,descriptionId:r}=e,n=K("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return a.useEffect(()=>{var e;let a=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&a&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},X=b,$=R,ee=P,et=O,er=q,ea=B,en=L,eo=Z},23227:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},28883:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},32919:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,t,r)=>{var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},47924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},75525:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);