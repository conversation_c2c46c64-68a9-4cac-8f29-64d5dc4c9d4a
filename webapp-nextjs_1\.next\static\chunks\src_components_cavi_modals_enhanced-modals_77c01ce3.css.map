{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/components/cavi/modals/enhanced-modals.css"], "sourcesContent": ["/* Enhanced Modal Styles for CABLYS Cable Management */\n\n/* Modal overlay improvements */\n.modal-overlay {\n  backdrop-filter: blur(2px);\n  background-color: rgba(0, 0, 0, 0.5);\n  transition: all 0.2s ease-in-out;\n}\n\n/* Enhanced modal content */\n.enhanced-modal-content {\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n  border-radius: 12px;\n  border: 1px solid rgba(229, 231, 235, 0.8);\n}\n\n/* Cable ID badge styling */\n.cable-id-badge {\n  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\n  border: 1px solid #93c5fd;\n  color: #1e40af;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-weight: 600;\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 0.875rem;\n  letter-spacing: 0.025em;\n}\n\n/* Enhanced button states */\n.enhanced-button {\n  transition: all 0.15s ease-in-out;\n  position: relative;\n  overflow: hidden;\n}\n\n.enhanced-button:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.enhanced-button:active {\n  transform: translateY(0);\n}\n\n.enhanced-button:disabled {\n  transform: none;\n  box-shadow: none;\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* Loading button animation */\n.enhanced-button.loading::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(255, 255, 255, 0.2),\n    transparent\n  );\n  animation: loading-shimmer 1.5s infinite;\n}\n\n@keyframes loading-shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n\n/* Status indicators */\n.status-indicator {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease-in-out;\n}\n\n.status-indicator.installed {\n  background-color: #dcfce7;\n  color: #166534;\n  border: 1px solid #bbf7d0;\n}\n\n.status-indicator.connected {\n  background-color: #dbeafe;\n  color: #1e40af;\n  border: 1px solid #93c5fd;\n}\n\n.status-indicator.not-certified {\n  background-color: #fef3c7;\n  color: #92400e;\n  border: 1px solid #fcd34d;\n}\n\n.status-indicator.error {\n  background-color: #fee2e2;\n  color: #991b1b;\n  border: 1px solid #fca5a5;\n}\n\n/* Status dots */\n.status-dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  display: inline-block;\n  position: relative;\n}\n\n.status-dot.green {\n  background-color: #10b981;\n  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);\n}\n\n.status-dot.red {\n  background-color: #ef4444;\n  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);\n}\n\n.status-dot.orange {\n  background-color: #f59e0b;\n  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);\n}\n\n.status-dot.pulsing {\n  animation: pulse-dot 2s infinite;\n}\n\n@keyframes pulse-dot {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n/* Enhanced form controls */\n.enhanced-input {\n  transition: all 0.2s ease-in-out;\n  border: 2px solid #e5e7eb;\n}\n\n.enhanced-input:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n  outline: none;\n}\n\n.enhanced-input.error {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.enhanced-input.success {\n  border-color: #10b981;\n  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);\n}\n\n/* Enhanced radio buttons */\n.enhanced-radio {\n  position: relative;\n  cursor: pointer;\n  transition: all 0.2s ease-in-out;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.enhanced-radio:hover {\n  border-color: #d1d5db;\n  background-color: #f9fafb;\n}\n\n.enhanced-radio.selected {\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n}\n\n.enhanced-radio input[type=\"radio\"] {\n  accent-color: #3b82f6;\n}\n\n/* Enhanced tabs */\n.enhanced-tabs {\n  border-bottom: 2px solid #e5e7eb;\n}\n\n.enhanced-tab {\n  position: relative;\n  padding: 12px 16px;\n  font-weight: 500;\n  color: #6b7280;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s ease-in-out;\n  cursor: pointer;\n}\n\n.enhanced-tab:hover {\n  color: #374151;\n  background-color: #f9fafb;\n}\n\n.enhanced-tab.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n  background-color: #eff6ff;\n}\n\n.enhanced-tab.active::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background-color: #3b82f6;\n}\n\n/* Enhanced alerts */\n.enhanced-alert {\n  border-radius: 8px;\n  padding: 12px;\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  font-size: 0.875rem;\n}\n\n.enhanced-alert.info {\n  background-color: #eff6ff;\n  border: 1px solid #bfdbfe;\n  color: #1e40af;\n}\n\n.enhanced-alert.warning {\n  background-color: #fffbeb;\n  border: 1px solid #fcd34d;\n  color: #92400e;\n}\n\n.enhanced-alert.error {\n  background-color: #fef2f2;\n  border: 1px solid #fca5a5;\n  color: #991b1b;\n}\n\n.enhanced-alert.success {\n  background-color: #f0fdf4;\n  border: 1px solid #bbf7d0;\n  color: #166534;\n}\n\n/* Toast notifications */\n.toast-notification {\n  position: fixed;\n  top: 1rem;\n  right: 1rem;\n  z-index: 9999;\n  max-width: 400px;\n  animation: toast-slide-in 0.3s ease-out;\n}\n\n.toast-notification.exiting {\n  animation: toast-slide-out 0.3s ease-in;\n}\n\n@keyframes toast-slide-in {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes toast-slide-out {\n  from {\n    transform: translateX(0);\n    opacity: 1;\n  }\n  to {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .enhanced-modal-content {\n    margin: 1rem;\n    width: calc(100% - 2rem);\n  }\n  \n  .cable-id-badge {\n    font-size: 0.75rem;\n    padding: 2px 6px;\n  }\n  \n  .enhanced-button {\n    padding: 8px 12px;\n    font-size: 0.875rem;\n  }\n  \n  .toast-notification {\n    top: 0.5rem;\n    right: 0.5rem;\n    left: 0.5rem;\n    max-width: none;\n  }\n}\n\n/* Accessibility improvements */\n.enhanced-modal-content:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n.enhanced-button:focus-visible {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n.enhanced-input:focus-visible {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .cable-id-badge {\n    border-width: 2px;\n  }\n  \n  .status-indicator {\n    border-width: 2px;\n  }\n  \n  .enhanced-input {\n    border-width: 3px;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;;AAOA;;;;;;AAOA;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;AAQA;;;;;;;;;;;AAgBA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;;;AASF;;;;;AAgBA;EACE;;;;EAQA", "debugId": null}}]}