(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23652:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>O});var s=t(60687),r=t(43210),i=t(44493),l=t(55527),n=t(63143),d=t(48730),c=t(5336),o=t(88233);function u({user:e,onEdit:a,onToggleStatus:t,onDelete:r}){return(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),a()},type:"button",className:"p-1.5 rounded hover:bg-blue-50 transition-colors",title:"Modifica utente",children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),t()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded transition-colors ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-slate-50"}`,title:e.abilitato?"Disabilita utente":"Abilita utente",children:e.abilitato?(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),r()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded transition-colors ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-red-50"}`,title:"Elimina utente",children:(0,s.jsx)(o.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})})]})}var m=t(96834),x=t(15391),p=t(6211),h=t(56770),j=t(63213),v=t(16189),g=t(62185),f=t(29523),b=t(89667),N=t(80013),y=t(15079),w=t(56896),A=t(81806),z=t(12597),k=t(13861),E=t(11860),C=t(8819);function S({user:e,onSave:a,onCancel:t}){let[n,d]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[c,o]=(0,r.useState)({}),[u,m]=(0,r.useState)(!1),[x,p]=(0,r.useState)(""),[h,j]=(0,r.useState)(!1),v=(e,a)=>{d(t=>({...t,[e]:a})),c[e]&&o(a=>({...a,[e]:""}))},S=()=>{let a=(0,A.GN)({username:n.username,password:e?void 0:n.password,ragione_sociale:n.ragione_sociale,email:n.email,vat:n.vat,indirizzo:n.indirizzo,nazione:n.nazione,referente_aziendale:n.referente_aziendale});return o(a.errors),a.isValid},R=async t=>{t.preventDefault();let s=`user-form-${e?.id_utente||"new"}-${Date.now()}`;if(!(0,A.Eb)(s,5,6e4))return void p("Troppi tentativi. Riprova tra un minuto.");if(S()){m(!0),p("");try{let t,s={...n};e||(s.ruolo="user"),e&&!s.password.trim()&&delete s.password,s.data_scadenza&&(s.data_scadenza=s.data_scadenza),t=e?await g.dG.updateUser(e.id_utente,s):await g.dG.createUser(s),a(t)}catch(e){p(e.response?.data?.detail||e.message||"Errore durante il salvataggio dell'utente")}finally{m(!1)}}};return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:e?`Modifica Utente: ${e.username}`:"Crea Nuovo Utente Standard"})}),(0,s.jsxs)(i.Wu,{children:[x&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-600",children:x})}),(0,s.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"username",children:"Username *"}),(0,s.jsx)(b.p,{id:"username",value:n.username,onChange:e=>v("username",e.target.value),disabled:u,className:c.username?"border-red-500":""}),c.username&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.username})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"password",children:e?"Nuova Password (lascia vuoto per non modificare)":"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.p,{id:"password",type:h?"text":"password",value:n.password,onChange:e=>v("password",e.target.value),disabled:u,className:c.password?"border-red-500 pr-10":"pr-10"}),(0,s.jsx)(f.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>j(!h),disabled:u,children:h?(0,s.jsx)(z.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(k.A,{className:"h-4 w-4 text-gray-400"})})]}),c.password&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.password})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"ragione_sociale",children:"Ragione Sociale *"}),(0,s.jsx)(b.p,{id:"ragione_sociale",value:n.ragione_sociale,onChange:e=>v("ragione_sociale",e.target.value),disabled:u,className:c.ragione_sociale?"border-red-500":""}),c.ragione_sociale&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.ragione_sociale})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(b.p,{id:"email",type:"email",value:n.email,onChange:e=>v("email",e.target.value),disabled:u,className:c.email?"border-red-500":""}),c.email&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"indirizzo",children:"Indirizzo"}),(0,s.jsx)(b.p,{id:"indirizzo",value:n.indirizzo,onChange:e=>v("indirizzo",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"nazione",children:"Nazione"}),(0,s.jsx)(b.p,{id:"nazione",value:n.nazione,onChange:e=>v("nazione",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"vat",children:"VAT"}),(0,s.jsx)(b.p,{id:"vat",value:n.vat,onChange:e=>v("vat",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,s.jsx)(b.p,{id:"referente_aziendale",value:n.referente_aziendale,onChange:e=>v("referente_aziendale",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,s.jsx)(b.p,{id:"data_scadenza",type:"date",value:n.data_scadenza,onChange:e=>v("data_scadenza",e.target.value),disabled:u})]}),e?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsxs)(y.l6,{value:n.ruolo,onValueChange:e=>v("ruolo",e),disabled:u,children:[(0,s.jsx)(y.bq,{children:(0,s.jsx)(y.yv,{})}),(0,s.jsxs)(y.gC,{children:[(0,s.jsx)(y.eb,{value:"user",children:"User"}),(0,s.jsx)(y.eb,{value:"cantieri_user",children:"Cantieri User"})]})]})]}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600",children:"User (Standard)"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(w.S,{id:"abilitato",checked:n.abilitato,onCheckedChange:e=>v("abilitato",e),disabled:u||e&&"owner"===e.ruolo}),(0,s.jsx)(N.J,{htmlFor:"abilitato",children:"Utente abilitato"})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,s.jsx)(l.tA,{type:"button",onClick:t,disabled:u,icon:(0,s.jsx)(E.A,{className:"h-4 w-4"}),children:"Annulla"}),(0,s.jsx)(l.jn,{type:"submit",loading:u,icon:(0,s.jsx)(C.A,{className:"h-4 w-4"}),glow:!0,children:u?"Salvataggio...":"Salva"})]})]})]})]})}var R=t(61611),T=t(78122),_=t(41862);function D(){let[e,a]=(0,r.useState)(null),[t,n]=(0,r.useState)(!1),[d,c]=(0,r.useState)(""),o=async()=>{n(!0),c("");try{let e=await g.dG.getDatabaseData();a(e)}catch(e){c(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati del database")}finally{n(!1)}},u=(e,a,t)=>{if(!a||0===a.length)return(0,s.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",t]});let r=Object.keys(a[0]);return(0,s.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,s.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:t}),(0,s.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,s.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,s.jsxs)(p.XI,{children:[(0,s.jsx)(p.A0,{className:"sticky top-0 bg-slate-50",children:(0,s.jsx)(p.Hj,{children:r.map(e=>(0,s.jsx)(p.nd,{className:"font-medium",children:e},e))})}),(0,s.jsx)(p.BF,{children:a.map((e,a)=>(0,s.jsx)(p.Hj,{children:r.map(a=>(0,s.jsx)(p.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,s.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},m=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(R.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,s.jsx)(l.jn,{size:"sm",onClick:o,loading:t,icon:(0,s.jsx)(T.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(k.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),t?(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(_.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,s.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):d?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,s.jsx)("p",{className:"text-red-600",children:d})]}):e?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),m.map(a=>e[a.key]&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),u(a.key,e[a.key],a.title)]},a.key)),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:m.map(a=>(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,s.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var I=t(62688);let M=(0,I.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var V=t(43649);function P(){let[e,a]=(0,r.useState)(""),[t,n]=(0,r.useState)(!1),[d,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)(""),[x,p]=(0,r.useState)(""),h=async()=>{if("RESET DATABASE"!==e||!t)return void m("Conferma richiesta per procedere con il reset");c(!0),m(""),p("");try{await g.dG.resetDatabase(),p("Database resettato con successo! Tutti i dati sono stati eliminati."),a(""),n(!1)}catch(e){m(e.response?.data?.detail||e.message||"Errore durante il reset del database")}finally{c(!1)}},j="RESET DATABASE"===e&&t&&!d;return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(M,{className:"h-5 w-5"}),"Reset Database"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(V.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,s.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,s.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,s.jsx)("li",{children:"Tutti i cavi installati"}),(0,s.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,s.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,s.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,s.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),u&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:u})}),x&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-green-600",children:x})}),(0,s.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(N.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,s.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,s.jsx)(b.p,{id:"confirm-text",value:e,onChange:e=>a(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:d,className:"RESET DATABASE"===e?"border-green-500":""})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(w.S,{id:"confirm-checkbox",checked:t,onCheckedChange:n,disabled:d}),(0,s.jsx)(N.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Stato Conferma:"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${"RESET DATABASE"===e?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===e?"✓ Corretto":"✗ Richiesto"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${t?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Checkbox confermata: ",t?"✓ S\xec":"✗ Richiesta"]})]})]})]}),(0,s.jsx)(l.Qi,{onClick:h,disabled:!j,className:"w-full",size:"lg",loading:d,icon:(0,s.jsx)(o.A,{className:"h-5 w-5"}),glow:!0,children:d?"Reset in corso...":"RESET DATABASE - ELIMINA TUTTI I DATI"}),!j&&(0,s.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per abilitare il reset"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,s.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,s.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,s.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,s.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var G=t(23361);let L=(0,I.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),U=(0,I.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var F=t(10022),q=t(96474);function B(){let[e,a]=(0,r.useState)("categorie");return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(G.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(G.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,s.jsxs)(h.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,s.jsxs)(h.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(h.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,s.jsx)(L,{className:"h-4 w-4"}),"Categorie"]}),(0,s.jsxs)(h.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,s.jsx)(U,{className:"h-4 w-4"}),"Produttori"]}),(0,s.jsxs)(h.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,s.jsx)(F.A,{className:"h-4 w-4"}),"Standard"]}),(0,s.jsxs)(h.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,s.jsxs)(h.av,{value:"categorie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,s.jsxs)(f.$,{children:[(0,s.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(L,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,s.jsxs)(h.av,{value:"produttori",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,s.jsxs)(f.$,{children:[(0,s.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(U,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,s.jsxs)(h.av,{value:"standard",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,s.jsxs)(f.$,{children:[(0,s.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(F.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,s.jsxs)(h.av,{value:"tipologie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,s.jsxs)(f.$,{children:[(0,s.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(G.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var Z=t(41312);let $=(0,I.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),H=(0,I.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function O(){let e=(0,v.useRouter)(),[a,t]=(0,r.useState)("visualizza-utenti"),[d,c]=(0,r.useState)(""),[o,f]=(0,r.useState)([]),[b,N]=(0,r.useState)([]),[y,w]=(0,r.useState)(!0),[A,z]=(0,r.useState)(""),[k,E]=(0,r.useState)(null),[C,T]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:I,impersonateUser:V}=(0,j.A)(),L=async()=>{try{if(w(!0),z(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await g.dG.getUsers();f(e)}else if("cantieri"===a){let e=await g._I.getCantieri();N(e)}}catch(e){z(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati")}finally{w(!1)}},U=e=>{E(e),t("modifica-utente")},F=async e=>{try{await g.dG.toggleUserStatus(e),L()}catch(e){z(e.response?.data?.detail||"Errore durante la modifica dello stato utente")}},q=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await g.dG.deleteUser(e),L()}catch(e){z(e.response?.data?.detail||"Errore durante l'eliminazione dell'utente")}},O=e=>{E(null),t("visualizza-utenti"),L()},X=()=>{E(null),t("visualizza-utenti")},J=async a=>{try{await V(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){z(e.response?.data?.detail||e.message||"Errore durante l'impersonificazione")}},K=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let t=(0,x.qn)(a);return(0,s.jsx)(m.E,{className:t.badge,children:e})},Q=(e,a)=>{let t="SUCCESS",r="Attivo";if(e){if(a){let e=new Date(a),s=new Date;e<s?(t="ERROR",r="Scaduto"):e.getTime()-s.getTime()<6048e5&&(t="WARNING",r="In Scadenza")}}else t="ERROR",r="Disabilitato";let i=(0,x.qn)(t);return(0,s.jsx)(m.E,{className:i.badge,children:r})};return(o.filter(e=>e.username?.toLowerCase().includes(d.toLowerCase())||e.ragione_sociale?.toLowerCase().includes(d.toLowerCase())||e.email?.toLowerCase().includes(d.toLowerCase())),I&&"owner"===I.ruolo)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,s.jsxs)(h.tU,{value:a,onValueChange:t,className:"w-full",children:[(0,s.jsxs)(h.j7,{className:`grid w-full ${k?"grid-cols-6":"grid-cols-5"}`,children:[(0,s.jsxs)(h.Xi,{value:"visualizza-utenti",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4"}),"Visualizza Utenti"]}),(0,s.jsxs)(h.Xi,{value:"crea-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)($,{className:"h-4 w-4"}),"Crea Nuovo Utente"]}),k&&(0,s.jsxs)(h.Xi,{value:"modifica-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),"Modifica Utente"]}),(0,s.jsxs)(h.Xi,{value:"database-tipologie-cavi",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),"Database Tipologie Cavi"]}),(0,s.jsxs)(h.Xi,{value:"visualizza-database-raw",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(R.A,{className:"h-4 w-4"}),"Visualizza Database Raw"]}),(0,s.jsxs)(h.Xi,{value:"reset-database",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(M,{className:"h-4 w-4"}),"Reset Database"]})]}),(0,s.jsxs)(h.av,{value:"visualizza-utenti",className:"space-y-4",children:[A&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:A})}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Lista Utenti"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(p.XI,{className:"min-w-full",children:[(0,s.jsx)(p.A0,{children:(0,s.jsxs)(p.Hj,{children:[(0,s.jsx)(p.nd,{className:"w-[60px] text-center",children:"ID"}),(0,s.jsx)(p.nd,{className:"w-[120px]",children:"Username"}),(0,s.jsx)(p.nd,{className:"w-[100px] text-center",children:"Password"}),(0,s.jsx)(p.nd,{className:"w-[100px] text-center",children:"Ruolo"}),(0,s.jsx)(p.nd,{className:"w-[250px]",children:"Ragione Sociale"}),(0,s.jsx)(p.nd,{className:"w-[200px]",children:"Email"}),(0,s.jsx)(p.nd,{className:"w-[120px] text-center",children:"VAT"}),(0,s.jsx)(p.nd,{className:"w-[100px] text-center",children:"Nazione"}),(0,s.jsx)(p.nd,{className:"w-[150px]",children:"Referente"}),(0,s.jsx)(p.nd,{className:"w-[100px] text-center",children:"Scadenza"}),(0,s.jsx)(p.nd,{className:"w-[100px] text-center",children:"Stato"}),(0,s.jsx)(p.nd,{className:"w-[120px] text-center",children:"Azioni"})]})}),(0,s.jsx)(p.BF,{children:y?(0,s.jsx)(p.Hj,{children:(0,s.jsx)(p.nA,{colSpan:12,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===o.length?(0,s.jsx)(p.Hj,{children:(0,s.jsx)(p.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):o.map(e=>(0,s.jsxs)(p.Hj,{className:"hover:bg-slate-50",children:[(0,s.jsx)(p.nA,{className:"text-center text-slate-500 text-sm font-mono",children:e.id_utente}),(0,s.jsx)(p.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,s.jsx)(p.nA,{className:"text-center font-mono text-xs text-slate-500",children:e.password_plain||"***"}),(0,s.jsx)(p.nA,{className:"text-center",children:K(e.ruolo)}),(0,s.jsx)(p.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,s.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,s.jsx)(p.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,s.jsx)(p.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,s.jsx)(p.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,s.jsx)(p.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,s.jsx)(p.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,s.jsx)(p.nA,{className:"text-center",children:Q(e.abilitato,e.data_scadenza)}),(0,s.jsx)(p.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(u,{user:e,onEdit:()=>U(e),onToggleStatus:()=>F(e.id_utente),onDelete:()=>q(e.id_utente)}),(0,s.jsx)(l.jn,{size:"sm",onClick:()=>J(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,s.jsx)(H,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,s.jsx)(h.av,{value:"crea-utente",className:"space-y-4",children:(0,s.jsx)(S,{user:null,onSave:O,onCancel:X})}),k&&(0,s.jsx)(h.av,{value:"modifica-utente",className:"space-y-4",children:(0,s.jsx)(S,{user:k,onSave:O,onCancel:X})}),(0,s.jsx)(h.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,s.jsx)(B,{})}),(0,s.jsx)(h.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,s.jsx)(D,{})}),(0,s.jsx)(h.av,{value:"reset-database",className:"space-y-4",children:(0,s.jsx)(P,{})})]})})}):null}},24883:(e,a,t)=>{Promise.resolve().then(t.bind(t,23652))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40211:(e,a,t)=>{"use strict";t.d(a,{C1:()=>w,bL:()=>N});var s=t(43210),r=t(98599),i=t(11273),l=t(70569),n=t(65551),d=t(83721),c=t(18853),o=t(46059),u=t(14163),m=t(60687),x="Checkbox",[p,h]=(0,i.A)(x),[j,v]=p(x);function g(e){let{__scopeCheckbox:a,checked:t,children:r,defaultChecked:i,disabled:l,form:d,name:c,onCheckedChange:o,required:u,value:p="on",internal_do_not_use_render:h}=e,[v,g]=(0,n.i)({prop:t,defaultProp:i??!1,onChange:o,caller:x}),[f,b]=s.useState(null),[N,y]=s.useState(null),w=s.useRef(!1),A=!f||!!d||!!f.closest("form"),z={checked:v,disabled:l,setChecked:g,control:f,setControl:b,name:c,form:d,value:p,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!k(i)&&i,isFormControl:A,bubbleInput:N,setBubbleInput:y};return(0,m.jsx)(j,{scope:a,...z,children:"function"==typeof h?h(z):r})}var f="CheckboxTrigger",b=s.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:t,...i},n)=>{let{control:d,value:c,disabled:o,checked:x,required:p,setControl:h,setChecked:j,hasConsumerStoppedPropagationRef:g,isFormControl:b,bubbleInput:N}=v(f,e),y=(0,r.s)(n,h),w=s.useRef(x);return s.useEffect(()=>{let e=d?.form;if(e){let a=()=>j(w.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[d,j]),(0,m.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":k(x)?"mixed":x,"aria-required":p,"data-state":E(x),"data-disabled":o?"":void 0,disabled:o,value:c,...i,ref:y,onKeyDown:(0,l.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(t,e=>{j(e=>!!k(e)||!e),N&&b&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});b.displayName=f;var N=s.forwardRef((e,a)=>{let{__scopeCheckbox:t,name:s,checked:r,defaultChecked:i,required:l,disabled:n,value:d,onCheckedChange:c,form:o,...u}=e;return(0,m.jsx)(g,{__scopeCheckbox:t,checked:r,defaultChecked:i,disabled:n,required:l,onCheckedChange:c,name:s,form:o,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(b,{...u,ref:a,__scopeCheckbox:t}),e&&(0,m.jsx)(z,{__scopeCheckbox:t})]})})});N.displayName=x;var y="CheckboxIndicator",w=s.forwardRef((e,a)=>{let{__scopeCheckbox:t,forceMount:s,...r}=e,i=v(y,t);return(0,m.jsx)(o.C,{present:s||k(i.checked)||!0===i.checked,children:(0,m.jsx)(u.sG.span,{"data-state":E(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:a,style:{pointerEvents:"none",...e.style}})})});w.displayName=y;var A="CheckboxBubbleInput",z=s.forwardRef(({__scopeCheckbox:e,...a},t)=>{let{control:i,hasConsumerStoppedPropagationRef:l,checked:n,defaultChecked:o,required:x,disabled:p,name:h,value:j,form:g,bubbleInput:f,setBubbleInput:b}=v(A,e),N=(0,r.s)(t,b),y=(0,d.Z)(n),w=(0,c.X)(i);s.useEffect(()=>{if(!f)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!l.current;if(y!==n&&e){let t=new Event("click",{bubbles:a});f.indeterminate=k(n),e.call(f,!k(n)&&n),f.dispatchEvent(t)}},[f,y,n,l]);let z=s.useRef(!k(n)&&n);return(0,m.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??z.current,required:x,disabled:p,name:h,value:j,form:g,...a,tabIndex:-1,ref:N,style:{...a.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function k(e){return"indeterminate"===e}function E(e){return k(e)?"indeterminate":e?"checked":"unchecked"}z.displayName=A},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56770:(e,a,t)=>{"use strict";t.d(a,{tU:()=>O,av:()=>K,j7:()=>X,Xi:()=>J});var s=t(60687),r=t(43210),i=t(70569),l=t(11273),n=t(9510),d=t(98599),c=t(96963),o=t(14163),u=t(13495),m=t(65551),x=t(43),p="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},j="RovingFocusGroup",[v,g,f]=(0,n.N)(j),[b,N]=(0,l.A)(j,[f]),[y,w]=b(j),A=r.forwardRef((e,a)=>(0,s.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(z,{...e,ref:a})})}));A.displayName=j;var z=r.forwardRef((e,a)=>{let{__scopeRovingFocusGroup:t,orientation:l,loop:n=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:b,onEntryFocus:N,preventScrollOnEntryFocus:w=!1,...A}=e,z=r.useRef(null),k=(0,d.s)(a,z),E=(0,x.jH)(c),[C,R]=(0,m.i)({prop:v,defaultProp:f??null,onChange:b,caller:j}),[T,_]=r.useState(!1),D=(0,u.c)(N),I=g(t),M=r.useRef(!1),[V,P]=r.useState(0);return r.useEffect(()=>{let e=z.current;if(e)return e.addEventListener(p,D),()=>e.removeEventListener(p,D)},[D]),(0,s.jsx)(y,{scope:t,orientation:l,dir:E,loop:n,currentTabStopId:C,onItemFocus:r.useCallback(e=>R(e),[R]),onItemShiftTab:r.useCallback(()=>_(!0),[]),onFocusableItemAdd:r.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>P(e=>e-1),[]),children:(0,s.jsx)(o.sG.div,{tabIndex:T||0===V?-1:0,"data-orientation":l,...A,ref:k,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let a=!M.current;if(e.target===e.currentTarget&&a&&!T){let a=new CustomEvent(p,h);if(e.currentTarget.dispatchEvent(a),!a.defaultPrevented){let e=I().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),w)}}M.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>_(!1))})})}),k="RovingFocusGroupItem",E=r.forwardRef((e,a)=>{let{__scopeRovingFocusGroup:t,focusable:l=!0,active:n=!1,tabStopId:d,children:u,...m}=e,x=(0,c.B)(),p=d||x,h=w(k,t),j=h.currentTabStopId===p,f=g(t),{onFocusableItemAdd:b,onFocusableItemRemove:N,currentTabStopId:y}=h;return r.useEffect(()=>{if(l)return b(),()=>N()},[l,b,N]),(0,s.jsx)(v.ItemSlot,{scope:t,id:p,focusable:l,active:n,children:(0,s.jsx)(o.sG.span,{tabIndex:j?0:-1,"data-orientation":h.orientation,...m,ref:a,onMouseDown:(0,i.m)(e.onMouseDown,e=>{l?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let a=function(e,a,t){var s;let r=(s=e.key,"rtl"!==t?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===a&&["ArrowLeft","ArrowRight"].includes(r))&&!("horizontal"===a&&["ArrowUp","ArrowDown"].includes(r)))return C[r]}(e,h.orientation,h.dir);if(void 0!==a){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=f().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===a)t.reverse();else if("prev"===a||"next"===a){"prev"===a&&t.reverse();let s=t.indexOf(e.currentTarget);t=h.loop?function(e,a){return e.map((t,s)=>e[(a+s)%e.length])}(t,s+1):t.slice(s+1)}setTimeout(()=>S(t))}}),children:"function"==typeof u?u({isCurrentTabStop:j,hasTabStop:null!=y}):u})})});E.displayName=k;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,a=!1){let t=document.activeElement;for(let s of e)if(s===t||(s.focus({preventScroll:a}),document.activeElement!==t))return}var R=t(46059),T="Tabs",[_,D]=(0,l.A)(T,[N]),I=N(),[M,V]=_(T),P=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:i,defaultValue:l,orientation:n="horizontal",dir:d,activationMode:u="automatic",...p}=e,h=(0,x.jH)(d),[j,v]=(0,m.i)({prop:r,onChange:i,defaultProp:l??"",caller:T});return(0,s.jsx)(M,{scope:t,baseId:(0,c.B)(),value:j,onValueChange:v,orientation:n,dir:h,activationMode:u,children:(0,s.jsx)(o.sG.div,{dir:h,"data-orientation":n,...p,ref:a})})});P.displayName=T;var G="TabsList",L=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...i}=e,l=V(G,t),n=I(t);return(0,s.jsx)(A,{asChild:!0,...n,orientation:l.orientation,dir:l.dir,loop:r,children:(0,s.jsx)(o.sG.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:a})})});L.displayName=G;var U="TabsTrigger",F=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:l=!1,...n}=e,d=V(U,t),c=I(t),u=Z(d.baseId,r),m=$(d.baseId,r),x=r===d.value;return(0,s.jsx)(E,{asChild:!0,...c,focusable:!l,active:x,children:(0,s.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...n,ref:a,onMouseDown:(0,i.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;x||l||!e||d.onValueChange(r)})})})});F.displayName=U;var q="TabsContent",B=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:i,forceMount:l,children:n,...d}=e,c=V(q,t),u=Z(c.baseId,i),m=$(c.baseId,i),x=i===c.value,p=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(R.C,{present:l||x,children:({present:t})=>(0,s.jsx)(o.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!t,id:m,tabIndex:0,...d,ref:a,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&n})})});function Z(e,a){return`${e}-trigger-${a}`}function $(e,a){return`${e}-content-${a}`}B.displayName=q;var H=t(4780);let O=P,X=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(L,{ref:t,className:(0,H.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));X.displayName=L.displayName;let J=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(F,{ref:t,className:(0,H.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));J.displayName=F.displayName;let K=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(B,{ref:t,className:(0,H.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));K.displayName=B.displayName},56896:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var s=t(60687);t(43210);var r=t(40211),i=t(13964),l=t(4780);function n({className:e,...a}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},61611:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81806:(e,a,t)=>{"use strict";t.d(a,{Eb:()=>p,GN:()=>h,TU:()=>n});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},c=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},o=(e,a=255)=>l(e).length>a?{isValid:!1,error:`Testo troppo lungo (max ${a} caratteri)`}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},u=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},m=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},x=new Map,p=(e,a,t)=>{let s=Date.now(),r=x.get(e);return!r||s>r.resetTime?(x.set(e,{count:1,resetTime:s+t}),!0):!(r.count>=a)&&(r.count++,!0)},h=e=>{let a={},t=n(e.username);if(t.isValid||(a.username=t.error),e.password){let t=d(e.password);t.isValid||(a.password=t.error)}let s=u(e.ragione_sociale);if(s.isValid||(a.ragione_sociale=s.error),e.email){let t=c(e.email);t.isValid||(a.email=t.error)}if(e.vat){let t=m(e.vat);t.isValid||(a.vat=t.error)}if(e.indirizzo){let t=o(e.indirizzo,200);t.isValid||(a.indirizzo=t.error)}if(e.nazione){let t=o(e.nazione,50);t.isValid||(a.nazione=t.error)}if(e.referente_aziendale){let t=o(e.referente_aziendale,100);t.isValid||(a.referente_aziendale=t.error)}return{isValid:0===Object.keys(a).length,errors:a}}},83997:e=>{"use strict";e.exports=require("tty")},88091:(e,a,t)=>{Promise.resolve().then(t.bind(t,1132))},88233:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96504:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var s=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,538,658,952,797,995,615,109],()=>t(96504));module.exports=s})();