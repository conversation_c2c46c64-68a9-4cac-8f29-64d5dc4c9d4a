(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5022],{11785:(e,t,s)=>{Promise.resolve().then(s.bind(s,79680))},13587:(e,t,s)=>{"use strict";s.d(t,{u:()=>h});var a=s(95155);s(12115);var r=s(55365),i=s(30285),n=s(66695),l=s(53904),o=s(85339),d=s(47957),c=s(35169),m=s(35695),x=s(17522);function h(e){let{children:t,fallback:s,showBackButton:h=!0,backUrl:g="/cantieri"}=e,u=(0,m.useRouter)(),{cantiereId:p,cantiere:b,isValidCantiere:j,isLoading:v,error:N,clearError:f}=(0,x.jV)();if(v)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"Caricamento cantiere..."}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Verifica della selezione cantiere in corso"})]})})})})});if(N||!j){let e=N||"Nessun cantiere selezionato";return s?(0,a.jsx)(a.Fragment,{children:s}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsxs)(n.Zp,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center text-red-800",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 mr-2"}),"Problema con la selezione del cantiere"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)(r.Fc,{variant:"destructive",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsxs)(r.TN,{children:[(0,a.jsx)("strong",{children:"Errore:"})," ",e]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center text-gray-700",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Informazioni cantiere"]})}),(0,a.jsx)(n.Wu,{className:"space-y-3",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-600",children:"ID Cantiere:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-800",children:p||"Non disponibile"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-600",children:"Nome Cantiere:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-800",children:(null==b?void 0:b.commessa)||"Non disponibile"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-600",children:"Cantiere Valido:"}),(0,a.jsx)("span",{className:"ml-2 ".concat(j?"text-green-600":"text-red-600"),children:j?"S\xec":"No"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-gray-600",children:"localStorage ID:"}),(0,a.jsx)("span",{className:"ml-2 text-gray-800",children:localStorage.getItem("selectedCantiereId")||"Non presente"})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{className:"text-gray-700",children:"Azioni disponibili"})}),(0,a.jsxs)(n.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[h&&(0,a.jsxs)(i.$,{onClick:()=>u.push(g),variant:"default",className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Seleziona Cantiere"]}),N&&(0,a.jsxs)(i.$,{onClick:f,variant:"outline",className:"flex items-center",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Riprova"]}),(0,a.jsxs)(i.$,{onClick:()=>{localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data"),window.location.reload()},variant:"outline",className:"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Reset Dati Cantiere"]})]}),(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Suggerimento:"})," Se il problema persiste, prova a selezionare nuovamente un cantiere dalla pagina principale o contatta l'amministratore del sistema."]})})]})]})]})})}return(0,a.jsx)(a.Fragment,{children:t})}},17313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var a=s(95155),r=s(12115),i=s(60704),n=s(59434);let l=i.bL,o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});o.displayName=i.B8.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});d.displayName=i.l9.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=i.UC.displayName},17522:(e,t,s)=>{"use strict";s.d(t,{jV:()=>i});var a=s(12115),r=s(40283);function i(){let{cantiere:e,isLoading:t}=(0,r.A)(),[s,i]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),[o,d]=(0,a.useState)(null),c=e=>{if(null==e)return!1;let t="string"==typeof e?parseInt(e,10):e;return!isNaN(t)&&!(t<=0)||(console.warn("\uD83C\uDFD7️ useCantiere: ID cantiere non valido:",e),!1)};return(0,a.useEffect)(()=>{if(t)return void console.log("\uD83C\uDFD7️ useCantiere: Autenticazione in corso...");l(!0),d(null);try{let t=null;if((null==e?void 0:e.id_cantiere)&&c(e.id_cantiere))t=e.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere dal context (login cantiere):",t);else{let e=localStorage.getItem("cantiere_data");if(e)try{let s=JSON.parse(e);s.id_cantiere&&c(s.id_cantiere)&&(t=s.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da cantiere_data:",t))}catch(e){console.warn("\uD83C\uDFD7️ useCantiere: Errore parsing cantiere_data:",e)}if(!t){let e=localStorage.getItem("selectedCantiereId");e&&c(e)&&(t=parseInt(e,10),console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da selectedCantiereId:",t))}}t?(i(t),console.log("\uD83C\uDFD7️ useCantiere: Cantiere valido impostato:",t)):(console.warn("\uD83C\uDFD7️ useCantiere: Nessun cantiere valido trovato"),i(null),d("Nessun cantiere selezionato. Seleziona un cantiere per continuare."))}catch(e){console.error("\uD83C\uDFD7️ useCantiere: Errore nella gestione cantiere:",e),d("Errore nella gestione del cantiere selezionato."),i(null)}finally{l(!1)}},[e,t]),{cantiereId:s,cantiere:e,isValidCantiere:null!==s&&s>0,isLoading:n,error:o,validateCantiere:c,clearError:()=>d(null)}}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>l});var a=s(95155);s(12115);var r=s(99708),i=s(74466),n=s(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:i,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...d})}},40975:()=>{},55365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>o,TN:()=>d});var a=s(95155),r=s(12115),i=s(74466),n=s(59434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef((e,t)=>{let{className:s,variant:r,...i}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(l({variant:r}),s),...i})});o.displayName="Alert",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})}).displayName="AlertTitle";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",s),...r})});d.displayName="AlertDescription"},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(52596),r=s(39688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},79680:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var a=s(95155),r=s(12115);s(40975);var i=s(66695),n=s(30285),l=s(17313),o=s(40283),d=s(17522),c=s(13587);s(25731);var m=s(51154),x=s(85339),h=s(53904),g=s(16785),u=s(57434),p=s(37108),b=s(71539),j=s(40646),v=s(33109),N=s(79397),f=s(14186),y=s(69074),w=s(1243),_=s(83540),A=s(56965),z=s(94754),k=s(96025),C=s(16238),S=s(94517),I=s(21374);function P(){var e,t,s,P,T,R,B,D,L,E,M,F,q;let[O,U]=(0,r.useState)("avanzamento"),[Z,W]=(0,r.useState)("month"),[V,Q]=(0,r.useState)(null),[$,G]=(0,r.useState)(null),[K,X]=(0,r.useState)(null),[J,H]=(0,r.useState)(null),[Y,ee]=(0,r.useState)(!0),[et,es]=(0,r.useState)(""),{user:ea,isLoading:er}=(0,o.A)(),{cantiereId:ei,cantiere:en,isValidCantiere:el,isLoading:eo,error:ed}=(0,d.jV)();return(0,r.useEffect)(()=>{let e=async()=>{ee(!0);try{if(localStorage.getItem("token"),console.log("\uD83C\uDFD7️ ReportsPage: Caricamento report per cantiere:",ei),!ei||ei<=0){console.warn("\uD83C\uDFD7️ ReportsPage: Nessun cantiere valido selezionato"),es(ed||'Nessun cantiere selezionato. Seleziona un cantiere da "Gestisci Cantieri".'),ee(!1);return}let e=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4;return Promise.race([fetch(e,t),new Promise((t,a)=>setTimeout(()=>a(Error("Timeout dopo ".concat(s/1e3,"s per ").concat(e))),s))])},t=e("http://localhost:8001/api/reports/".concat(ei,"/progress?formato=video"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}).then(e=>e.json()).then(e=>e).catch(e=>({content:null,error:e.message.includes("Timeout")?"Timeout API Progress":"Errore API Progress"})),s=e("http://localhost:8001/api/reports/".concat(ei,"/boq?formato=video"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}).then(e=>e.json()).then(e=>e).catch(e=>({content:null,error:e.message.includes("Timeout")?"Timeout API BOQ":"Errore API BOQ"})),a=e("http://localhost:8001/api/reports/".concat(ei,"/storico-bobine?formato=video"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}).then(e=>e.json()).then(e=>e).catch(e=>({content:null,error:e.message.includes("Timeout")?"Timeout API Utilizzo Bobine":"Errore API Utilizzo Bobine"})),[r,i,n]=await Promise.all([t,s,a]);H(r),G(i),X(n);let l=[r,i,n].some(e=>{var t;return null==e||null==(t=e.error)?void 0:t.includes("Timeout")});(null==r?void 0:r.content)||(null==i?void 0:i.content)||(null==n?void 0:n.content)||r||i||n?l?es("Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto."):es(""):es("Errore nel caricamento dei report. Riprova pi\xf9 tardi."),ee(!1)}catch(e){es("Errore nel caricamento dei report. Riprova pi\xf9 tardi.")}finally{ee(!1)}};if(er||eo)return void console.log("\uD83C\uDFD7️ ReportsPage: Caricamento in corso, attendo...",{authLoading:er,cantiereLoading:eo});el&&ei&&ei>0?(console.log("\uD83C\uDFD7️ ReportsPage: Cantiere valido trovato, carico report"),e()):(console.warn("\uD83C\uDFD7️ ReportsPage: Nessun cantiere valido"),ee(!1),es(ed||'Nessun cantiere selezionato. Seleziona un cantiere da "Gestisci Cantieri".'))},[ei,el,er,eo,ed]),(0,a.jsx)(c.u,{children:(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:(0,a.jsx)("div",{className:"max-w-[90%] mx-auto py-6 space-y-6",children:Y||er?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-6 w-6 animate-spin"}),(0,a.jsx)("span",{children:"Caricamento report..."})]})}):et?(0,a.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,a.jsx)("span",{className:"text-amber-800 font-medium",children:et.includes("Nessun cantiere selezionato")||et.includes("Cantiere non selezionato")?"Cantiere non selezionato":et.includes("timeout")||et.includes("Timeout")?"Timeout API":"Errore caricamento report"})]}),(0,a.jsx)("p",{className:"text-amber-700 mb-4",children:et}),et.includes("timeout")||et.includes("Timeout")?(0,a.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded",children:(0,a.jsxs)("p",{className:"text-blue-800 text-sm",children:["\uD83D\uDCA1 ",(0,a.jsx)("strong",{children:"Suggerimento:"})," Le API stanno impiegando pi\xf9 tempo del previsto. Prova ad aggiornare la pagina o riprova tra qualche minuto."]})}):null,(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{onClick:()=>window.location.href="/cantieri",className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Gestisci Cantieri"}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{el&&ei?(console.log("\uD83C\uDFD7️ ReportsPage: Refresh report per cantiere:",ei),ee(!0),es(""),H(null),G(null),X(null),Q(null),loadAllReports()):console.warn("\uD83C\uDFD7️ ReportsPage: Impossibile fare refresh, nessun cantiere valido")},className:"border-amber-600 text-amber-700 hover:bg-amber-100",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Riprova"]})]})]}):(0,a.jsxs)(l.tU,{value:O,onValueChange:U,className:"w-full",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1 mb-6 mt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-1 h-auto bg-transparent p-0",children:[(0,a.jsxs)("button",{onClick:()=>U("avanzamento"),className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ".concat("avanzamento"===O?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"),"data-state":"avanzamento"===O?"active":"inactive",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Avanzamento"})]}),(0,a.jsxs)("button",{onClick:()=>U("boq"),className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ".concat("boq"===O?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"),"data-state":"boq"===O?"active":"inactive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"BOQ"})]}),(0,a.jsxs)("button",{onClick:()=>U("bobine"),className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ".concat("bobine"===O?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"),"data-state":"bobine"===O?"active":"inactive",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Bobine"})]}),(0,a.jsxs)("button",{onClick:()=>U("produttivita"),className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 border border-transparent ".concat("produttivita"===O?"bg-blue-50 text-blue-700 border-blue-200 shadow-sm":"hover:bg-slate-50"),"data-state":"produttivita"===O?"active":"inactive",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"font-medium",children:"Produttivit\xe0"})]})]})}),(0,a.jsx)(l.av,{value:"avanzamento",className:"space-y-6",children:(null==J?void 0:J.content)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(e=J.content.metri_totali)?void 0:e.toLocaleString())||0}),(0,a.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"metri"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Metri Totali"}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[J.content.totale_cavi||0," cavi totali"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-green-50 rounded-lg",children:(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(t=J.content.metri_posati)?void 0:t.toLocaleString())||0}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"metri"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:"Metri Posati"}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(J.content.percentuale_avanzamento||0,100),"%")}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[(null==(s=J.content.percentuale_avanzamento)?void 0:s.toFixed(1))||0,"% completato"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,a.jsx)(v.A,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(P=J.content.media_giornaliera)?void 0:P.toFixed(1))||0}),(0,a.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"m/giorno"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Media Giornaliera"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[J.content.giorni_lavorativi_effettivi||0," giorni attivi"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-50 rounded-lg",children:(0,a.jsx)(f.A,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:J.content.data_completamento?new Date(J.content.data_completamento).toLocaleDateString("it-IT"):"Da calcolare"}),(0,a.jsx)("div",{className:"text-xs text-orange-600 font-medium",children:"stima"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Completamento"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[J.content.giorni_stimati||0," giorni rimanenti"]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Posa Recente"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Ultimi 10 giorni di attivit\xe0"})]}),(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(_.u,{width:"100%",height:"100%",children:(0,a.jsxs)(A.b,{data:[...J.content.posa_recente||[]].reverse(),children:[(0,a.jsx)(z.d,{strokeDasharray:"3 3",stroke:"#f1f5f9"}),(0,a.jsx)(k.W,{dataKey:"data",tick:{fontSize:12,fill:"#64748b"},axisLine:{stroke:"#e2e8f0"}}),(0,a.jsx)(C.h,{tick:{fontSize:12,fill:"#64748b"},axisLine:{stroke:"#e2e8f0"}}),(0,a.jsx)(S.m,{contentStyle:{backgroundColor:"white",border:"1px solid #e2e8f0",borderRadius:"8px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",fontSize:"14px"},labelStyle:{color:"#374151",fontWeight:"500"}}),(0,a.jsx)(I.N,{type:"monotone",dataKey:"metri",stroke:"#3b82f6",strokeWidth:3,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2,fill:"white"},name:"Metri Posati"})]})})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Stato Cavi"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Distribuzione per stato di avanzamento"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Cavi Posati"})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-700",children:J.content.cavi_posati||0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-400 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Cavi Rimanenti"})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-700",children:J.content.cavi_rimanenti||0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Percentuale Completamento"})]}),(0,a.jsxs)("span",{className:"text-lg font-bold text-blue-700",children:[(null==(T=J.content.percentuale_cavi)?void 0:T.toFixed(1))||0,"%"]})]})]})]})]})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Nessun Dato Disponibile"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Nessun dato di avanzamento disponibile per questo cantiere"})]})}),(0,a.jsx)(l.av,{value:"boq",className:"space-y-6",children:(null==$?void 0:$.error)?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-12 text-center",children:[(0,a.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-amber-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"API BOQ Temporaneamente Non Disponibile"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Il servizio BOQ sta riscontrando problemi di performance."}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Stiamo lavorando per risolvere il problema."})]}):(null==$?void 0:$.content)?(0,a.jsxs)(a.Fragment,{children:[$.content.metri_orfani&&$.content.metri_orfani.metri_orfani_totali>0&&(0,a.jsxs)(i.Zp,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(i.aR,{className:"pb-3",children:(0,a.jsxs)(i.ZB,{className:"text-red-700 flex items-center",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDEA8 METRI POSATI SENZA TRACCIABILIT\xc0 BOBINA"]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("p",{className:"text-red-800 font-medium mb-2",children:[(0,a.jsxs)("strong",{children:[$.content.metri_orfani.metri_orfani_totali,"m"]})," installati con BOBINA_VUOTA (",$.content.metri_orfani.num_cavi_orfani," cavi)"]}),(0,a.jsx)("div",{className:"text-sm text-red-700 space-y-1",children:Array.isArray($.content.metri_orfani.dettaglio_per_categoria)?$.content.metri_orfani.dettaglio_per_categoria.map((e,t)=>(0,a.jsxs)("div",{children:["• ",(0,a.jsxs)("strong",{children:[e.tipologia," ",e.formazione]}),": ",e.metri_orfani,"m (",e.num_cavi," cavi)"]},t)):(0,a.jsx)("div",{children:"Dettaglio metri orfani non disponibile"})}),(0,a.jsx)("div",{className:"mt-3 p-3 bg-amber-50 border border-amber-200 rounded",children:(0,a.jsxs)("p",{className:"text-amber-800 text-sm",children:["⚠️ ",(0,a.jsx)("strong",{children:"NOTA:"})," I metri orfani NON sono inclusi nel calcolo acquisti. Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti."]})})]})]}),$.content.riepilogo&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-red-50 rounded-lg",children:(0,a.jsx)(w.A,{className:"h-5 w-5 text-red-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(R=$.content.riepilogo.totale_metri_mancanti)?void 0:R.toLocaleString())||0}),(0,a.jsx)("div",{className:"text-xs text-red-600 font-medium",children:"metri"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Metri da Acquistare"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"per completamento progetto"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-green-50 rounded-lg",children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(B=$.content.riepilogo.totale_metri_residui)?void 0:B.toLocaleString())||0}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"metri"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Metri Residui"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"disponibili in magazzino"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,a.jsx)(j.A,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(D=$.content.riepilogo.percentuale_completamento)?void 0:D.toFixed(1))||0}),(0,a.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"%"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Completamento"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"progetto completato"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-50 rounded-lg",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-orange-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:$.content.riepilogo.categorie_necessitano_acquisto||0}),(0,a.jsx)("div",{className:"text-xs text-orange-600 font-medium",children:"categorie"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Categorie"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"necessitano acquisto"})]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tipologia"}),(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Formazione"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cavi"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Teorici"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Posati"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri da Posare"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bobine"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Residui"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Mancanti"}),(0,a.jsx)("th",{className:"text-center p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acquisto"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==(L=$.content.distinta_materiali)?void 0:L.map((e,t)=>{var s,r,i,n,l;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150 ".concat(e.ha_bobina_vuota?"bg-red-50":""),children:[(0,a.jsx)("td",{className:"p-4 text-sm font-medium text-gray-900",children:e.tipologia}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700",children:e.formazione}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:e.num_cavi}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(s=e.metri_teorici_totali)?void 0:s.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(r=e.metri_reali_posati)?void 0:r.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(i=e.metri_da_posare)?void 0:i.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:e.num_bobine}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(n=e.metri_residui)?void 0:n.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm font-medium text-right",children:e.metri_mancanti>0?(0,a.jsxs)("span",{className:"text-red-600",children:[null==(l=e.metri_mancanti)?void 0:l.toLocaleString(),"m"]}):(0,a.jsx)("span",{className:"text-green-600",children:"0m"})}),(0,a.jsx)("td",{className:"p-4 text-center",children:e.necessita_acquisto?(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:"S\xec"}):(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"No"})})]},t)})})]})})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tipologia"}),(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Formazione"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Numero Bobine"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Disponibili"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==(E=$.content.bobine_per_tipo)?void 0:E.map((e,t)=>{var s;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,a.jsx)("td",{className:"p-4 text-sm font-medium text-gray-900",children:e.tipologia}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700",children:e.formazione}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:e.num_bobine}),(0,a.jsx)("td",{className:"p-4 text-sm font-medium text-right",children:(0,a.jsxs)("span",{className:e.metri_disponibili>0?"text-green-600":"text-red-600",children:[null==(s=e.metri_disponibili)?void 0:s.toLocaleString(),"m"]})})]},t)})})]})})})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Nessun Dato Disponibile"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Nessun dato BOQ disponibile per questo cantiere"})]})}),(0,a.jsx)(l.av,{value:"bobine",className:"space-y-6",children:(null==K?void 0:K.content)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,a.jsx)(p.A,{className:"h-5 w-5 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:K.content.totale_bobine||0}),(0,a.jsx)("div",{className:"text-xs text-blue-600 font-medium",children:"bobine"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Totale Bobine"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"bobine nel cantiere"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-green-50 rounded-lg",children:(0,a.jsx)(N.A,{className:"h-5 w-5 text-green-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(M=K.content.bobine)?void 0:M.filter(e=>"In uso"===e.stato||"Disponibile"===e.stato).length)||0}),(0,a.jsx)("div",{className:"text-xs text-green-600 font-medium",children:"attive"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Bobine Attive"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"disponibili/in uso"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 bg-purple-50 rounded-lg",children:(0,a.jsx)(v.A,{className:"h-5 w-5 text-purple-600"})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null==(F=K.content.bobine)?void 0:F.length)>0?(K.content.bobine.reduce((e,t)=>e+(t.percentuale_utilizzo||0),0)/K.content.bobine.length).toFixed(1):0}),(0,a.jsx)("div",{className:"text-xs text-purple-600 font-medium",children:"%"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-1",children:"Utilizzo Medio"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"utilizzo medio"})]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Codice"}),(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tipologia"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Totali"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Utilizzati"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metri Residui"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Utilizzo %"}),(0,a.jsx)("th",{className:"text-left p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stato"}),(0,a.jsx)("th",{className:"text-right p-4 text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Cavi"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==(q=K.content.bobine)?void 0:q.map((e,t)=>{var s,r,i,n;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-150",children:[(0,a.jsx)("td",{className:"p-4 text-sm font-medium text-gray-900",children:e.codice}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700",children:e.tipologia}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(s=e.metri_totali)?void 0:s.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(r=e.metri_utilizzati)?void 0:r.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:null==(i=e.metri_residui)?void 0:i.toLocaleString()}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium",children:[null==(n=e.percentuale_utilizzo)?void 0:n.toFixed(1),"%"]}),(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(e.percentuale_utilizzo||0,100),"%")}})})]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("Disponibile"===e.stato?"bg-green-100 text-green-800":"In uso"===e.stato?"bg-blue-100 text-blue-800":"Terminata"===e.stato?"bg-gray-100 text-gray-800":"Over"===e.stato?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.stato})}),(0,a.jsx)("td",{className:"p-4 text-sm text-gray-700 text-right",children:e.totale_cavi_associati||0})]},t)})})]})})})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Nessun Dato Disponibile"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Nessun dato bobine disponibile per questo cantiere"})]})}),(0,a.jsx)(l.av,{value:"produttivita",className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,a.jsx)("div",{className:"p-4 bg-blue-50 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center",children:(0,a.jsx)(b.A,{className:"h-10 w-10 text-blue-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Produttivit\xe0"}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:"Funzionalit\xe0 in fase di sviluppo"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Includer\xe0 calcoli IAP, statistiche team e analisi performance"})]})})]})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8902,3455,3464,9384,3845,8593,5731,283,8441,1684,7358],()=>t(11785)),_N_E=e.O()}]);