(()=>{var t={};t.id=22,t.ids=[22],t.modules={22:(t,e,r)=>{var n=r(75254),i=r(20623),o=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?o:"object"==typeof t?a(t)?i(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),i=r(658),o=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(i),m=l(o),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||i&&x(new i)!=s||o&&x(o.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),i=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,o=e.length;null!=t&&r<o;)t=t[i(e[r++])];return r&&r==o?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),i=r(59467);t.exports=function(t,e){return null!=t&&i(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),i=r(55048);t.exports=function(t){if(!i(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),i=r(34117),o=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},6335:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>dI});var n={};r.r(n),r.d(n,{scaleBand:()=>nB,scaleDiverging:()=>function t(){var e=oj(cz()(oo));return e.copy=function(){return cB(e,t())},nk.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=oN(cz()).domain([.1,1,10]);return e.copy=function(){return cB(e,t()).base(e.base())},nk.apply(e,arguments)},scaleDivergingPow:()=>cU,scaleDivergingSqrt:()=>c$,scaleDivergingSymlog:()=>function t(){var e=oD(cz());return e.copy=function(){return cB(e,t()).constant(e.constant())},nk.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,on),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,on):[0,1],oj(n)},scaleImplicit:()=>nI,scaleLinear:()=>oS,scaleLog:()=>function t(){let e=oN(os()).domain([1,10]);return e.copy=()=>ol(e,t()).base(e.base()),nM.apply(e,arguments),e},scaleOrdinal:()=>nD,scalePoint:()=>nR,scalePow:()=>oU,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=iO){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[iS(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(ib),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nM.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[iS(o,t,0,i)]:e}function u(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nM.apply(oj(c),arguments)},scaleRadial:()=>function t(){var e,r=of(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(oF(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,on)).map(oF)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},nM.apply(o,arguments),oj(o)},scaleSequential:()=>function t(){var e=oj(cD()(oo));return e.copy=function(){return cB(e,t())},nk.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=oN(cD()).domain([1,10]);return e.copy=function(){return cB(e,t()).base(e.base())},nk.apply(e,arguments)},scaleSequentialPow:()=>cR,scaleSequentialQuantile:()=>function t(){var e=[],r=oo;function n(t){if(null!=t&&!isNaN(t*=1))return r((iS(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(ib),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return oW(t);if(e>=1)return oq(t);var n,i=(n-1)*e,o=Math.floor(i),a=oq((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?oX:function(t=ib){if(t===ib)return oX;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(i,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,o)}let a=e[r],c=n,u=i;for(oG(e,n,r),o(e[i],a)>0&&oG(e,n,i);c<u;){for(oG(e,c,u),++c,--u;0>o(e[c],a);)++c;for(;o(e[u],a)>0;)--u}0===o(e[n],a)?oG(e,n,u):oG(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,o).subarray(0,o+1));return a+(oW(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nk.apply(n,arguments)},scaleSequentialSqrt:()=>cL,scaleSequentialSymlog:()=>function t(){var e=oD(cD());return e.copy=function(){return cB(e,t()).constant(e.constant())},nk.apply(e,arguments)},scaleSqrt:()=>o$,scaleSymlog:()=>function t(){var e=oD(os());return e.copy=function(){return ol(e,t()).constant(e.constant())},nM.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[iS(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},nM.apply(o,arguments)},scaleTime:()=>cC,scaleUtc:()=>cI,tickFormat:()=>oO});var i=r(60687),o=r(43210),a=r.n(o),c=r(44493),u=r(29523),l=r(96834),s=r(46657),f=r(56770),p=r(63213),h=r(62185),d=r(78122),y=r(41862),v=r(93613),m=r(28947),b=r(10022),g=r(19080),x=r(45583),w=r(5336),O=r(25541),j=r(58559),S=r(48730),P=r(40228),A=r(31158),E=r(43649),_=r(49384),M=r(45603),k=r.n(M),T=r(63866),N=r.n(T),C=r(77822),I=r.n(C),D=r(40491),B=r.n(D),R=r(93490),L=r.n(R),z=function(t){return 0===t?0:t>0?1:-1},U=function(t){return N()(t)&&t.indexOf("%")===t.length-1},$=function(t){return L()(t)&&!I()(t)},F=function(t){return $(t)||N()(t)},q=0,W=function(t){var e=++q;return"".concat(t||"").concat(e)},X=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!$(t)&&!N()(t))return n;if(U(t)){var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return I()(r)&&(r=n),i&&r>e&&(r=e),r},G=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},H=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},V=function(t,e){return $(t)&&$(e)?function(r){return t+r*(e-t)}:function(){return e}};function Y(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):B()(t,e))===r}):null}var Z=function(t,e){return $(t)&&$(e)?t-e:N()(t)&&N()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},K=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},Q=r(37456),J=r.n(Q),tt=r(5231),te=r.n(tt),tr=r(55048),tn=r.n(tr),ti=r(93780);function to(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function ta(t){return(ta="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tc=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],tu=["points","pathLength"],tl={svg:["viewBox","children"],polygon:tu,polyline:tu},ts=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],tf=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,o.isValidElement)(t)&&(r=t.props),!tn()(r))return null;var n={};return Object.keys(r).forEach(function(t){ts.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},tp=function(t,e,r){if(!tn()(t)||"object"!==ta(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];ts.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n},th=["children"],td=["children"];function ty(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var tv={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tm=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tb=null,tg=null,tx=function t(e){if(e===tb&&Array.isArray(tg))return tg;var r=[];return o.Children.forEach(e,function(e){J()(e)||((0,ti.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tg=r,tb=e,r};function tw(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tm(t)}):[tm(e)],tx(t).forEach(function(t){var e=B()(t,"type.displayName")||B()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tO(t,e){var r=tw(t,e);return r&&r[0]}var tj=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!$(r)&&!(r<=0)&&!!$(n)&&!(n<=0)},tS=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tP=function(t,e,r,n){var i,o=null!=(i=null==tl?void 0:tl[n])?i:[];return e.startsWith("data-")||!te()(t)&&(n&&o.includes(e)||tc.includes(e))||r&&ts.includes(e)},tA=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,o.isValidElement)(t)&&(n=t.props),!tn()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;tP(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},tE=function t(e,r){if(e===r)return!0;var n=o.Children.count(e);if(n!==o.Children.count(r))return!1;if(0===n)return!0;if(1===n)return t_(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],c=r[i];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!t_(a,c))return!1}return!0},t_=function(t,e){if(J()(t)&&J()(e))return!0;if(!J()(t)&&!J()(e)){var r=t.props||{},n=r.children,i=ty(r,th),o=e.props||{},a=o.children,c=ty(o,td);if(n&&a)return to(i,c)&&tE(n,a);if(!n&&!a)return to(i,c)}return!1},tM=function(t,e){var r=[],n={};return tx(t).forEach(function(t,i){var o;if((o=t)&&o.type&&N()(o.type)&&tS.indexOf(o.type)>=0)r.push(t);else if(t){var a=tm(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,i);r.push(s),n[a]=!0}}}),r},tk=function(t){var e=t&&t.type;return e&&tv[e]?tv[e]:null};function tT(t){return(tT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tN(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tT(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tI(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tD=(0,o.forwardRef)(function(t,e){var r,n=t.aspect,i=t.initialDimension,c=void 0===i?{width:-1,height:-1}:i,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,O=t.style,j=(0,o.useRef)(null),S=(0,o.useRef)();S.current=w,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var P=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tI(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tI(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),A=P[0],E=P[1],M=(0,o.useCallback)(function(t,e){E(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;M(n,i),null==(e=S.current)||e.call(S,n,i)};b>0&&(t=k()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect();return M(r.width,r.height),e.observe(j.current),function(){e.disconnect()}},[M,b]);var T=(0,o.useMemo)(function(){var t=A.containerWidth,e=A.containerHeight;if(t<0||e<0)return null;K(U(l)||U(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),K(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=U(l)?t:l,i=U(f)?e:f;n&&n>0&&(r?i=r/n:i&&(r=i*n),y&&i>y&&(i=y)),K(r>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,i,l,f,h,d,n);var c=!Array.isArray(v)&&tm(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,o.cloneElement)(t,tC({width:r,height:i},c?{style:tC({height:"100%",width:"100%",maxHeight:i,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,d,h,A,l]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,_.A)("recharts-responsive-container",x),style:tC(tC({},void 0===O?{}:O),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:y}),ref:j},T)}),tB=r(34990),tR=r.n(tB),tL=r(85938),tz=r.n(tL);function tU(t,e){if(!t)throw Error("Invariant failed")}var t$=["children","width","height","viewBox","className","style","title","desc"];function tF(){return(tF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tq(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,o=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,t$),f=i||{width:r,height:n,x:0,y:0},p=(0,_.A)("recharts-surface",o);return a().createElement("svg",tF({},tA(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var tW=["children","className"];function tX(){return(tX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tG=a().forwardRef(function(t,e){var r=t.children,n=t.className,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tW),o=(0,_.A)("recharts-layer",n);return a().createElement("g",tX({className:o},tA(i,!0),{ref:e}),r)});function tH(t){return(tH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tV(){return(tV=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tZ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tH(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tQ(t){return Array.isArray(t)&&F(t[0])&&F(t[1])?t.join(" ~ "):t}var tJ=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,i=t.itemStyle,o=void 0===i?{}:i,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,h=t.label,d=t.labelFormatter,y=t.accessibilityLayer,v=tK({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tK({margin:0},void 0===c?{}:c),b=!J()(h),g=b?h:"",x=(0,_.A)("recharts-default-tooltip",f),w=(0,_.A)("recharts-tooltip-label",p);return b&&d&&null!=u&&(g=d(h,u)),a().createElement("div",tV({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(s?tz()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=tK({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},o),i=t.formatter||l||tQ,c=t.value,s=t.name,f=c,p=s;if(i&&null!=f&&null!=p){var h=i(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tY(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tY(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},F(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,F(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function t0(t){return(t0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t1(t,e,r){var n;return(n=function(t,e){if("object"!=t0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==t0(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var t2="recharts-tooltip-wrapper",t5={visibility:"hidden"};function t4(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(o&&$(o[n]))return o[n];var s=r[n]-c-i,f=r[n]+i;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function t3(t){return(t3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t6(Object(r),!0).forEach(function(e){ee(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t7=function(){return!!t})()}function t9(t){return(t9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function et(t,e){return(et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ee(t,e,r){return(e=er(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function er(t){var e=function(t,e){if("object"!=t3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t3(e)?e:e+""}var en=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=t9(e),ee(t=function(t,e){if(e&&("object"===t3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t7()?Reflect.construct(e,n||[],t9(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ee(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&et(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,i,o,c,u,l,s,f,p,h,d,y,v,m,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,P=w.animationEasing,A=w.children,E=w.coordinate,M=w.hasPayload,k=w.isAnimationActive,T=w.offset,N=w.position,C=w.reverseDirection,I=w.useTranslate3d,D=w.viewBox,B=w.wrapperStyle,R=(p=(t={allowEscapeViewBox:j,coordinate:E,offsetTopLeft:T,position:N,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:D}).allowEscapeViewBox,h=t.coordinate,d=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&h?(r=(e={translateX:s=t4({allowEscapeViewBox:p,coordinate:h,key:"x",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=t4({allowEscapeViewBox:p,coordinate:h,key:"y",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=t5,{cssProperties:l,cssClasses:(o=(i={translateX:s,translateY:f,coordinate:h}).coordinate,c=i.translateX,u=i.translateY,(0,_.A)(t2,t1(t1(t1(t1({},"".concat(t2,"-right"),$(c)&&o&&$(o.x)&&c>=o.x),"".concat(t2,"-left"),$(c)&&o&&$(o.x)&&c<o.x),"".concat(t2,"-bottom"),$(u)&&o&&$(o.y)&&u>=o.y),"".concat(t2,"-top"),$(u)&&o&&$(o.y)&&u<o.y)))}),L=R.cssClasses,z=R.cssProperties,U=t8(t8({transition:k&&O?"transform ".concat(S,"ms ").concat(P):void 0},z),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&M?"visible":"hidden",position:"absolute",top:0,left:0},B);return a().createElement("div",{tabIndex:-1,className:L,style:U,ref:function(t){x.wrapperNode=t}},A)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,er(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent),ei={isSsr:!0,get:function(t){return ei[t]},set:function(t,e){if("string"==typeof t)ei[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){ei[e]=t[e]})}}},eo=r(36315),ea=r.n(eo);function ec(t,e,r){return!0===e?ea()(t,r):te()(e)?ea()(t,e):t}function eu(t){return(eu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function el(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function es(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?el(Object(r),!0).forEach(function(e){ed(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):el(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ef(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ef=function(){return!!t})()}function ep(t){return(ep=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eh(t,e){return(eh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ed(t,e,r){return(e=ey(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ey(t){var e=function(t,e){if("object"!=eu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eu(e)?e:e+""}function ev(t){return t.dataKey}var em=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ep(t),function(t,e){if(e&&("object"===eu(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ef()?Reflect.construct(t,e||[],ep(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&eh(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,i=r.allowEscapeViewBox,o=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=ec(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,ev));var w=x.length>0;return a().createElement(en,{allowEscapeViewBox:i,animationDuration:o,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=es(es({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tJ,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ey(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);ed(em,"displayName","Tooltip"),ed(em,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ei.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var eb=r(69433),eg=r.n(eb);let ex=Math.cos,ew=Math.sin,eO=Math.sqrt,ej=Math.PI,eS=2*ej,eP={draw(t,e){let r=eO(e/ej);t.moveTo(r,0),t.arc(0,0,r,0,eS)}},eA=eO(1/3),eE=2*eA,e_=ew(ej/10)/ew(7*ej/10),eM=ew(eS/10)*e_,ek=-ex(eS/10)*e_,eT=eO(3),eN=eO(3)/2,eC=1/eO(12),eI=(eC/2+1)*3;function eD(t){return function(){return t}}let eB=Math.PI,eR=2*eB,eL=eR-1e-6;function ez(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eU{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?ez:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return ez;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,n,i){if(t*=1,e*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,c=r-t,u=n-e,l=o-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&i){let p=r-o,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=i*Math.tan((eB-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${i},${i},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,i,o){if(t*=1,e*=1,r*=1,o=!!o,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^o,f=o?n-i:i-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eR+eR),f>eL?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eB)},${s},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function e$(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eU(e)}function eF(t){return(eF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eU.prototype,eO(3),eO(3);var eq=["type","size","sizeType"];function eW(){return(eW=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eX(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=eF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eF(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eH={symbolCircle:eP,symbolCross:{draw(t,e){let r=eO(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=eO(e/eE),n=r*eA;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=eO(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=eO(.8908130915292852*e),n=eM*r,i=ek*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=eS*e/5,a=ex(o),c=ew(o);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*i,c*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-eO(e/(3*eT));t.moveTo(0,2*r),t.lineTo(-eT*r,-r),t.lineTo(eT*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=eO(e/eI),n=r/2,i=r*eC,o=r*eC+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-eN*i,eN*n+-.5*i),t.lineTo(-.5*n-eN*o,eN*n+-.5*o),t.lineTo(-.5*a-eN*o,eN*a+-.5*o),t.lineTo(-.5*n+eN*i,-.5*i-eN*n),t.lineTo(-.5*n+eN*o,-.5*o-eN*n),t.lineTo(-.5*a+eN*o,-.5*o-eN*a),t.closePath()}}},eV=Math.PI/180,eY=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eV;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eZ=function(t){var e,r=t.type,n=void 0===r?"circle":r,i=t.size,o=void 0===i?64:i,c=t.sizeType,u=void 0===c?"area":c,l=eG(eG({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,eq)),{},{type:n,size:o,sizeType:u}),s=l.className,f=l.cx,p=l.cy,h=tA(l,!0);return f===+f&&p===+p&&o===+o?a().createElement("path",eW({},h,{className:(0,_.A)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eH["symbol".concat(eg()(n))]||eP,(function(t,e){let r=null,n=e$(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:eD(t||eP),e="function"==typeof e?e:eD(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:eD(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(eY(o,u,n))())})):null};function eK(t){return(eK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eQ(){return(eQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eZ.registerSymbol=function(t,e){eH["symbol".concat(eg()(t))]=e};function e0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e0=function(){return!!t})()}function e1(t){return(e1=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e2(t,e){return(e2=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e5(t,e,r){return(e=e4(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e4(t){var e=function(t,e){if("object"!=eK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eK(e)?e:e+""}var e3=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=e1(t),function(t,e){if(e&&("object"===eK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,e0()?Reflect.construct(t,e||[],e1(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&e2(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var o=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eJ(Object(r),!0).forEach(function(e){e5(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete o.legendIcon,a().cloneElement(t.legendIcon,o)}return a().createElement(eZ,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,i=e.layout,o=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===i?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||o,f=(0,_.A)(e5(e5({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=te()(e.value)?null:e.value;K(!te()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return a().createElement("li",eQ({className:f,style:l,key:"legend-item-".concat(r)},tp(t.props,e,r)),a().createElement(tq,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:h}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e4(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);function e6(t){return(e6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e5(e3,"displayName","Legend"),e5(e3,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var e8=["ref"];function e7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e7(Object(r),!0).forEach(function(e){ri(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ro(n.key),n)}}function re(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(re=function(){return!!t})()}function rr(t){return(rr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rn(t,e){return(rn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ri(t,e,r){return(e=ro(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ro(t){var e=function(t,e){if("object"!=e6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e6(e)?e:e+""}function ra(t){return t.value}var rc=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=rr(e),ri(t=function(t,e){if(e&&("object"===e6(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,re()?Reflect.construct(e,r||[],rr(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&rn(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?e9({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),e9(e9({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,i=e.height,o=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=e9(e9({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(o)),o);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,e8);return a().createElement(e3,r)}(r,e9(e9({},this.props),{},{payload:ec(u,c,ra)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=e9(e9({},this.defaultProps),t.props).layout;return"vertical"===r&&$(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&rt(n.prototype,e),r&&rt(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function ru(){return(ru=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}ri(rc,"displayName","Legend"),ri(rc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var rl=function(t){var e=t.cx,r=t.cy,n=t.r,i=t.className,o=(0,_.A)("recharts-dot",i);return e===+e&&r===+r&&n===+n?a().createElement("circle",ru({},tA(t,!1),tf(t),{className:o,cx:e,cy:r,r:n})):null},rs=r(87955),rf=r.n(rs),rp=Object.getOwnPropertyNames,rh=Object.getOwnPropertySymbols,rd=Object.prototype.hasOwnProperty;function ry(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function rv(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function rm(t){return rp(t).concat(rh(t))}var rb=Object.hasOwn||function(t,e){return rd.call(t,e)};function rg(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rx=Object.getOwnPropertyDescriptor,rw=Object.keys;function rO(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rj(t,e){return rg(t.getTime(),e.getTime())}function rS(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rP(t,e){return t===e}function rA(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rE(t,e,r){var n=rw(t),i=n.length;if(rw(e).length!==i)return!1;for(;i-- >0;)if(!rI(t,e,r,n[i]))return!1;return!0}function r_(t,e,r){var n,i,o,a=rm(t),c=a.length;if(rm(e).length!==c)return!1;for(;c-- >0;)if(!rI(t,e,r,n=a[c])||(i=rx(t,n),o=rx(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function rM(t,e){return rg(t.valueOf(),e.valueOf())}function rk(t,e){return t.source===e.source&&t.flags===e.flags}function rT(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rN(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rC(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rI(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rb(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rD=Array.isArray,rB="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rR=Object.assign,rL=Object.prototype.toString.call.bind(Object.prototype.toString),rz=rU();function rU(t){void 0===t&&(t={});var e,r,n,i,o,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?r_:rO,areDatesEqual:rj,areErrorsEqual:rS,areFunctionsEqual:rP,areMapsEqual:n?ry(rA,r_):rA,areNumbersEqual:rg,areObjectsEqual:n?r_:rE,arePrimitiveWrappersEqual:rM,areRegExpsEqual:rk,areSetsEqual:n?ry(rT,r_):rT,areTypedArraysEqual:n?r_:rN,areUrlsEqual:rC};if(r&&(i=rR({},i,r(i))),e){var o=rv(i.areArraysEqual),a=rv(i.areMapsEqual),c=rv(i.areObjectsEqual),u=rv(i.areSetsEqual);i=rR({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&o(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rD(t))return r(t,e,d);if(null!=rB&&rB(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rL(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?i(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,i,o,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function r$(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function rF(t){return(rF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rq(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rW(t){return(rW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rX(Object(r),!0).forEach(function(e){rH(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rH(t,e,r){var n;return(n=function(t,e){if("object"!==rW(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rW(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rU({strict:!0}),rU({circular:!0}),rU({circular:!0,strict:!0}),rU({createInternalComparator:function(){return rg}}),rU({strict:!0,createInternalComparator:function(){return rg}}),rU({circular:!0,createInternalComparator:function(){return rg}}),rU({circular:!0,createInternalComparator:function(){return rg},strict:!0});var rV=function(t){return t},rY=function(t,e){return Object.keys(e).reduce(function(r,n){return rG(rG({},r),{},rH({},n,t(n,e[n])))},{})},rZ=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rK=function(t,e,r,n,i,o,a,c){};function rQ(t,e){if(t){if("string"==typeof t)return rJ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rJ(t,e)}}function rJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r0=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},r1=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},r2=function(t,e){return function(r){return r1(r0(t,e),r)}},r5=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,u=1;break;case"ease":o=.25,a=.1,c=.25,u=1;break;case"ease-in":o=.42,a=0,c=1,u=1;break;case"ease-out":o=.42,a=0,c=.58,u=1;break;case"ease-in-out":o=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(s,4)||rQ(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],u=f[3]}else rK(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rK([o,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r2(o,c),h=r2(a,u),d=(t=o,e=c,function(r){var n;return r1([].concat(function(t){if(Array.isArray(t))return rJ(t)}(n=r0(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rQ(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=d(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return h(r)};return y.isStepper=!1,y},r4=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},r3=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r5(n);case"spring":return r4();default:if("cubic-bezier"===n.split("(")[0])return r5(n);rK(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rK(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function r6(t){return(r6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r8(t){return function(t){if(Array.isArray(t))return nr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ne(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r7(Object(r),!0).forEach(function(e){nt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nt(t,e,r){var n;return(n=function(t,e){if("object"!==r6(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r6(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ne(t,e){if(t){if("string"==typeof t)return nr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nr(t,e)}}function nr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nn=function(t,e,r){return t+(e-t)*r},ni=function(t){return t.from!==t.to},no=function t(e,r,n){var i=rY(function(t,r){if(ni(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(n,2)||ne(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return r9(r9({},r),{},{from:o,velocity:a})}return r},r);return n<1?rY(function(t,e){return ni(e)?r9(r9({},e),{},{velocity:nn(e.velocity,i[t].velocity,n),from:nn(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let na=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return r9(r9({},r),{},nt({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return r9(r9({},r),{},nt({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;l=no(r,l,a),i(r9(r9(r9({},t),e),rY(function(t,e){return e.from},l))),o=n,Object.values(l).filter(ni).length&&(s=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,l=rY(function(t,e){return nn.apply(void 0,r8(e).concat([r(c)]))},u);if(i(r9(r9(r9({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rY(function(t,e){return nn.apply(void 0,r8(e).concat([r(1)]))},u);i(r9(r9(r9({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function nc(t){return(nc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var nu=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function nl(t){return function(t){if(Array.isArray(t))return ns(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ns(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ns(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ns(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function np(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nf(Object(r),!0).forEach(function(e){nh(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nf(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nh(t,e,r){return(e=nd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nd(t){var e=function(t,e){if("object"!==nc(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==nc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nc(e)?e:String(e)}function ny(t,e){return(ny=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nv(t,e){if(e&&("object"===nc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nm(t)}function nm(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nb(t){return(nb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var ng=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&ny(i,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nb(i);return t=e?Reflect.construct(r,arguments,nb(this).constructor):r.apply(this,arguments),nv(this,t)});function i(t,e){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),o=r.props,a=o.isActive,c=o.attributeName,u=o.from,l=o.to,s=o.steps,f=o.children,p=o.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nm(r)),r.changeStyle=r.changeStyle.bind(nm(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nv(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nv(r);r.state={style:c?nh({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:i?nh({},i,a):a};this.state&&u&&(i&&u[i]!==a||!i&&u!==a)&&this.setState(l);return}if(!rz(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||o?c:t.to;if(this.state&&u){var p={style:i?nh({},i,f):f};(i&&u[i]!==f||!i&&u!==f)&&this.setState(p)}this.runAnimation(np(np({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=na(r,n,r3(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(nl(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=i>0?r[i-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(nl(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var h=rZ(p,o,c),d=np(np(np({},f.style),u),{},{transition:h});return[].concat(nl(t),[d,o,s]).filter(rV)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rq(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rq(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void r$(t.bind(null,a),o):(t(o),void r$(t.bind(null,a)))}"object"===rF(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?nh({},c,u):u,v=rZ(Object.keys(y),a,l);d.start([s,o,np(np({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,nu)),c=o.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,o.cloneElement)(t,np(np({},i),{},{style:np(np({},void 0===r?{}:r),u),className:n}))};return 1===c?l(o.Children.only(e)):a().createElement("div",null,o.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nd(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(o.PureComponent);function nx(t){return(nx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nw(){return(nw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nj(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nx(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}ng.displayName="Animate",ng.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ng.propTypes={from:rf().oneOfType([rf().object,rf().string]),to:rf().oneOfType([rf().object,rf().string]),attributeName:rf().string,duration:rf().number,begin:rf().number,easing:rf().oneOfType([rf().string,rf().func]),steps:rf().arrayOf(rf().shape({duration:rf().number.isRequired,style:rf().object.isRequired,easing:rf().oneOfType([rf().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rf().func]),properties:rf().arrayOf("string"),onAnimationEnd:rf().func})),children:rf().oneOfType([rf().node,rf().func]),isActive:rf().bool,canBegin:rf().bool,onAnimationEnd:rf().func,shouldReAnimate:rf().bool,onAnimationStart:rf().func,onAnimationReStart:rf().func};var nP=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),o+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),o+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),o+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},nA=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nE={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},n_=function(t){var e,r=nS(nS({},nE),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nO(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nO(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,_.A)("recharts-rectangle",d);return g?a().createElement(ng,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,i=t.height,o=t.x,u=t.y;return a().createElement(ng,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",nw({},tA(r,!0),{className:x,d:nP(o,u,e,i,h),ref:n})))}):a().createElement("path",nw({},tA(r,!0),{className:x,d:nP(l,s,f,p,h)}))};function nM(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nk(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nT extends Map{constructor(t,e=nC){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nN(this,t))}has(t){return super.has(nN(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nN({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nC(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nI=Symbol("implicit");function nD(){var t=new nT,e=[],r=[],n=nI;function i(i){let o=t.get(i);if(void 0===o){if(n!==nI)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nT,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return nD(e,r).unknown(n)},nM.apply(i,arguments),i}function nB(){var t,e,r=nD().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<o,p=f?a:o,h=f?o:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return p+t*e});return i(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nB(n(),[o,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nM.apply(f(),arguments)}function nR(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nB.apply(null,arguments).paddingInner(1))}function nL(t){return(nL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nz(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nL(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function n$(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nF={widthCache:{},cacheCount:0},nq={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nW="recharts_measurement_span",nX=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||ei.isSsr)return{width:0,height:0};var n=(Object.keys(e=nU({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:n});if(nF.widthCache[i])return nF.widthCache[i];try{var o=document.getElementById(nW);o||((o=document.createElement("span")).setAttribute("id",nW),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=nU(nU({},nq),n);Object.assign(o.style,a),o.textContent="".concat(t);var c=o.getBoundingClientRect(),u={width:c.width,height:c.height};return nF.widthCache[i]=u,++nF.cacheCount>2e3&&(nF.cacheCount=0,nF.widthCache={}),u}catch(t){return{width:0,height:0}}};function nG(t){return(nG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nH(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nV(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nV(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nV(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nY(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nG(e)?e:e+""}(n.key),n)}}var nZ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nK=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nQ=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nJ=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},n1=Object.keys(n0),n2=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nQ.test(e)||(this.num=NaN,this.unit=""),n1.includes(e)&&(this.num=t*n0[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nH(null!=(e=nJ.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&nY(r.prototype,t),e&&nY(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function n5(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nH(null!=(r=nZ.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],c=n2.parse(null!=i?i:""),u=n2.parse(null!=a?a:""),l="*"===o?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nZ,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nH(null!=(s=nK.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=n2.parse(null!=p?p:""),v=n2.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nK,m.toString())}return e}var n4=/\(([^()]*)\)/;function n3(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nH(n4.exec(e),2)[1];e=e.replace(n4,n5(r))}return e}(e),e=n5(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var n6=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],n8=["dx","dy","angle","className","breakAll"];function n7(){return(n7=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function n9(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function it(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ie(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ie(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ir=/[ \f\n\r\t\v\u2028\u2029]+/,ii=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];J()(e)||(i=r?e.toString().split(""):e.toString().split(ir));var o=i.map(function(t){return{word:t,width:nX(t,n).width}}),a=r?0:nX("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:a}}catch(t){return null}},io=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=$(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(ii({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=it(h(m-1),2),g=b[0],x=b[1],w=it(h(m),1)[0];if(g||w||(d=m+1),g&&w&&(y=m-1),!g&&w){o=x;break}v++}return o||p},ia=function(t){return[{words:J()(t)?[]:t.toString().split(ir)}]},ic=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!ei.isSsr){var c=ii({breakAll:o,children:n,style:i});if(!c)return ia(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return io({breakAll:o,children:n,maxLines:a,style:i},u,l,e,r)}return ia(n)},iu="#808080",il=function(t){var e,r=t.x,n=void 0===r?0:r,i=t.y,c=void 0===i?0:i,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,d=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?iu:v,b=n9(t,n6),g=(0,o.useMemo)(function(){return ic({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:h,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,h,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,P=n9(b,n8);if(!F(n)||!F(c))return null;var A=n+($(x)?x:0),E=c+($(w)?w:0);switch(void 0===y?"end":y){case"start":e=n3("calc(".concat(f,")"));break;case"middle":e=n3("calc(".concat((g.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=n3("calc(".concat(g.length-1," * -").concat(l,")"))}var M=[];if(h){var k=g[0].width,T=b.width;M.push("scale(".concat(($(T)?T/k:1)/k,")"))}return O&&M.push("rotate(".concat(O,", ").concat(A,", ").concat(E,")")),M.length&&(P.transform=M.join(" ")),a().createElement("text",n7({},tA(P,!0),{x:A,y:E,className:(0,_.A)("recharts-text",j),textAnchor:void 0===d?"start":d,fill:m.includes("url")?iu:m}),g.map(function(t,r){var n=t.words.join(S?"":" ");return a().createElement("tspan",{x:A,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let is=Math.sqrt(50),ip=Math.sqrt(10),ih=Math.sqrt(2);function id(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=is?10:u>=ip?5:u>=ih?2:1;return(c<0?(n=Math.round(t*(o=Math.pow(10,-c)/l)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,c)*l)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?id(t,e,2*r):[n,i,o]}function iy(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?id(e,t,r):id(t,e,r);if(!(o>=i))return[];let c=o-i+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((o-t)/a);else for(let t=0;t<c;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((i+t)/a);else for(let t=0;t<c;++t)u[t]=(i+t)*a;return u}function iv(t,e,r){return id(t*=1,e*=1,r*=1)[2]}function im(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?iv(e,t,r):iv(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function ib(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ig(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ix(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=ib,r=(e,r)=>ib(t(e),r),n=(e,r)=>t(e)-r):(e=t===ib||t===ig?t:iw,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function iw(){return 0}function iO(t){return null===t?NaN:+t}let ij=ix(ib),iS=ij.right;function iP(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function iA(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function iE(){}ij.left,ix(iO).center;var i_="\\s*([+-]?\\d+)\\s*",iM="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ik="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iT=/^#([0-9a-f]{3,8})$/,iN=RegExp(`^rgb\\(${i_},${i_},${i_}\\)$`),iC=RegExp(`^rgb\\(${ik},${ik},${ik}\\)$`),iI=RegExp(`^rgba\\(${i_},${i_},${i_},${iM}\\)$`),iD=RegExp(`^rgba\\(${ik},${ik},${ik},${iM}\\)$`),iB=RegExp(`^hsl\\(${iM},${ik},${ik}\\)$`),iR=RegExp(`^hsla\\(${iM},${ik},${ik},${iM}\\)$`),iL={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iz(){return this.rgb().formatHex()}function iU(){return this.rgb().formatRgb()}function i$(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=iT.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?iF(e):3===r?new iX(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?iq(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?iq(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=iN.exec(t))?new iX(e[1],e[2],e[3],1):(e=iC.exec(t))?new iX(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=iI.exec(t))?iq(e[1],e[2],e[3],e[4]):(e=iD.exec(t))?iq(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=iB.exec(t))?iK(e[1],e[2]/100,e[3]/100,1):(e=iR.exec(t))?iK(e[1],e[2]/100,e[3]/100,e[4]):iL.hasOwnProperty(t)?iF(iL[t]):"transparent"===t?new iX(NaN,NaN,NaN,0):null}function iF(t){return new iX(t>>16&255,t>>8&255,255&t,1)}function iq(t,e,r,n){return n<=0&&(t=e=r=NaN),new iX(t,e,r,n)}function iW(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof iE||(i=i$(i)),i)?new iX((i=i.rgb()).r,i.g,i.b,i.opacity):new iX:new iX(t,e,r,null==n?1:n)}function iX(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function iG(){return`#${iZ(this.r)}${iZ(this.g)}${iZ(this.b)}`}function iH(){let t=iV(this.opacity);return`${1===t?"rgb(":"rgba("}${iY(this.r)}, ${iY(this.g)}, ${iY(this.b)}${1===t?")":`, ${t})`}`}function iV(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function iY(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function iZ(t){return((t=iY(t))<16?"0":"")+t.toString(16)}function iK(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new iJ(t,e,r,n)}function iQ(t){if(t instanceof iJ)return new iJ(t.h,t.s,t.l,t.opacity);if(t instanceof iE||(t=i$(t)),!t)return new iJ;if(t instanceof iJ)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new iJ(a,c,u,t.opacity)}function iJ(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function i0(t){return(t=(t||0)%360)<0?t+360:t}function i1(t){return Math.max(0,Math.min(1,t||0))}function i2(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function i5(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}iP(iE,i$,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:iz,formatHex:iz,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iQ(this).formatHsl()},formatRgb:iU,toString:iU}),iP(iX,iW,iA(iE,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iX(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iX(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new iX(iY(this.r),iY(this.g),iY(this.b),iV(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iG,formatHex:iG,formatHex8:function(){return`#${iZ(this.r)}${iZ(this.g)}${iZ(this.b)}${iZ((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iH,toString:iH})),iP(iJ,function(t,e,r,n){return 1==arguments.length?iQ(t):new iJ(t,e,r,null==n?1:n)},iA(iE,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iJ(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iJ(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new iX(i2(t>=240?t-240:t+120,i,n),i2(t,i,n),i2(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new iJ(i0(this.h),i1(this.s),i1(this.l),iV(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=iV(this.opacity);return`${1===t?"hsl(":"hsla("}${i0(this.h)}, ${100*i1(this.s)}%, ${100*i1(this.l)}%${1===t?")":`, ${t})`}`}}));let i4=t=>()=>t;function i3(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):i4(isNaN(t)?e:t)}let i6=function t(e){var r,n=1==(r=+e)?i3:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):i4(isNaN(t)?e:t)};function i(t,e){var r=n((t=iW(t)).r,(e=iW(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=i3(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function i8(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=iW(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}i8(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return i5((r-n/e)*e,a,i,o,c)}}),i8(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return i5((r-n/e)*e,i,o,a,c)}});function i7(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var i9=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ot=RegExp(i9.source,"g");function oe(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?i4(e):("number"===i?i7:"string"===i?(n=i$(e))?(e=n,i6):function(t,e){var r,n,i,o,a,c=i9.lastIndex=ot.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(i=i9.exec(t))&&(o=ot.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(i=i[0])===(o=o[0])?l[u]?l[u]+=o:l[++u]=o:(l[++u]=null,s.push({i:u,x:i7(i,o)})),c=ot.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof i$?i6:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=oe(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=oe(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:i7:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function or(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function on(t){return+t}var oi=[0,1];function oo(t){return t}function oa(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function oc(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=oa(i,n),o=r(a,o)):(n=oa(n,i),o=r(o,a)),function(t){return o(n(t))}}function ou(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=oa(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=iS(t,e,1,n)-1;return o[r](i[r](e))}}function ol(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function os(){var t,e,r,n,i,o,a=oi,c=oi,u=oe,l=oo;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==oo&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?ou:oc,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(c,a.map(t),i7)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,on),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=or,s()},f.clamp=function(t){return arguments.length?(l=!!t||oo,s()):l!==oo},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function of(){return os()(oo,oo)}var op=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function oh(t){var e;if(!(e=op.exec(t)))throw Error("invalid format: "+t);return new od({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function od(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function oy(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function ov(t){return(t=oy(Math.abs(t)))?t[1]:NaN}function om(t,e){var r=oy(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}oh.prototype=od.prototype,od.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ob={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>om(100*t,e),r:om,s:function(t,e){var r=oy(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(cH=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+oy(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function og(t){return t}var ox=Array.prototype.map,ow=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function oO(t,e,r,n){var i,o,a,c=im(t,e,r);switch((n=oh(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ov(u)/3)))-ov(Math.abs(c))))||(n.precision=a),cZ(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,ov(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-ov(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-ov(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cY(n)}function oj(t){var e=t.domain;return t.ticks=function(t){var r=e();return iy(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return oO(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],l=o[c],s=10;for(l<u&&(i=u,u=l,l=i,i=a,a=c,c=i);s-- >0;){if((i=iv(u,l,r))===n)return o[a]=u,o[c]=l,e(o);if(i>0)u=Math.floor(u/i)*i,l=Math.ceil(l/i)*i;else if(i<0)u=Math.ceil(u*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function oS(){var t=of();return t.copy=function(){return ol(t,oS())},nM.apply(t,arguments),oj(t)}function oP(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function oA(t){return Math.log(t)}function oE(t){return Math.exp(t)}function o_(t){return-Math.log(-t)}function oM(t){return-Math.exp(-t)}function ok(t){return isFinite(t)?+("1e"+t):t<0?0:t}function oT(t){return(e,r)=>-t(-e,r)}function oN(t){let e,r,n=t(oA,oE),i=n.domain,o=10;function a(){var a,c;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=o)?ok:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=oT(e),r=oT(r),t(o_,oM)):t(oA,oE),n}return n.base=function(t){return arguments.length?(o=+t,a()):o},n.domain=function(t){return arguments.length?(i(t),a()):i()},n.ticks=t=>{let n,a,c=i(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(o%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=iy(u,l,h))}else d=iy(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=oh(i)).precision||(i.trim=!0),i=cY(i)),t===1/0)return i;let a=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=a?i(t):""}},n.nice=()=>i(oP(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function oC(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function oI(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function oD(t){var e=1,r=t(oC(1),oI(e));return r.constant=function(r){return arguments.length?t(oC(e=+r),oI(e)):e},oj(r)}function oB(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function oR(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function oL(t){return t<0?-t*t:t*t}function oz(t){var e=t(oo,oo),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(oo,oo):.5===r?t(oR,oL):t(oB(r),oB(1/r)):r},oj(e)}function oU(){var t=oz(os());return t.copy=function(){return ol(t,oU()).exponent(t.exponent())},nM.apply(t,arguments),t}function o$(){return oU.apply(null,arguments).exponent(.5)}function oF(t){return Math.sign(t)*t*t}function oq(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function oW(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}cY=(cV=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?og:(e=ox.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?og:(n=ox.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=oh(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):ob[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",w=ob[b],O=/[defgprs%]/.test(b);function j(t){var o,a,l,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ow[8+cH/3]:"")+j+(S&&"("===n?")":""),O){for(o=-1,a=t.length;++o<a;)if(48>(l=t.charCodeAt(o))||l>57){j=(46===l?c+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}}y&&!h&&(t=i(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=i(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=oh(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(ov(e)/3))),i=Math.pow(10,-n),o=ow[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cZ=cV.formatPrefix;function oX(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function oG(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let oH=new Date,oV=new Date;function oY(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>oY(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(oH.setTime(+e),oV.setTime(+n),t(oH),t(oV),Math.floor(r(oH,oV))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let oZ=oY(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);oZ.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?oY(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):oZ:null,oZ.range;let oK=oY(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());oK.range;let oQ=oY(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());oQ.range;let oJ=oY(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());oJ.range;let o0=oY(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());o0.range;let o1=oY(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());o1.range;let o2=oY(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);o2.range;let o5=oY(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);o5.range;let o4=oY(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function o3(t){return oY(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}o4.range;let o6=o3(0),o8=o3(1),o7=o3(2),o9=o3(3),at=o3(4),ae=o3(5),ar=o3(6);function an(t){return oY(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}o6.range,o8.range,o7.range,o9.range,at.range,ae.range,ar.range;let ai=an(0),ao=an(1),aa=an(2),ac=an(3),au=an(4),al=an(5),as=an(6);ai.range,ao.range,aa.range,ac.range,au.range,al.range,as.range;let af=oY(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());af.range;let ap=oY(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());ap.range;let ah=oY(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());ah.every=t=>isFinite(t=Math.floor(t))&&t>0?oY(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,ah.range;let ad=oY(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ay(t,e,r,n,i,o){let a=[[oK,1,1e3],[oK,5,5e3],[oK,15,15e3],[oK,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=ix(([,,t])=>t).right(a,i);if(o===a.length)return t.every(im(e/31536e6,r/31536e6,n));if(0===o)return oZ.every(Math.max(im(e,r,n),1));let[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}ad.every=t=>isFinite(t=Math.floor(t))&&t>0?oY(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ad.range;let[av,am]=ay(ad,ap,ai,o4,o1,oJ),[ab,ag]=ay(ah,af,o6,o2,o0,oQ);function ax(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function aw(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function aO(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var aj={"-":"",_:" ",0:"0"},aS=/^\s*\d+/,aP=/^%/,aA=/[\\^$*+?|[\]().{}]/g;function aE(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function a_(t){return t.replace(aA,"\\$&")}function aM(t){return RegExp("^(?:"+t.map(a_).join("|")+")","i")}function ak(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aT(t,e,r){var n=aS.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aN(t,e,r){var n=aS.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aC(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=aS.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aL(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function az(t,e,r){var n=aS.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aU(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function a$(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aF(t,e,r){var n=aS.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aq(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aW(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aX(t,e,r){var n=aS.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aG(t,e,r){var n=aS.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aH(t,e,r){var n=aS.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aV(t,e,r){var n=aP.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aY(t,e,r){var n=aS.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aZ(t,e,r){var n=aS.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aK(t,e){return aE(t.getDate(),e,2)}function aQ(t,e){return aE(t.getHours(),e,2)}function aJ(t,e){return aE(t.getHours()%12||12,e,2)}function a0(t,e){return aE(1+o2.count(ah(t),t),e,3)}function a1(t,e){return aE(t.getMilliseconds(),e,3)}function a2(t,e){return a1(t,e)+"000"}function a5(t,e){return aE(t.getMonth()+1,e,2)}function a4(t,e){return aE(t.getMinutes(),e,2)}function a3(t,e){return aE(t.getSeconds(),e,2)}function a6(t){var e=t.getDay();return 0===e?7:e}function a8(t,e){return aE(o6.count(ah(t)-1,t),e,2)}function a7(t){var e=t.getDay();return e>=4||0===e?at(t):at.ceil(t)}function a9(t,e){return t=a7(t),aE(at.count(ah(t),t)+(4===ah(t).getDay()),e,2)}function ct(t){return t.getDay()}function ce(t,e){return aE(o8.count(ah(t)-1,t),e,2)}function cr(t,e){return aE(t.getFullYear()%100,e,2)}function cn(t,e){return aE((t=a7(t)).getFullYear()%100,e,2)}function ci(t,e){return aE(t.getFullYear()%1e4,e,4)}function co(t,e){var r=t.getDay();return aE((t=r>=4||0===r?at(t):at.ceil(t)).getFullYear()%1e4,e,4)}function ca(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+aE(e/60|0,"0",2)+aE(e%60,"0",2)}function cc(t,e){return aE(t.getUTCDate(),e,2)}function cu(t,e){return aE(t.getUTCHours(),e,2)}function cl(t,e){return aE(t.getUTCHours()%12||12,e,2)}function cs(t,e){return aE(1+o5.count(ad(t),t),e,3)}function cf(t,e){return aE(t.getUTCMilliseconds(),e,3)}function cp(t,e){return cf(t,e)+"000"}function ch(t,e){return aE(t.getUTCMonth()+1,e,2)}function cd(t,e){return aE(t.getUTCMinutes(),e,2)}function cy(t,e){return aE(t.getUTCSeconds(),e,2)}function cv(t){var e=t.getUTCDay();return 0===e?7:e}function cm(t,e){return aE(ai.count(ad(t)-1,t),e,2)}function cb(t){var e=t.getUTCDay();return e>=4||0===e?au(t):au.ceil(t)}function cg(t,e){return t=cb(t),aE(au.count(ad(t),t)+(4===ad(t).getUTCDay()),e,2)}function cx(t){return t.getUTCDay()}function cw(t,e){return aE(ao.count(ad(t)-1,t),e,2)}function cO(t,e){return aE(t.getUTCFullYear()%100,e,2)}function cj(t,e){return aE((t=cb(t)).getUTCFullYear()%100,e,2)}function cS(t,e){return aE(t.getUTCFullYear()%1e4,e,4)}function cP(t,e){var r=t.getUTCDay();return aE((t=r>=4||0===r?au(t):au.ceil(t)).getUTCFullYear()%1e4,e,4)}function cA(){return"+0000"}function cE(){return"%"}function c_(t){return+t}function cM(t){return Math.floor(t/1e3)}function ck(t){return new Date(t)}function cT(t){return t instanceof Date?+t:+new Date(+t)}function cN(t,e,r,n,i,o,a,c,u,l){var s=of(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cT)):p().map(ck)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(oP(r,t)):s},s.copy=function(){return ol(s,cN(t,e,r,n,i,o,a,c,u,l))},s}function cC(){return nM.apply(cN(ab,ag,ah,af,o6,o2,o0,oQ,oK,cQ).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cI(){return nM.apply(cN(av,am,ad,ap,ai,o5,o1,oJ,oK,cJ).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cD(){var t,e,r,n,i,o=0,a=1,c=oo,u=!1;function l(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(oe),l.rangeRound=s(or),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function cB(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cR(){var t=oz(cD());return t.copy=function(){return cB(t,cR()).exponent(t.exponent())},nk.apply(t,arguments)}function cL(){return cR.apply(null,arguments).exponent(.5)}function cz(){var t,e,r,n,i,o,a,c=0,u=.5,l=1,s=1,f=oo,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=oe);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=o(c*=1),e=o(u*=1),r=o(l*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(oe),h.rangeRound=d(or),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cU(){var t=oz(cz());return t.copy=function(){return cB(t,cU()).exponent(t.exponent())},nk.apply(t,arguments)}function c$(){return cU.apply(null,arguments).exponent(.5)}function cF(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cq(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cW(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cX(t,e){return t[e]}function cG(t){let e=[];return e.key=t,e}cQ=(cK=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=aM(i),s=ak(i),f=aM(o),p=ak(o),h=aM(a),d=ak(a),y=aM(c),v=ak(c),m=aM(u),b=ak(u),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aK,e:aK,f:a2,g:cn,G:co,H:aQ,I:aJ,j:a0,L:a1,m:a5,M:a4,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:c_,s:cM,S:a3,u:a6,U:a8,V:a9,w:ct,W:ce,x:null,X:null,y:cr,Y:ci,Z:ca,"%":cE},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:cc,e:cc,f:cp,g:cj,G:cP,H:cu,I:cl,j:cs,L:cf,m:ch,M:cd,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:c_,s:cM,S:cy,u:cv,U:cm,V:cg,w:cx,W:cw,x:null,X:null,y:cO,Y:cS,Z:cA,"%":cE},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:a$,e:a$,f:aH,g:aR,G:aB,H:aq,I:aq,j:aF,L:aG,m:aU,M:aW,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:az,Q:aY,s:aZ,S:aX,u:aN,U:aC,V:aI,w:aT,W:aD,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aR,Y:aB,Z:aL,"%":aV};function O(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=aj[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,i,o=aO(1900,void 0,1);if(S(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=aw(aO(o.y,0,1))).getUTCDay())>4||0===i?ao.ceil(n):ao(n),n=o5.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=ax(aO(o.y,0,1))).getDay())>4||0===i?o8.ceil(n):o8(n),n=o2.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?aw(aO(o.y,0,1)).getUTCDay():ax(aO(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,aw(o)):ax(o)}}function S(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=w[(i=e.charAt(a++))in aj?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cK.parse,cJ=cK.utcFormat,cK.utcParse,Array.prototype.slice;var cH,cV,cY,cZ,cK,cQ,cJ,c0,c1,c2=r(90453),c5=r.n(c2),c4=r(15883),c3=r.n(c4),c6=r(21592),c8=r.n(c6),c7=r(71967),c9=r.n(c7),ut=!0,ue="[DecimalError] ",ur=ue+"Invalid argument: ",un=ue+"Exponent out of range: ",ui=Math.floor,uo=Math.pow,ua=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,uc=ui(1286742750677284.5),uu={};function ul(t,e){var r,n,i,o,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),ut?ug(e,f):e;if(u=t.d,l=e.d,a=t.e,i=e.e,u=u.slice(),o=a-i){for(o<0?(n=u,o=-o,c=l.length):(n=l,i=a,c=u.length),o>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=u.length)-(o=l.length)<0&&(o=c,n=l,l=u,u=n),r=0;o;)r=(u[--o]=u[o]+l[o]+r)/1e7|0,u[o]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=i,ut?ug(e,f):e}function us(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ur+t)}function uf(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=uv(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=uv(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}uu.absoluteValue=uu.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},uu.comparedTo=uu.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},uu.decimalPlaces=uu.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},uu.dividedBy=uu.div=function(t){return up(this,new this.constructor(t))},uu.dividedToIntegerBy=uu.idiv=function(t){var e=this.constructor;return ug(up(this,new e(t),0,1),e.precision)},uu.equals=uu.eq=function(t){return!this.cmp(t)},uu.exponent=function(){return ud(this)},uu.greaterThan=uu.gt=function(t){return this.cmp(t)>0},uu.greaterThanOrEqualTo=uu.gte=function(t){return this.cmp(t)>=0},uu.isInteger=uu.isint=function(){return this.e>this.d.length-2},uu.isNegative=uu.isneg=function(){return this.s<0},uu.isPositive=uu.ispos=function(){return this.s>0},uu.isZero=function(){return 0===this.s},uu.lessThan=uu.lt=function(t){return 0>this.cmp(t)},uu.lessThanOrEqualTo=uu.lte=function(t){return 1>this.cmp(t)},uu.logarithm=uu.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(c1))throw Error(ue+"NaN");if(this.s<1)throw Error(ue+(this.s?"NaN":"-Infinity"));return this.eq(c1)?new r(0):(ut=!1,e=up(um(this,i),um(t,i),i),ut=!0,ug(e,n))},uu.minus=uu.sub=function(t){return t=new this.constructor(t),this.s==t.s?ux(this,t):ul(this,(t.s=-t.s,t))},uu.modulo=uu.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(ue+"NaN");return this.s?(ut=!1,e=up(this,t,0,1).times(t),ut=!0,this.minus(e)):ug(new r(this),n)},uu.naturalExponential=uu.exp=function(){return uh(this)},uu.naturalLogarithm=uu.ln=function(){return um(this)},uu.negated=uu.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},uu.plus=uu.add=function(t){return t=new this.constructor(t),this.s==t.s?ul(this,t):ux(this,(t.s=-t.s,t))},uu.precision=uu.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(ur+t);if(e=ud(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},uu.squareRoot=uu.sqrt=function(){var t,e,r,n,i,o,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(ue+"NaN")}for(t=ud(this),ut=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=uf(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=ui((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=a=(r=c.precision)+3;;)if(n=(o=n).plus(up(this,o,a+2)).times(.5),uf(o.d).slice(0,a)===(e=uf(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(ug(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return ut=!0,ug(n,r)},uu.times=uu.mul=function(t){var e,r,n,i,o,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(o=f,f=p,p=o,a=u,u=l,l=a),o=[],n=a=u+l;n--;)o.push(0);for(n=l;--n>=0;){for(e=0,i=u+n;i>n;)c=o[i]+p[n]*f[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,ut?ug(t,s.precision):t},uu.toDecimalPlaces=uu.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(us(t,0,1e9),void 0===e?e=n.rounding:us(e,0,8),ug(r,t+ud(r)+1,e))},uu.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=uw(n,!0):(us(t,0,1e9),void 0===e?e=i.rounding:us(e,0,8),r=uw(n=ug(new i(n),t+1,e),!0,t+1)),r},uu.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?uw(this):(us(t,0,1e9),void 0===e?e=i.rounding:us(e,0,8),r=uw((n=ug(new i(this),t+ud(this)+1,e)).abs(),!1,t+ud(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},uu.toInteger=uu.toint=function(){var t=this.constructor;return ug(new t(this),ud(this)+1,t.rounding)},uu.toNumber=function(){return+this},uu.toPower=uu.pow=function(t){var e,r,n,i,o,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(c1);if(!(c=new u(c)).s){if(t.s<1)throw Error(ue+"Infinity");return c}if(c.eq(c1))return c;if(n=u.precision,t.eq(c1))return ug(c,n);if(a=(e=t.e)>=(r=t.d.length-1),o=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(i=new u(c1),e=Math.ceil(n/7+4),ut=!1;r%2&&uO((i=i.times(c)).d,e),0!==(r=ui(r/2));)uO((c=c.times(c)).d,e);return ut=!0,t.s<0?new u(c1).div(i):ug(i,n)}}else if(o<0)throw Error(ue+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,ut=!1,i=t.times(um(c,n+12)),ut=!0,(i=uh(i)).s=o,i},uu.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=ud(i),n=uw(i,r<=o.toExpNeg||r>=o.toExpPos)):(us(t,1,1e9),void 0===e?e=o.rounding:us(e,0,8),r=ud(i=ug(new o(i),t,e)),n=uw(i,t<=r||r<=o.toExpNeg,t)),n},uu.toSignificantDigits=uu.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(us(t,1,1e9),void 0===e?e=r.rounding:us(e,0,8)),ug(new r(this),t,e)},uu.toString=uu.valueOf=uu.val=uu.toJSON=uu[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=ud(this),e=this.constructor;return uw(this,t<=e.toExpNeg||t>=e.toExpPos)};var up=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,w,O,j,S,P=n.constructor,A=n.s==i.s?1:-1,E=n.d,_=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(ue+"Division by zero");for(l=0,u=n.e-i.e,j=_.length,w=E.length,d=(h=new P(A)).d=[];_[l]==(E[l]||0);)++l;if(_[l]>(E[l]||0)&&--u,(b=null==o?o=P.precision:a?o+(ud(n)-ud(i))+1:o)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,_=_[0],b++;(l<w||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/_|0,s=g%_|0;else{for((s=1e7/(_[0]+1)|0)>1&&(_=t(_,s),E=t(E,s),j=_.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=_.slice()).unshift(0),O=_[0],_[1]>=1e7/2&&++O;do s=0,(c=e(_,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/O|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(_,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:_,p))):(0==s&&(c=s=1),f=_.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(_,y,j,v))<1&&(s++,r(y,j<v?S:_,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,ug(h,a?o+ud(h)+1:o)}}();function uh(t,e){var r,n,i,o,a,c=0,u=0,l=t.constructor,s=l.precision;if(ud(t)>16)throw Error(un+ud(t));if(!t.s)return new l(c1);for(null==e?(ut=!1,a=s):a=e,o=new l(.03125);t.abs().gte(.1);)t=t.times(o),u+=5;for(a+=Math.log(uo(2,u))/Math.LN10*2+5|0,r=n=i=new l(c1),l.precision=a;;){if(n=ug(n.times(t),a),r=r.times(++c),uf((o=i.plus(up(n,r,a))).d).slice(0,a)===uf(i.d).slice(0,a)){for(;u--;)i=ug(i.times(i),a);return l.precision=s,null==e?(ut=!0,ug(i,s)):i}i=o}}function ud(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function uy(t,e,r){if(e>t.LN10.sd())throw ut=!0,r&&(t.precision=r),Error(ue+"LN10 precision limit exceeded");return ug(new t(t.LN10),e)}function uv(t){for(var e="";t--;)e+="0";return e}function um(t,e){var r,n,i,o,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(ue+(p.s?"NaN":"-Infinity"));if(p.eq(c1))return new d(0);if(null==e?(ut=!1,l=y):l=e,p.eq(10))return null==e&&(ut=!0),uy(d,l);if(d.precision=l+=10,n=(r=uf(h)).charAt(0),!(15e14>Math.abs(o=ud(p))))return u=uy(d,l+2,y).times(o+""),p=um(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(ut=!0,ug(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=uf((p=p.times(t)).d)).charAt(0),f++;for(o=ud(p),n>1?(p=new d("0."+r),o++):p=new d(n+"."+r.slice(1)),c=a=p=up(p.minus(c1),p.plus(c1),l),s=ug(p.times(p),l),i=3;;){if(a=ug(a.times(s),l),uf((u=c.plus(up(a,new d(i),l))).d).slice(0,l)===uf(c.d).slice(0,l))return c=c.times(2),0!==o&&(c=c.plus(uy(d,l+2,y).times(o+""))),c=up(c,new d(f),l),d.precision=y,null==e?(ut=!0,ug(c,y)):c;c=u,i+=2}}function ub(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=ui((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),ut&&(t.e>uc||t.e<-uc))throw Error(un+r)}else t.s=0,t.e=0,t.d=[0];return t}function ug(t,e,r){var n,i,o,a,c,u,l,s,f=t.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(o=f.length))return t;for(a=1,l=o=f[s];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=l/(o=uo(10,a-i-1))%10|0,u=e<0||void 0!==f[s+1]||l%o,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?l/uo(10,a-i):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(o=ud(t),f.length=1,e=e-o-1,f[0]=uo(10,(7-e%7)%7),t.e=ui(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,o=1,s--):(f.length=s+1,o=uo(10,7-n),f[s]=i>0?(l/uo(10,a-i)%uo(10,i)|0)*o:0),u)for(;;)if(0==s){1e7==(f[0]+=o)&&(f[0]=1,++t.e);break}else{if(f[s]+=o,1e7!=f[s])break;f[s--]=0,o=1}for(n=f.length;0===f[--n];)f.pop();if(ut&&(t.e>uc||t.e<-uc))throw Error(un+ud(t));return t}function ux(t,e){var r,n,i,o,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),ut?ug(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(i=Math.max(Math.ceil(h/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((s=(i=u.length)<(c=f.length))&&(c=i),i=0;i<c;i++)if(u[i]!=f[i]){s=u[i]<f[i];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,i=f.length-c;i>0;--i)u[c++]=0;for(i=f.length;i>a;){if(u[--i]<f[i]){for(o=i;o&&0===u[--o];)u[o]=1e7-1;--u[o],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,ut?ug(e,h):e):new p(0)}function uw(t,e,r){var n,i=ud(t),o=uf(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+uv(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+uv(-i-1)+o,r&&(n=r-a)>0&&(o+=uv(n))):i>=a?(o+=uv(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+uv(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=uv(n))),t.s<0?"-"+o:o}function uO(t,e){if(t.length>e)return t.length=e,!0}function uj(t){if(!t||"object"!=typeof t)throw Error(ue+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(ui(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(ur+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(ur+r+": "+n);return this}var c0=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(ur+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return ub(this,t.toString())}if("string"!=typeof t)throw Error(ur+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,ua.test(t))ub(this,t);else throw Error(ur+t)}if(o.prototype=uu,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=uj,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c1=new c0(1);let uS=c0;function uP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var uA=function(t){return t},uE={},u_=function(t){return t===uE},uM=function(t){return function e(){return 0==arguments.length||1==arguments.length&&u_(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uk=function(t){return function t(e,r){return 1===e?r:uM(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==uE}).length;return a>=e?r.apply(void 0,i):t(e-a,uM(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return u_(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uP(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return uP(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uP(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uT=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uN=uk(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uC=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return uA;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},uI=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uD=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};uk(function(t,e,r){var n=+t;return n+r*(e-n)}),uk(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),uk(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uB={rangeStep:function(t,e,r){for(var n=new uS(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uS(t).abs().log(10).toNumber())+1}};function uR(t){return function(t){if(Array.isArray(t))return uU(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uz(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uL(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||uz(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uz(t,e){if(t){if("string"==typeof t)return uU(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uU(t,e)}}function uU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u$(t){var e=uL(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function uF(t,e,r){if(t.lte(0))return new uS(0);var n=uB.getDigitCount(t.toNumber()),i=new uS(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new uS(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new uS(Math.ceil(c))}function uq(t,e,r){var n=1,i=new uS(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new uS(10).pow(uB.getDigitCount(t)-1),i=new uS(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new uS(Math.floor(t)))}else 0===t?i=new uS(Math.floor((e-1)/2)):r||(i=new uS(Math.floor(t)));var a=Math.floor((e-1)/2);return uC(uN(function(t){return i.add(new uS(t-a).mul(n)).toNumber()}),uT)(0,e)}var uW=uD(function(t){var e=uL(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=uL(u$([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uR(uT(0,i-1).map(function(){return 1/0}))):[].concat(uR(uT(0,i-1).map(function(){return-1/0})),[l]);return r>n?uI(s):s}if(u===l)return uq(u,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uS(0),tickMin:new uS(0),tickMax:new uS(0)};var c=uF(new uS(r).sub(e).div(n-1),i,a),u=Math.ceil((o=e<=0&&r>=0?new uS(0):(o=new uS(e).add(r).div(2)).sub(new uS(o).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uS(r).sub(o).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,i,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:o.sub(new uS(u).mul(c)),tickMax:o.add(new uS(l).mul(c))})}(u,l,a,o),p=f.step,h=f.tickMin,d=f.tickMax,y=uB.rangeStep(h,d.add(new uS(.1).mul(p)),p);return r>n?uI(y):y});uD(function(t){var e=uL(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=uL(u$([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uq(u,i,o);var s=uF(new uS(l).sub(u).div(a-1),o,0),f=uC(uN(function(t){return new uS(u).add(new uS(t).mul(s)).toNumber()}),uT)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uI(f):f});var uX=uD(function(t,e){var r=uL(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uL(u$([n,i]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var l=Math.max(e,2),s=uF(new uS(u).sub(c).div(l-1),o,0),f=[].concat(uR(uB.rangeStep(new uS(c),new uS(u).sub(new uS(.99).mul(s)),s)),[u]);return n>i?uI(f):f}),uG=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uH(t){return(uH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uV(){return(uV=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uZ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uZ=function(){return!!t})()}function uK(t){return(uK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uQ(t,e){return(uQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uJ(t,e,r){return(e=u0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u0(t){var e=function(t,e){if("object"!=uH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uH(e)?e:e+""}var u1=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=uK(t),function(t,e){if(e&&("object"===uH(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uZ()?Reflect.construct(t,e||[],uK(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&uQ(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,i=t.dataKey,o=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tA(function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,uG),!1);"x"===this.props.direction&&"number"!==u.type&&tU(!1);var f=o.map(function(t){var o,f,p=c(t,i),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uY(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uY(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=b[0],f=b[1]}else o=f=v;if("vertical"===r){var g=u.scale,x=d+e,w=x+n,O=x-n,j=g(y-o),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=l.scale,A=h+e,E=A-n,_=A+n,M=P(y-o),k=P(y+f);m.push({x1:E,y1:k,x2:_,y2:k}),m.push({x1:A,y1:M,x2:A,y2:k}),m.push({x1:E,y1:M,x2:_,y2:M})}return a().createElement(tG,uV({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uV({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tG,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u0(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function u2(t){return(u2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u5(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u2(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uJ(u1,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uJ(u1,"displayName","ErrorBar");var u3=function(t){var e,r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,o=t.legendContent,a=tO(r,rc);if(!a)return null;var c=rc.defaultProps,u=void 0!==c?u4(u4({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===o?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u4(u4({},r),e.props):{},i=n.dataKey,o=n.name,a=n.legendType;return{inactive:n.hide,dataKey:i,type:u.iconType||a||"square",color:lo(e),value:o||i,payload:n}}),u4(u4(u4({},u),rc.getWithHeight(a,i)),{},{payload:e,item:a})};function u6(t){return(u6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u8(t){return function(t){if(Array.isArray(t))return u7(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u7(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u7(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u7(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u9(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u9(Object(r),!0).forEach(function(e){le(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u9(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function le(t,e,r){var n;return(n=function(t,e){if("object"!=u6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==u6(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lr(t,e,r){return J()(t)||J()(e)?r:F(e)?B()(t,e,r):te()(e)?e(t):r}function ln(t,e,r,n){var i=c8()(t,function(t){return lr(t,e)});if("number"===r){var o=i.filter(function(t){return $(t)||parseFloat(t)});return o.length?[c3()(o),c5()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!J()(t)}):i).map(function(t){return F(t)||t instanceof Date?t:""})}var li=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(z(s-l)!==z(f-s)){var h=[];if(z(f-s)===z(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},lo=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?lt(lt({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},la=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var l=i[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tm(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?lt(lt({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var w=J()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:J()(w)?void 0:X(w,r,0)})}}return o},lc=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=X(r,i,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=i&&(h-=(u-1)*l,l=0),h>=i&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((i-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(u8(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=X(n,i,0,!0);i-2*y-(u-1)*l<=0&&(l=0);var v=(i-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(u8(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},lu=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=u3({children:i,legendWidth:o-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&$(t[f]))return lt(lt({},t),{},le({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&$(t[p]))return lt(lt({},t),{},le({},p,t[p]+(s||0)))}return t},ll=function(t,e,r,n,i){var o=tw(e.props.children,u1).filter(function(t){var e;return e=t.props.direction,!!J()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(o&&o.length){var a=o.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=lr(e,r);if(J()(n))return t;var i=Array.isArray(n)?[c3()(n),c5()(n)]:[n,n],o=a.reduce(function(t,r){var n=lr(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},ls=function(t,e,r,n,i){var o=e.map(function(e){return ll(t,e,r,i,n)}).filter(function(t){return!J()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},lf=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&ll(t,e,o,n)||ln(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},lp=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},lh=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},ld=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*z(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!I()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}})},ly=new WeakMap,lv=function(t,e){if("function"!=typeof e)return t;ly.has(t)||ly.set(t,new WeakMap);var r=ly.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},lm=function(t,e,r){var i=t.scale,o=t.type,a=t.layout,c=t.axisType;if("auto"===i)return"radial"===a&&"radiusAxis"===c?{scale:nB(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:oS(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nR(),realScaleType:"point"}:"category"===o?{scale:nB(),realScaleType:"band"}:{scale:oS(),realScaleType:"linear"};if(N()(i)){var u="scale".concat(eg()(i));return{scale:(n[u]||nR)(),realScaleType:n[u]?u:"point"}}return te()(i)?{scale:i}:{scale:nR(),realScaleType:"point"}},lb=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},lg=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lx=function(t,e){if(!e||2!==e.length||!$(e[0])||!$(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!$(t[0])||t[0]<r)&&(i[0]=r),(!$(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},lw={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=I()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}cF(t,e)}},none:cF,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}cF(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<i;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=l/u)}r[a-1][1]+=r[a-1][0]=o,cF(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=I()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},lO=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=lw[r];return(function(){var t=eD([]),e=cW,r=cF,n=cX;function i(i){var o,a,c=Array.from(t.apply(this,arguments),cG),u=c.length,l=-1;for(let t of i)for(o=0,++l;o<u;++o)(c[o][l]=[0,+n(t,c[o].key,l,i)]).data=t;for(o=0,a=cq(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:eD(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:eD(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?cW:"function"==typeof t?t:eD(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?cF:t,i):r},i})().keys(n).value(function(t,e){return+lr(t,e,0)}).order(cW).offset(i)(t)},lj=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?lt(lt({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(F(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[W("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return lt(lt({},t),{},le({},c,u))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return lt(lt({},e),{},le({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lO(t,a.items,i)}))},{})),lt(lt({},e),{},le({},o,c))},{})},lS=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var l=uW(u,i,a);return t.domain([c3()(l),c5()(l)]),{niceTicks:l}}return i&&"number"===n?{niceTicks:uX(t.domain(),i,a)}:null},lP=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=lr(o,e.dataKey,e.domain[a]);return J()(c)?null:e.scale(c)-i/2+n},lA=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},lE=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?lt(lt({},t.type.defaultProps),t.props):t.props).stackId;if(F(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},l_=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[c3()(e.concat([t[0]]).filter($)),c5()(e.concat([t[1]]).filter($))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lM=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lk=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lT=function(t,e,r){if(te()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if($(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lM.test(t[0])){var i=+lM.exec(t[0])[1];n[0]=e[0]-i}else te()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if($(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lk.test(t[1])){var o=+lk.exec(t[1])[1];n[1]=e[1]+o}else te()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lN=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=tz()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],l=i[a-1];o=Math.min((u.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},lC=function(t,e,r){return!t||!t.length||c9()(t,B()(r,"type.defaultProps.domain"))?e:t},lI=function(t,e){var r=t.type.defaultProps?lt(lt({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return lt(lt({},tA(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:lo(t),value:lr(e,n),type:c,payload:e,chartType:u,hide:l})};function lD(t){return(lD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lB(Object(r),!0).forEach(function(e){lL(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lL(t,e,r){var n;return(n=function(t,e){if("object"!=lD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lD(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lz=["Webkit","Moz","O","ms"],lU=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lz.reduce(function(t,n){return lR(lR({},t),{},lL({},n+r,e))},{});return n[t]=e,n};function l$(t){return(l$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lF(){return(lF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lq(Object(r),!0).forEach(function(e){lY(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lX(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lZ(n.key),n)}}function lG(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lG=function(){return!!t})()}function lH(t){return(lH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lV(t,e){return(lV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lY(t,e,r){return(e=lZ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lZ(t){var e=function(t,e){if("object"!=l$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l$(e)?e:e+""}var lK=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nR().domain(tR()(0,c)).range([i,i+o-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lQ=function(t){return t.changedTouches&&!!t.changedTouches.length},lJ=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=lH(r),lY(e=function(t,e){if(e&&("object"===l$(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lG()?Reflect.construct(r,i||[],lH(this).constructor):r.apply(this,i)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lY(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lY(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),lY(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lY(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lY(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lY(e,"handleSlideDragStart",function(t){var r=lQ(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lV(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,i=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(i,u),f=n.getIndexInRange(i,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=lr(r[t],i,t);return te()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lQ(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(lY(lY({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(lY({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:o,x:e,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,c=t.data,u=t.children,l=t.padding,s=o.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:i,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,o=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lW(lW({},tA(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(i=h[y])?void 0:i.name);return a().createElement(tG,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),o.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,i=r.height,o=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:o,fillOpacity:.2,x:u,y:n,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,i=t.height,o=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tG,{className:"recharts-brush-texts"},a().createElement(il,lF({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+i/2},f),this.getTextOfTick(e)),a().createElement(il,lF({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+o+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,i=t.x,o=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!$(i)||!$(o)||!$(c)||!$(u)||c<=0||u<=0)return null;var m=(0,_.A)("recharts-brush",r),b=1===a().Children.count(n),g=lU("userSelect","none");return a().createElement(tG,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,i=t.height,o=t.stroke,c=Math.floor(r+i/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:i,fill:o,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):te()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lW({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?lK({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&lX(n.prototype,e),r&&lX(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function l0(t){return(l0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l1(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=l0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==l0(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}lY(lJ,"displayName","Brush"),lY(lJ,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var l5=Math.PI/180,l4=function(t,e,r,n){return{x:t+Math.cos(-l5*n)*r,y:e+Math.sin(-l5*n)*r}},l3=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l6=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=l3({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},l8=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},l7=function(t,e){var r,n=l6({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,c=e.outerRadius;if(i<a||i>c)return!1;if(0===i)return!0;var u=l8(e),l=u.startAngle,s=u.endAngle,f=o;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?l2(l2({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function l9(t){return(l9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var st=["offset"];function se(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sr(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=l9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l9(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function si(){return(si=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var so=function(t){var e=t.value,r=t.formatter,n=J()(t.children)?e:t.children;return te()(r)?r(n):n},sa=function(t,e,r){var n,i,o=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+h)/2,b=z(y-d)*Math.min(Math.abs(y-d),360),g=b>=0?1:-1;"insideStart"===o?(n=d+g*u,i=v):"insideEnd"===o?(n=y-g*u,i=!v):"end"===o&&(n=y+g*u,i=v),i=b<=0?i:!i;var x=l4(s,f,m,n),w=l4(s,f,m,n+(i?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!i,",\n    ").concat(w.x,",").concat(w.y),j=J()(t.id)?W("recharts-radial-line-"):t.id;return a().createElement("text",si({},r,{dominantBaseline:"central",className:(0,_.A)("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:j,d:O})),a().createElement("textPath",{xlinkHref:"#".concat(j)},e))},sc=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=l4(i,o,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=l4(i,o,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},su=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return sn(sn({},{x:o+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return sn(sn({},{x:o+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===i){var m={x:o-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return sn(sn({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===i){var b={x:o+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return sn(sn({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===i?sn({x:o+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===i?sn({x:o+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===i?sn({x:o+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===i?sn({x:o+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===i?sn({x:o+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===i?sn({x:o+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===i?sn({x:o+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===i?sn({x:o+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):tn()(i)&&($(i.x)||U(i.x))&&($(i.y)||U(i.y))?sn({x:o+X(i.x,c),y:a+X(i.y,u),textAnchor:"end",verticalAnchor:"end"},g):sn({x:o+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function sl(t){var e,r=t.offset,n=sn({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,st)),i=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!i||J()(u)&&J()(l)&&!(0,o.isValidElement)(s)&&!te()(s))return null;if((0,o.isValidElement)(s))return(0,o.cloneElement)(s,n);if(te()(s)){if(e=(0,o.createElement)(s,n),(0,o.isValidElement)(e))return e}else e=so(n);var h="cx"in i&&$(i.cx),d=tA(n,!0);if(h&&("insideStart"===c||"insideEnd"===c||"end"===c))return sa(n,e,d);var y=h?sc(n):su(n);return a().createElement(il,si({className:(0,_.A)("recharts-label",void 0===f?"":f)},d,y,{breakAll:p}),e)}sl.displayName="Label";var ss=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if($(d)&&$(y)){if($(s)&&$(f))return{x:s,y:f,width:d,height:y};if($(p)&&$(h))return{x:p,y:h,width:d,height:y}}return $(s)&&$(f)?{x:s,y:f,width:0,height:0}:$(e)&&$(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};sl.parseViewBox=ss,sl.renderCallByParent=function(t,e){var r,n,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var c=t.children,u=ss(t),l=tw(c,sl).map(function(t,r){return(0,o.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!i)return l;return[(r=t.label,n=e||u,!r?null:!0===r?a().createElement(sl,{key:"label-implicit",viewBox:n}):F(r)?a().createElement(sl,{key:"label-implicit",viewBox:n,value:r}):(0,o.isValidElement)(r)?r.type===sl?(0,o.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(sl,{key:"label-implicit",content:r,viewBox:n}):te()(r)?a().createElement(sl,{key:"label-implicit",content:r,viewBox:n}):tn()(r)?a().createElement(sl,si({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return se(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return se(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return se(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var sf=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sp=r(69691),sh=r.n(sp),sd=r(47212),sy=r.n(sd),sv=function(t){return null};sv.displayName="Cell";var sm=r(5359),sb=r.n(sm);function sg(t){return(sg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sx=["valueAccessor"],sw=["data","dataKey","clockWise","id","textBreakAll"];function sO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sj(){return(sj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sS(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sg(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sA(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var sE=function(t){return Array.isArray(t.value)?sb()(t.value):t.value};function s_(t){var e=t.valueAccessor,r=void 0===e?sE:e,n=sA(t,sx),i=n.data,o=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=sA(n,sw);return i&&i.length?a().createElement(tG,{className:"recharts-label-list"},i.map(function(t,e){var n=J()(o)?r(t,e):lr(t&&t.payload,o),i=J()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(sl,sj({},tA(t,!0),s,i,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:sl.parseViewBox(J()(c)?t:sP(sP({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}s_.displayName="LabelList",s_.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=tw(t.children,s_).map(function(t,r){return(0,o.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(s_,{key:"labelList-implicit",data:e}):a().isValidElement(r)||te()(r)?a().createElement(s_,{key:"labelList-implicit",data:e,content:r}):tn()(r)?a().createElement(s_,sj({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sO(t)}(i)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return sO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sO(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):i};var sM=r(38404),sk=r.n(sM),sT=r(98451),sN=r.n(sT);function sC(t){return(sC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sI(){return(sI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sB(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sC(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sL=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},sz={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sU=function(t){var e,r=sR(sR({},sz),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sD(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sD(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=(0,_.A)("recharts-trapezoid",d);return b?a().createElement(ng,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,i=t.lowerWidth,o=t.height,u=t.x,l=t.y;return a().createElement(ng,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",sI({},tA(r,!0),{className:g,d:sL(u,l,e,i,o),ref:n})))}):a().createElement("g",null,a().createElement("path",sI({},tA(r,!0),{className:g,d:sL(l,s,f,p,h)})))};function s$(t){return(s$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sF(){return(sF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sq(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s$(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sX=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/l5,f=u?i:i+o*s;return{center:l4(e,r,l,f),circleTangency:l4(e,r,n,f),lineTangency:l4(e,r,l*Math.cos(s*l5),u?i-o*s:i),theta:s}},sG=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=t.endAngle,c=z(a-o)*Math.min(Math.abs(a-o),359.999),u=o+c,l=l4(e,r,i,o),s=l4(e,r,i,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(o>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=l4(e,r,n,o),h=l4(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(o<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sH=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=z(l-u),f=sX({cx:e,cy:r,radius:i,angle:u,sign:s,cornerRadius:o,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sX({cx:e,cy:r,radius:i,angle:l,sign:-s,cornerRadius:o,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):sG({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sX({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,P=sX({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=P.circleTangency,E=P.lineTangency,_=P.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-_;if(M<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sV={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sY=function(t){var e,r=sW(sW({},sV),t),n=r.cx,i=r.cy,o=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,h=r.className;if(c<o||f===p)return null;var d=(0,_.A)("recharts-sector",h),y=c-o,v=X(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?sH({cx:n,cy:i,innerRadius:o,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):sG({cx:n,cy:i,innerRadius:o,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sF({},tA(r,!0),{className:d,d:e,role:"img"}))},sZ=["option","shapeType","propTransformer","activeClassName","isActive"];function sK(t){return(sK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sQ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sK(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s0(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(n_,r);case"trapezoid":return a().createElement(sU,r);case"sector":return a().createElement(sY,r);case"symbols":if("symbols"===e)return a().createElement(eZ,r);break;default:return null}}function s1(t){var e,r=t.option,n=t.shapeType,i=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,sZ);if((0,o.isValidElement)(r))e=(0,o.cloneElement)(r,sJ(sJ({},l),(0,o.isValidElement)(r)?r.props:r));else if(te()(r))e=r(l);else if(sk()(r)&&!sN()(r)){var s=(void 0===i?function(t,e){return sJ(sJ({},e),t)}:i)(r,l);e=a().createElement(s0,{shapeType:n,elementProps:s})}else e=a().createElement(s0,{shapeType:n,elementProps:l});return u?a().createElement(tG,{className:void 0===c?"recharts-active-shape":c},e):e}function s2(t,e){return null!=e&&"trapezoids"in t.props}function s5(t,e){return null!=e&&"sectors"in t.props}function s4(t,e){return null!=e&&"points"in t.props}function s3(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function s6(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function s8(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}var s7=["x","y"];function s9(t){return(s9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ft(){return(ft=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fe(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s9(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fe(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fn(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,s7),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),u=parseInt("".concat(e.width||i.width),10);return fr(fr(fr(fr(fr({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function fi(t){return a().createElement(s1,ft({shapeType:"rectangle",propTransformer:fn,activeClassName:"recharts-active-bar"},t))}var fo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||tU(!1),e)}},fa=["value","background"];function fc(t){return(fc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fu(){return(fu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fs(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fl(Object(r),!0).forEach(function(e){fy(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ff(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fv(n.key),n)}}function fp(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fp=function(){return!!t})()}function fh(t){return(fh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fd(t,e){return(fd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fy(t,e,r){return(e=fv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fv(t){var e=function(t,e){if("object"!=fc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fc(e)?e:e+""}var fm=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=fh(e),fy(t=function(t,e){if(e&&("object"===fc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fp()?Reflect.construct(e,r||[],fh(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fy(t,"id",W("recharts-bar-")),fy(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fy(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fd(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,o=r.activeIndex,c=r.activeBar,u=tA(this.props,!1);return t&&t.map(function(t,r){var l=r===o,s=fs(fs(fs({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tG,fu({className:"recharts-bar-rectangle"},tp(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(fi,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,o=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(ng,{begin:o,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=s&&s[e];if(r){var o=V(r.x,t.x),a=V(r.y,t.y),c=V(r.width,t.width),u=V(r.height,t.height);return fs(fs({},t),{},{x:o(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===n){var l=V(0,t.height)(i);return fs(fs({},t),{},{y:t.y+t.height-l,height:l})}var f=V(0,t.width)(i);return fs(fs({},t),{},{width:f})});return a().createElement(tG,null,t.renderRectanglesStatically(o))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c9()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,o=tA(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,fa);if(!c)return null;var l=fs(fs(fs(fs(fs({},u),{},{fill:"#eee"},c),o),tp(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(fi,fu({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,o=r.yAxis,c=r.layout,u=tw(r.children,u1);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:lr(t,e)}};return a().createElement(tG,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.xAxis,o=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,y=(0,_.A)("recharts-bar",n),v=i&&i.allowDataOverflow,m=o&&o.allowDataOverflow,b=v||m,g=J()(h)?this.id:h;return a().createElement(tG,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,a().createElement(tG,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||d)&&s_.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&ff(n.prototype,e),r&&ff(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function fb(t){return(fb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fg(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fj(n.key),n)}}function fx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fx(Object(r),!0).forEach(function(e){fO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fO(t,e,r){return(e=fj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fj(t){var e=function(t,e){if("object"!=fb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fb(e)?e:e+""}fy(fm,"displayName","Bar"),fy(fm,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ei.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fy(fm,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=lg(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fs(fs({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:o,w=l?x.scale.domain():null,O=lA({numericAxis:x}),j=tw(b,sv),S=f.map(function(t,e){l?f=lx(l[s+e],w):Array.isArray(f=lr(t,m))||(f=[O,f]);var n=fo(g,fm.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],E=P[1];p=lP({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:t,index:e}),y=null!=(S=null!=E?E:A)?S:void 0,v=h.size;var _=A-E;if(b=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=z(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var k=[o.scale(f[0]),o.scale(f[1])],T=k[0],N=k[1];if(p=T,y=lP({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:t,index:e}),v=N-T,b=h.size,x={x:o.x,y:y,width:o.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var C=z(v||n)*(Math.abs(n)-Math.abs(v));v+=C}}return fs(fs(fs({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lI(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fs({data:S,layout:d},p)});var fS=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},fP=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fg(r.prototype,t),e&&fg(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fO(fP,"EPS",1e-4);var fA=function(t){var e=Object.keys(t).reduce(function(e,r){return fw(fw({},e),{},fO({},r,fP.create(t[r])))},{});return fw(fw({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return sh()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return sy()(t,function(t,r){return e[r].isInRange(t)})}})},fE=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))};function f_(){return(f_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fM(t){return(fM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fk(Object(r),!0).forEach(function(e){fD(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fN(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fN=function(){return!!t})()}function fC(t){return(fC=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fI(t,e){return(fI=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fD(t,e,r){return(e=fB(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fB(t){var e=function(t,e){if("object"!=fM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fM(e)?e:e+""}var fR=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=fA({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return sf(t,"discard")&&!o.isInRange(a)?null:a},fL=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fC(t),function(t,e){if(e&&("object"===fM(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fN()?Reflect.construct(t,e||[],fC(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fI(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.r,o=t.alwaysShow,c=t.clipPathId,u=F(e),l=F(n);if(K(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fR(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=fT(fT({clipPath:sf(this.props,"hidden")?"url(#".concat(c,")"):void 0},tA(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tG,{className:(0,_.A)("recharts-reference-dot",y)},r.renderDot(d,v),sl.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fB(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fD(fL,"displayName","ReferenceDot"),fD(fL,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fD(fL,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):te()(t)?t(e):a().createElement(rl,f_({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fz=r(67367),fU=r.n(fz),f$=r(22964),fF=r.n(f$),fq=r(86451),fW=r.n(fq)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fX=(0,o.createContext)(void 0),fG=(0,o.createContext)(void 0),fH=(0,o.createContext)(void 0),fV=(0,o.createContext)({}),fY=(0,o.createContext)(void 0),fZ=(0,o.createContext)(0),fK=(0,o.createContext)(0),fQ=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,i=e.offset,o=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fW(i);return a().createElement(fX.Provider,{value:r},a().createElement(fG.Provider,{value:n},a().createElement(fV.Provider,{value:i},a().createElement(fH.Provider,{value:s},a().createElement(fY.Provider,{value:o},a().createElement(fZ.Provider,{value:l},a().createElement(fK.Provider,{value:u},c)))))))},fJ=function(t){var e=(0,o.useContext)(fX);null==e&&tU(!1);var r=e[t];return null==r&&tU(!1),r},f0=function(){var t=(0,o.useContext)(fG);return fF()(t,function(t){return sy()(t.domain,Number.isFinite)})||G(t)},f1=function(t){var e=(0,o.useContext)(fG);null==e&&tU(!1);var r=e[t];return null==r&&tU(!1),r},f2=function(){return(0,o.useContext)(fK)},f5=function(){return(0,o.useContext)(fZ)};function f4(t){return(f4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f3(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f3=function(){return!!t})()}function f6(t){return(f6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f8(t,e){return(f8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f7(Object(r),!0).forEach(function(e){pt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function pt(t,e,r){return(e=pe(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pe(t){var e=function(t,e){if("object"!=f4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f4(e)?e:e+""}function pr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function pn(){return(pn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var pi=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):te()(t)?t(e):a().createElement("line",pn({},e,{className:"recharts-reference-line-line"}))},po=function(t,e,r,n,i,o,a,c,u){var l=i.x,s=i.y,f=i.width,p=i.height;if(r){var h=u.y,d=t.y.apply(h,{position:o});if(sf(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:o});if(sf(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:o})});return sf(u,"discard")&&fU()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pa(t){var e,r=t.x,n=t.y,i=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,o.useContext)(fY),h=fJ(c),d=f1(u),y=(0,o.useContext)(fH);if(!p||!y)return null;K(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=po(fA({x:h.scale,y:d.scale}),F(r),F(n),i&&2===i.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return pr(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pr(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],O=w.x,j=w.y,S=f9(f9({clipPath:sf(t,"hidden")?"url(#".concat(p,")"):void 0},tA(t,!0)),{},{x1:g,y1:x,x2:O,y2:j});return a().createElement(tG,{className:(0,_.A)("recharts-reference-line",s)},pi(l,S),sl.renderCallByParent(t,fS({x:(e={x1:g,y1:x,x2:O,y2:j}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pc=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f6(t),function(t,e){if(e&&("object"===f4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f3()?Reflect.construct(t,e||[],f6(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f8(r,t),e=[{key:"render",value:function(){return a().createElement(pa,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pe(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pu(){return(pu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pl(t){return(pl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ps(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ps(Object(r),!0).forEach(function(e){py(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ps(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pt(pc,"displayName","ReferenceLine"),pt(pc,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pp(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pp=function(){return!!t})()}function ph(t){return(ph=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pd(t,e){return(pd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function py(t,e,r){return(e=pv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pv(t){var e=function(t,e){if("object"!=pl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pl(e)?e:e+""}var pm=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,l=i.xAxis,s=i.yAxis;if(!l||!s)return null;var f=fA({x:l.scale,y:s.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sf(i,"discard")||f.isInRange(p)&&f.isInRange(h)?fS(p,h):null},pb=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ph(t),function(t,e){if(e&&("object"===pl(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pp()?Reflect.construct(t,e||[],ph(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pd(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,i=t.y1,o=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;K(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=F(e),f=F(n),p=F(i),h=F(o),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=pm(s,f,p,h,this.props);if(!y&&!d)return null;var v=sf(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tG,{className:(0,_.A)("recharts-reference-area",c)},r.renderRect(d,pf(pf({clipPath:v},tA(this.props,!0)),y)),sl.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pv(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pg(t){return function(t){if(Array.isArray(t))return px(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return px(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return px(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function px(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}py(pb,"displayName","ReferenceArea"),py(pb,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),py(pb,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):te()(t)?t(e):a().createElement(n_,pu({},e,{className:"recharts-reference-area-rect"}))});var pw=function(t,e,r,n,i){var o=tw(t,pc),a=tw(t,fL),c=[].concat(pg(o),pg(a)),u=tw(t,pb),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sf(e.props,"extendDomain")&&$(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sf(e.props,"extendDomain")&&$(e.props[p])&&$(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return $(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pO=r(11117),pj=new(r.n(pO)()),pS="recharts.syncMouseEvents";function pP(t){return(pP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pA(t,e,r){return(e=pE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pE(t){var e=function(t,e){if("object"!=pP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pP(e)?e:e+""}var p_=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pA(this,"activeIndex",0),pA(this,"coordinateList",[]),pA(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pE(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pM(){}function pk(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pT(t){this._context=t}function pN(t){this._context=t}function pC(t){this._context=t}pT.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pk(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pk(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pN.prototype={areaStart:pM,areaEnd:pM,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pk(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pC.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pk(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pI{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pD(t){this._context=t}function pB(t){this._context=t}function pR(t){return new pB(t)}pD.prototype={areaStart:pM,areaEnd:pM,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pL(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function pz(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pU(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function p$(t){this._context=t}function pF(t){this._context=new pq(t)}function pq(t){this._context=t}function pW(t){this._context=t}function pX(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function pG(t,e){this._context=t,this._t=e}function pH(t){return t[0]}function pV(t){return t[1]}function pY(t,e){var r=eD(!0),n=null,i=pR,o=null,a=e$(c);function c(c){var u,l,s,f=(c=cq(c)).length,p=!1;for(null==n&&(o=i(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(l,u,c),+e(l,u,c));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?pH:eD(t),e="function"==typeof e?e:void 0===e?pV:eD(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eD(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eD(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function pZ(t,e,r){var n=null,i=eD(!0),o=null,a=pR,c=null,u=e$(l);function l(l){var s,f,p,h,d,y=(l=cq(l)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&i(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pY().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?pH:eD(+t),e="function"==typeof e?e:void 0===e?eD(0):eD(+e),r="function"==typeof r?r:void 0===r?pV:eD(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eD(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eD(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eD(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eD(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eD(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:eD(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),l):o},l}function pK(t){return(pK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pQ(){return(pQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pJ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=pK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pK(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pB.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},p$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pU(this,this._t0,pz(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pU(this,pz(this,r=pL(this,t,e)),r);break;default:pU(this,this._t0,r=pL(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pF.prototype=Object.create(p$.prototype)).point=function(t,e){p$.prototype.point.call(this,e,t)},pq.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},pW.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pX(t),i=pX(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pG.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var p1={curveBasisClosed:function(t){return new pN(t)},curveBasisOpen:function(t){return new pC(t)},curveBasis:function(t){return new pT(t)},curveBumpX:function(t){return new pI(t,!0)},curveBumpY:function(t){return new pI(t,!1)},curveLinearClosed:function(t){return new pD(t)},curveLinear:pR,curveMonotoneX:function(t){return new p$(t)},curveMonotoneY:function(t){return new pF(t)},curveNatural:function(t){return new pW(t)},curveStep:function(t){return new pG(t,.5)},curveStepAfter:function(t){return new pG(t,1)},curveStepBefore:function(t){return new pG(t,0)}},p2=function(t){return t.x===+t.x&&t.y===+t.y},p5=function(t){return t.x},p4=function(t){return t.y},p3=function(t,e){if(te()(t))return t;var r="curve".concat(eg()(t));return("curveMonotone"===r||"curveBump"===r)&&e?p1["".concat(r).concat("vertical"===e?"Y":"X")]:p1[r]||pR},p6=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=p3(void 0===r?"linear":r,a),s=u?i.filter(function(t){return p2(t)}):i;if(Array.isArray(o)){var f=u?o.filter(function(t){return p2(t)}):o,p=s.map(function(t,e){return p0(p0({},t),{},{base:f[e]})});return(e="vertical"===a?pZ().y(p4).x1(p5).x0(function(t){return t.base.x}):pZ().x(p5).y1(p4).y0(function(t){return t.base.y})).defined(p2).curve(l),e(p)}return(e="vertical"===a&&$(o)?pZ().y(p4).x1(p5).x0(o):$(o)?pZ().x(p5).y1(p4).y0(o):pY().x(p5).y(p4)).defined(p2).curve(l),e(s)},p8=function(t){var e=t.className,r=t.points,n=t.path,i=t.pathRef;if((!r||!r.length)&&!n)return null;var o=r&&r.length?p6(t):n;return a().createElement("path",pQ({},tA(t,!1),tf(t),{className:(0,_.A)("recharts-curve",e),d:o,ref:i}))};function p7(t){return(p7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p9=["x","y","top","left","width","height","className"];function ht(){return(ht=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function he(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var hr=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,i=void 0===n?0:n,o=t.top,c=void 0===o?0:o,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?he(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=p7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p7(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):he(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,p9));return $(r)&&$(i)&&$(f)&&$(h)&&$(c)&&$(l)?a().createElement("path",ht({},tA(y,!0),{className:(0,_.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(i,"h").concat(f)})):null};function hn(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[l4(e,r,n,i),l4(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}function hi(t){return(hi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ho(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ha(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ho(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hi(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hi(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ho(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hc(t){var e,r,n,i,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var m=p8;if("ScatterChart"===y)i=l,m=hr;else if("BarChart"===y)e=h/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=n_;else if("radial"===d){var b=hn(l),g=b.cx,x=b.cy,w=b.radius;i={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=sY}else i={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return hn(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=l4(c,u,l,f),h=l4(c,u,s,f);n=p.x,i=p.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(d,l,f)},m=p8;var O=ha(ha(ha(ha({stroke:"#ccc",pointerEvents:"none"},f),i),tA(v,!1)),{},{payload:s,payloadIndex:p,className:(0,_.A)("recharts-tooltip-cursor",v.className)});return(0,o.isValidElement)(v)?(0,o.cloneElement)(v,O):(0,o.createElement)(m,O)}var hu=["item"],hl=["children","className","width","height","style","compact","title","desc"];function hs(t){return(hs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hf(){return(hf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||hb(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hh(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function hd(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hd=function(){return!!t})()}function hy(t){return(hy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hv(t,e){return(hv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hm(t){return function(t){if(Array.isArray(t))return hg(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hb(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hb(t,e){if(t){if("string"==typeof t)return hg(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hg(t,e)}}function hg(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hx(Object(r),!0).forEach(function(e){hO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hO(t,e,r){return(e=hj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hj(t){var e=function(t,e){if("object"!=hs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hs(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hs(e)?e:e+""}var hS={xAxis:["bottom","top"],yAxis:["left","right"]},hP={width:"100%",height:"100%"},hA={x:0,y:0};function hE(t){return t}var h_=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return hw(hw(hw({},n),l4(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return hw(hw(hw({},n),l4(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hA},hM=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hm(t),hm(r)):t},[]);return o.length>0?o:t&&t.length&&$(n)&&$(i)?t.slice(n,i+1):[]};function hk(t){return"number"===t?[0,"auto"]:void 0}var hT=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=hM(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=o.dataKey&&!o.allowDuplicatedCategory?Y(void 0===s?a:s,o.dataKey,n):s&&s[r]||a[r])?[].concat(hm(i),[lI(c,l)]):i},[])},hN=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=li(o,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hT(t,e,l,s),p=h_(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hC=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=lp(l,i);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hw(hw({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,w=h[o];if(e[w])return e;var O=hM(t.data,{graphicalItems:n.filter(function(t){var e;return(o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o])===w}),dataStartIndex:c,dataEndIndex:u}),j=O.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&$(n)&&$(i))return!0}return!1})(h.domain,v,d)&&(A=lT(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(_=ln(O,y,"category")));var S=hk(d);if(!A||0===A.length){var P,A,E,_,M,k=null!=(M=h.domain)?M:S;if(y){if(A=ln(O,y,d),"category"===d&&p){var T=H(A);m&&T?(E=A,A=tR()(0,j)):m||(A=lC(k,A,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hm(t),[e])},[]))}else if("category"===d)A=m?A.filter(function(t){return""!==t&&!J()(t)}):lC(k,A,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||J()(e)?t:[].concat(hm(t),[e])},[]);else if("number"===d){var N=ls(O,n.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===w&&(x||!i)}),y,i,l);N&&(A=N)}p&&("number"===d||"auto"!==b)&&(_=ln(O,y,"category"))}else A=p?tR()(0,j):a&&a[w]&&a[w].hasStack&&"number"===d?"expand"===f?[0,1]:l_(a[w].stackGroups,c,u):lf(O,n.filter(function(t){var e=o in t.props?t.props[o]:t.type.defaultProps[o],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),d,l,!0);"number"===d?(A=pw(s,A,w,i,g),k&&(A=lT(k,A,v))):"category"===d&&k&&A.every(function(t){return k.indexOf(t)>=0})&&(A=k)}return hw(hw({},e),{},hO({},w,hw(hw({},h),{},{axisType:i,domain:A,categoricalDomain:_,duplicateDomain:E,originalDomain:null!=(P=h.domain)?P:S,isCategorical:p,layout:l})))},{})},hI=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hM(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=lp(l,i),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hw(hw({},e.type.defaultProps),e.props):e.props)[o],m=hk("number");return t[v]?t:(d++,y=h?tR()(0,p):a&&a[v]&&a[v].hasStack?pw(s,y=l_(a[v].stackGroups,c,u),v,i):pw(s,y=lT(m,lf(f,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!i}),"number",l),n.defaultProps.allowDataOverflow),v,i),hw(hw({},t),{},hO({},v,hw(hw({axisType:i},n.defaultProps),{},{hide:!0,orientation:B()(hS,"".concat(i,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hD=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tw(l,i),p={};return f&&f.length?p=hC(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=hI(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hB=function(t){var e=G(t),r=ld(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tz()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lN(e,r)}},hR=function(t){var e=t.children,r=t.defaultShowTooltip,n=tO(e,lJ),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},hL=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hz=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tO(s,lJ),h=tO(s,rc),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hw(hw({},t),{},hO({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:hw(hw({},t),{},hO({},n,B()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hw(hw({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lJ.defaultProps.height),h&&e&&(v=lu(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hw(hw({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})};function hU(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function h$(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function hF(t){return(hF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hq(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hF(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hF(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hX(t,e,r){var n,i,o,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if($(h)||ei.isSsr)return hU(l,("number"==typeof h&&$(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nX(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var i,o=te()(d)?d(t.value,n):t.value;return"width"===b?(i=nX(o,{fontSize:e,letterSpacing:r}),fE({width:i.width+g.width,height:i.height+g.height},v)):nX(o,{fontSize:e,letterSpacing:r})[b]},w=l.length>=2?z(l[1].coordinate-l[0].coordinate):1,O=(n="width"===b,i=s.x,o=s.y,a=s.width,c=s.height,1===w?{start:n?i:o,end:n?i+a:o+c}:{start:n?i+a:o+c,end:n?i:o});return"equidistantPreserveStart"===h?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(o=function(){var e,o=null==n?void 0:n[l];if(void 0===o)return{v:hU(n,s)};var a=l,p=function(){return void 0===e&&(e=r(o,a)),e},h=o.coordinate,d=0===l||h$(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+i),l+=s)}())return o.v;return[]}(w,O,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(o){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=hW(hW({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),h$(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+i),a[c-1]=hW(hW({},s),{},{isShow:!0}))}for(var h=o?c-1:c,d=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var s=t*(o.coordinate-t*c()/2-u);a[e]=o=hW(hW({},o),{},{tickCoord:s<0?o.coordinate-s*t:o.coordinate})}else a[e]=o=hW(hW({},o),{},{tickCoord:o.coordinate});h$(t,o.tickCoord,c,u,l)&&(u=o.tickCoord+t*(c()/2+i),a[e]=hW(hW({},o),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(w,O,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,l=function(e){var n,l=o[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);o[e]=l=hW(hW({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else o[e]=l=hW(hW({},l),{},{tickCoord:l.coordinate});h$(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+i),o[e]=hW(hW({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return o}(w,O,x,l,f)).filter(function(t){return t.isShow})}var hG=["viewBox"],hH=["viewBox"],hV=["ticks"];function hY(t){return(hY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hZ(){return(hZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hQ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hK(Object(r),!0).forEach(function(e){h4(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hJ(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function h0(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h3(n.key),n)}}function h1(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h1=function(){return!!t})()}function h2(t){return(h2=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h5(t,e){return(h5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h4(t,e,r){return(e=h3(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h3(t){var e=function(t,e){if("object"!=hY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hY(e)?e:e+""}var h6=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=h2(r),(e=function(t,e){if(e&&("object"===hY(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h1()?Reflect.construct(r,i||[],h2(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&h5(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=hJ(t,hG),i=this.props,o=i.viewBox,a=hJ(i,hH);return!to(r,o)||!to(n,a)||!to(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=$(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=l+!d*f)-v*m)-v*y,o=b;break;case"left":n=i=t.coordinate,o=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=i=t.coordinate,o=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(i=l+d*f)+v*m)+v*y,o=b}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.orientation,c=t.mirror,u=t.axisLine,l=hQ(hQ(hQ({},tA(this.props,!1)),tA(u,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var s=+("top"===o&&!c||"bottom"===o&&c);l=hQ(hQ({},l),{},{x1:e,y1:r+s*i,x2:e+n,y2:r+s*i})}else{var f=+("left"===o&&!c||"right"===o&&c);l=hQ(hQ({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+i})}return a().createElement("line",hZ({},l,{className:(0,_.A)("recharts-cartesian-axis-line",B()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var i=this,o=this.props,c=o.tickLine,u=o.stroke,l=o.tick,s=o.tickFormatter,f=o.unit,p=hX(hQ(hQ({},this.props),{},{ticks:t}),e,r),h=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=tA(this.props,!1),v=tA(l,!1),m=hQ(hQ({},y),{},{fill:"none"},tA(c,!1)),b=p.map(function(t,e){var r=i.getTickLineCoord(t),o=r.line,b=r.tick,g=hQ(hQ(hQ(hQ({textAnchor:h,verticalAnchor:d},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tG,hZ({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},tp(i.props,t,e)),c&&a().createElement("line",hZ({},m,o,{className:(0,_.A)("recharts-cartesian-axis-tick-line",B()(c,"className"))})),l&&n.renderTickItem(l,g,"".concat(te()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,i=e.height,o=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=hJ(u,hV),f=l;return(te()(o)&&(f=o(l&&l.length>0?this.props:s)),n<=0||i<=0||!f||!f.length)?null:a().createElement(tG,{className:(0,_.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),sl.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):te()(t)?t(e):a().createElement(il,hZ({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&h0(n.prototype,e),r&&h0(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);function h8(t){return(h8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}h4(h6,"displayName","CartesianAxis"),h4(h6,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function h7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h7=function(){return!!t})()}function h9(t){return(h9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dt(t,e){return(dt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function de(t,e,r){return(e=dr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dr(t){var e=function(t,e){if("object"!=h8(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h8(e)?e:e+""}function dn(){return(dn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function di(t){var e=t.xAxisId,r=f2(),n=f5(),i=fJ(e);return null==i?null:a().createElement(h6,dn({},i,{className:(0,_.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return ld(t,!0)}}))}var da=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=h9(t),function(t,e){if(e&&("object"===h8(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h7()?Reflect.construct(t,e||[],h9(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dt(r,t),e=[{key:"render",value:function(){return a().createElement(di,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dr(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dc(t){return(dc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}de(da,"displayName","XAxis"),de(da,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function du(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(du=function(){return!!t})()}function dl(t){return(dl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ds(t,e){return(ds=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function df(t,e,r){return(e=dp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dp(t){var e=function(t,e){if("object"!=dc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dc(e)?e:e+""}function dh(){return(dh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var dd=function(t){var e=t.yAxisId,r=f2(),n=f5(),i=f1(e);return null==i?null:a().createElement(h6,dh({},i,{className:(0,_.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return ld(t,!0)}}))},dy=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dl(t),function(t,e){if(e&&("object"===dc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,du()?Reflect.construct(t,e||[],dl(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ds(r,t),e=[{key:"render",value:function(){return a().createElement(dd,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dp(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);df(dy,"displayName","YAxis"),df(dy,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dv=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,i=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hL(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tm(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hM(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hw(hw({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=x["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||tU(!1);var o=n[i];return hw(hw({},t),{},hO(hO({},r.axisType,o),"".concat(r.axisType,"Ticks"),ld(o)))},{}),A=P[v],E=P["".concat(v,"Ticks")],_=n&&n[j]&&n[j].hasStack&&lE(r,n[j].stackGroups),M=tm(r.type).indexOf("Bar")>=0,k=lN(A,E),T=[],N=m&&la({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var C,I,D=J()(O)?h:O,B=null!=(C=null!=(I=lN(A,E,!0))?I:D)?C:0;T=lc({barGap:f,barCategoryGap:p,bandSize:B!==k?B:k,sizeList:N[S],maxBarSize:D}),B!==k&&(T=T.map(function(t){return hw(hw({},t),{},{position:hw(hw({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:hw(hw({},R(hw(hw({},P),{},{displayedData:g,props:t,dataKey:w,item:r,bandSize:k,barPosition:T,offset:i,stackedData:_,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hO(hO(hO({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",o)),childIndex:tx(t.children).indexOf(r),item:r})}),b},d=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tj({props:i}))return null;var u=i.children,s=i.layout,p=i.stackOffset,d=i.data,y=i.reverseStackOrder,v=hL(s),m=v.numericAxisName,b=v.cateAxisName,g=tw(u,r),x=lj(d,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hw(hw({},t),{},hO({},r,hD(i,hw(hw({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:o,dataEndIndex:a}))))},{}),O=hz(hw(hw({},w),{},{props:i,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=f(i,w[t],O,t.replace("Map",""),e)});var j=hB(w["".concat(b,"Map")]),S=h(i,hw(hw({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return hw(hw({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(t){var r;function n(t){var r,i,c,u,l;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return u=n,l=[t],u=hy(u),hO(c=function(t,e){if(e&&("object"===hs(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hd()?Reflect.construct(u,l||[],hy(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hO(c,"accessibilityManager",new p_),hO(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;c.setState(hw({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:i},hw(hw({},c.state),{},{legendBBox:t}))))}}),hO(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hO(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return hw({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hO(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=hw(hw({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;te()(n)&&n(r,t)}}),hO(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?hw(hw({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;te()(n)&&n(r,t)}),hO(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hO(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hO(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hO(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;te()(r)&&r(e,t)}),hO(c,"handleOuterEvent",function(t){var e,r,n=tk(t),i=B()(c.props,"".concat(n));n&&te()(i)&&i(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),hO(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=hw(hw({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;te()(n)&&n(r,t)}}),hO(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;te()(e)&&e(c.getMouseInfo(t),t)}),hO(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;te()(e)&&e(c.getMouseInfo(t),t)}),hO(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hO(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hO(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hO(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;te()(e)&&e(c.getMouseInfo(t),t)}),hO(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;te()(e)&&e(c.getMouseInfo(t),t)}),hO(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pj.emit(pS,c.props.syncId,t,c.eventEmitterSymbol)}),hO(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,i=c.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(hw({dataStartIndex:o,dataEndIndex:a},d({props:c.props,dataStartIndex:o,dataEndIndex:a,updateId:i},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=hw(hw({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=hT(c.state,c.props.data,s),w=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hA;c.setState(hw(hw({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hO(c,"renderCursor",function(t){var r,n=c.state,i=n.isTooltipActive,o=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!=(r=t.props.active)?r:i,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(hc,{key:y,activeCoordinate:o,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hO(c,"renderPolarAxis",function(t,e,r){var n=B()(t,"type.axisType"),i=B()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?hw(hw({},a),t.props):t.props,l=i&&i[u["".concat(n,"Id")]];return(0,o.cloneElement)(t,hw(hw({},l),{},{className:(0,_.A)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:ld(l,!0)}))}),hO(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,i=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=G(u),f=G(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,o.cloneElement)(t,{polarAngles:Array.isArray(n)?n:ld(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:ld(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hO(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,i=e.height,a=c.props.margin||{},u=u3({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=hh(u,hu);return(0,o.cloneElement)(l,hw(hw({},f),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hO(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,i=tO(r,em);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=i.props.active)?t:u;return(0,o.cloneElement)(i,{viewBox:hw(hw({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hO(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,o.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lv(c.handleBrushChange,t.props.onChange),data:n,x:$(t.props.x)?t.props.x:a.left,y:$(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:$(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hO(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,o.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hO(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,i=t.basePoint,o=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hw(hw({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hw(hw({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:lo(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tA(s,!1)),tf(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(o))),i?c.push(n.renderActiveDot(s,hw(hw({},f),{},{cx:i.x,cy:i.y}),"".concat(u,"-basePoint-").concat(o))):a&&c.push(null),c}),hO(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=tO(c.props.children,em),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hw(hw({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O=!!(!g&&u&&p&&(b||x||w)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:lv(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(j={onMouseLeave:lv(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lv(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,o.cloneElement)(t,hw(hw({},n.props),j));if(O)if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var P="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());E=Y(d,P,f),_=y&&v&&Y(v,P,f)}else E=null==d?void 0:d[s],_=y&&v&&v[s];if(w||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,o.cloneElement)(t,hw(hw(hw({},n.props),j),{},{activeIndex:A})),null,null]}if(!J()(E))return[S].concat(hm(c.renderActivePoints({item:n,activePoint:E,basePoint:_,childIndex:s,isRange:y})))}else{var E,_,M,k=(null!=(M=c.getItemByXY(c.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,T=k.item,N=void 0===T?t:T,C=k.childIndex,I=hw(hw(hw({},n.props),j),{},{activeIndex:C});return[(0,o.cloneElement)(N,I),null,null]}return y?[S,null,null]:[S,null]}),hO(c,"renderCustomized",function(t,e,r){return(0,o.cloneElement)(t,hw(hw({key:"recharts-customized-".concat(r)},c.props),c.state))}),hO(c,"renderMap",{CartesianGrid:{handler:hE,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hE},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hE},YAxis:{handler:hE},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:W("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=k()(c.triggeredAfterMouseMove,null!=(i=t.throttleDelay)?i:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hv(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=tO(e,em);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hT(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hw(hw({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tE([tO(t.children,em)],[tO(this.props.children,em)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tO(this.props.children,em);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:i}return i}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hN(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=G(u).scale,h=G(l).scale,d=p&&p.invert?p.invert(i.chartX):null,y=h&&h.invert?h.invert(i.chartY):null;return hw(hw({},i),{},{xValue:d,yValue:y},f)}return f?hw(hw({},i),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?l7({x:i,y:o},G(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tO(t,em),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hw(hw({},tf(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pj.on(pS,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pj.removeListener(pS,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===tm(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,i=e.height,o=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:i,width:o})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hp(e,2),n=r[0],i=r[1];return hw(hw({},t),{},hO({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hp(e,2),n=r[0],i=r[1];return hw(hw({},t),{},hO({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hw(hw({},u.type.defaultProps),u.props):u.props,s=tm(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nA(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return l7(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(s2(a,n)||s5(a,n)||s4(a,n)){var h=function(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(s2(o,i)?e="trapezoids":s5(o,i)?e="sectors":s4(o,i)&&(e="points"),e),u=s2(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:s5(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:s4(o,i)?i.payload:{},l=a.filter(function(t,e){var r=c9()(u,t),n=o.props[c].filter(function(t){var e;return(s2(o,i)?e=s3:s5(o,i)?e=s6:s4(o,i)&&(e=s8),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hw(hw({},a),{},{childIndex:d}),payload:s4(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tj(this))return null;var n=this.props,i=n.children,o=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=tA(hh(n,hl),!1);if(s)return a().createElement(fQ,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tq,hf({},h,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tM(i,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!=(t=this.props.tabIndex)?t:0,h.role=null!=(e=this.props.role)?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return a().createElement(fQ,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",hf({className:(0,_.A)("recharts-wrapper",o),style:hw({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(t){r.container=t}}),a().createElement(tq,hf({},h,{width:c,height:u,title:f,desc:p,style:hP}),this.renderClipPath(),tM(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hj(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);hO(y,"displayName",e),hO(y,"defaultProps",hw({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hO(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,o=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hR(t);return hw(hw(hw({},p),{},{updateId:0},d(hw(hw({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||o!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!to(l,e.prevMargin)){var h=hR(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hw(hw({},hN(e,n,c)),{},{updateId:e.updateId+1}),m=hw(hw(hw({},h),y),v);return hw(hw(hw({},m),d(hw({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:i})}if(!tE(i,e.prevChildren)){var b,g,x,w,O=tO(i,lJ),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:s,S=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:f,P=J()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hw(hw({updateId:P},d(hw(hw({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:S})}return null}),hO(y,"renderActiveDot",function(t,e,r){var n;return n=(0,o.isValidElement)(t)?(0,o.cloneElement)(t,e):te()(t)?t(e):a().createElement(rl,e),a().createElement(tG,{className:"recharts-active-dot",key:r},n)});var v=(0,o.forwardRef)(function(t,e){return a().createElement(y,hf({},t,{ref:e}))});return v.displayName=y.displayName,v}({chartName:"BarChart",GraphicalChild:fm,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:da},{axisType:"yAxis",AxisComp:dy}],formatAxisMap:function(t,e,r,n,i){var o=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tO(u,fm);return l.reduce(function(o,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(Z);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var _=X(t.barCategoryGap,A*E),M=A*E/2;u=M-_-(M-_)/E*_}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,w&&(l=[l[1],l[0]]);var k=lm(y,i,f),T=k.scale,N=k.realScaleType;T.domain(m).range(l),lb(T);var C=lS(T,fw(fw({},y),{},{realScaleType:N}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[O]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[O]-d*y.width,h=r.top);var I=fw(fw(fw({},y),C),{},{realScaleType:N,x:p,y:h,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return I.bandSize=lN(I,C),y.hide||"xAxis"!==n?y.hide||(s[O]+=(d?-1:1)*I.width):s[O]+=(d?-1:1)*I.height,fw(fw({},o),{},fO({},a,I))},{})}}),dm=["x1","y1","x2","y2","key"],db=["offset"];function dg(t){return(dg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dx(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=dg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dg(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dO(){return(dO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dj(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var dS=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:i,ry:u,width:o,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dP(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(te()(t))r=t(e);else{var n=e.x1,i=e.y1,o=e.x2,c=e.y2,u=e.key,l=tA(dj(e,dm),!1),s=(l.offset,dj(l,db));r=a().createElement("line",dO({},s,{x1:n,y1:i,x2:o,y2:c,fill:"none",key:u}))}return r}function dA(t){var e=t.x,r=t.width,n=t.horizontal,i=void 0===n||n,o=t.horizontalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return dP(i,dw(dw({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dE(t){var e=t.y,r=t.height,n=t.vertical,i=void 0===n||n,o=t.verticalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return dP(i,dw(dw({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function d_(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:i+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dM(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,i=t.x,o=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:i+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:o,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dk=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return lh(hX(dw(dw(dw({},h6.defaultProps),r),{},{ticks:ld(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},dT=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return lh(hX(dw(dw(dw({},h6.defaultProps),r),{},{ticks:ld(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},dN={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dC(t){var e,r,n,i,c,u,l=f2(),s=f5(),f=(0,o.useContext)(fV),p=dw(dw({},t),{},{stroke:null!=(e=t.stroke)?e:dN.stroke,fill:null!=(r=t.fill)?r:dN.fill,horizontal:null!=(n=t.horizontal)?n:dN.horizontal,horizontalFill:null!=(i=t.horizontalFill)?i:dN.horizontalFill,vertical:null!=(c=t.vertical)?c:dN.vertical,verticalFill:null!=(u=t.verticalFill)?u:dN.verticalFill,x:$(t.x)?t.x:f.left,y:$(t.y)?t.y:f.top,width:$(t.width)?t.width:f.width,height:$(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=G((0,o.useContext)(fX)),w=f0();if(!$(y)||y<=0||!$(v)||v<=0||!$(h)||h!==+h||!$(d)||d!==+d)return null;var O=p.verticalCoordinatesGenerator||dk,j=p.horizontalCoordinatesGenerator||dT,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&te()(j)){var A=b&&b.length,E=j({yAxis:w?dw(dw({},w),{},{ticks:A?b:w.ticks}):void 0,width:l,height:s,offset:f},!!A||m);K(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dg(E),"]")),Array.isArray(E)&&(S=E)}if((!P||!P.length)&&te()(O)){var _=g&&g.length,M=O({xAxis:x?dw(dw({},x),{},{ticks:_?g:x.ticks}):void 0,width:l,height:s,offset:f},!!_||m);K(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dg(M),"]")),Array.isArray(M)&&(P=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dS,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dA,dO({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),a().createElement(dE,dO({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:w})),a().createElement(d_,dO({},p,{horizontalPoints:S})),a().createElement(dM,dO({},p,{verticalPoints:P})))}function dI(){let[t,e]=(0,o.useState)("avanzamento"),[r,n]=(0,o.useState)("month"),[a,_]=(0,o.useState)(null),[M,k]=(0,o.useState)(null),[T,N]=(0,o.useState)(null),[C,I]=(0,o.useState)(null),[D,B]=(0,o.useState)(!0),[R,L]=(0,o.useState)(""),{user:z,cantiere:U,isLoading:$}=(0,p.A)(),F=()=>{U?.id_cantiere&&(B(!0),L(""),I(null),k(null),N(null),setTimeout(()=>{window.location.reload()},100))},q=async(t,e="pdf")=>{try{let e,r=U?.id_cantiere;if(!r)return;switch(t){case"progress":e=await h.ug.getReportProgress(r);break;case"boq":e=await h.ug.getReportBOQ(r);break;case"utilizzo-bobine":e=await h.ug.getReportUtilizzoBobine(r);break;default:return}e.data.file_url&&window.open(e.data.file_url,"_blank")}catch(t){}};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-slate-900",children:"Reports"}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:U?.commessa?`Cantiere: ${U.commessa}`:"Seleziona un cantiere per visualizzare i report"})]}),(0,i.jsx)("div",{className:"flex gap-2",children:(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:F,children:[(0,i.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Aggiorna"]})})]}),D||$?(0,i.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(y.A,{className:"h-6 w-6 animate-spin"}),(0,i.jsx)("span",{children:"Caricamento report..."})]})}):R?(0,i.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)(v.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,i.jsx)("span",{className:"text-amber-800 font-medium",children:R.includes("Nessun cantiere selezionato")||R.includes("Cantiere non selezionato")?"Cantiere non selezionato":R.includes("timeout")||R.includes("Timeout")?"Timeout API":"Errore caricamento report"})]}),(0,i.jsx)("p",{className:"text-amber-700 mb-4",children:R}),R.includes("timeout")||R.includes("Timeout")?(0,i.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded",children:(0,i.jsxs)("p",{className:"text-blue-800 text-sm",children:["\uD83D\uDCA1 ",(0,i.jsx)("strong",{children:"Suggerimento:"})," Le API stanno impiegando pi\xf9 tempo del previsto. Prova ad aggiornare la pagina o riprova tra qualche minuto."]})}):null,(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(u.$,{onClick:()=>window.location.href="/cantieri",className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Gestisci Cantieri"}),(0,i.jsxs)(u.$,{variant:"outline",onClick:F,className:"border-amber-600 text-amber-700 hover:bg-amber-100",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Riprova"]})]})]}):(0,i.jsxs)(f.tU,{value:t,onValueChange:e,className:"w-full",children:[(0,i.jsxs)(f.j7,{className:"grid w-full grid-cols-4",children:[(0,i.jsxs)(f.Xi,{value:"avanzamento",className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-4 w-4"}),"Avanzamento"]}),(0,i.jsxs)(f.Xi,{value:"boq",className:"flex items-center gap-2",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),"BOQ"]}),(0,i.jsxs)(f.Xi,{value:"bobine",className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),"Bobine"]}),(0,i.jsxs)(f.Xi,{value:"produttivita",className:"flex items-center gap-2",children:[(0,i.jsx)(x.A,{className:"h-4 w-4"}),"Produttivit\xe0"]})]}),(0,i.jsx)(f.av,{value:"avanzamento",className:"space-y-6",children:C?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Totali"}),(0,i.jsx)(m.A,{className:"h-4 w-4 text-blue-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[C.content.metri_totali?.toLocaleString()||0,"m"]}),(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[C.content.totale_cavi||0," cavi totali"]})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-green-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Posati"}),(0,i.jsx)(w.A,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[C.content.metri_posati?.toLocaleString()||0,"m"]}),(0,i.jsx)(s.k,{value:C.content.percentuale_avanzamento||0,className:"mt-2"}),(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[C.content.percentuale_avanzamento?.toFixed(1)||0,"% completato"]})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Media Giornaliera"}),(0,i.jsx)(O.A,{className:"h-4 w-4 text-purple-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[C.content.media_giornaliera?.toFixed(1)||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"metri/giorno"}),(0,i.jsxs)("div",{className:"flex items-center mt-2",children:[(0,i.jsx)(j.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-purple-600",children:[C.content.giorni_lavorativi_effettivi||0," giorni attivi"]})]})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento"}),(0,i.jsx)(S.A,{className:"h-4 w-4 text-orange-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:C.content.data_completamento||"N/A"}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"stima completamento"}),(0,i.jsxs)("div",{className:"flex items-center mt-2",children:[(0,i.jsx)(P.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-orange-600",children:[C.content.giorni_stimati||0," giorni rimanenti"]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{children:"Posa Recente"}),(0,i.jsx)(c.BT,{children:"Ultimi 10 giorni di attivit\xe0"})]}),(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>q("progress","pdf"),children:[(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"PDF"]})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)(tD,{width:"100%",height:300,children:(0,i.jsxs)(dv,{data:C.content.posa_recente||[],children:[(0,i.jsx)(dC,{strokeDasharray:"3 3"}),(0,i.jsx)(da,{dataKey:"data"}),(0,i.jsx)(dy,{}),(0,i.jsx)(em,{}),(0,i.jsx)(fm,{dataKey:"metri",fill:"#3b82f6",name:"Metri Posati"})]})})})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Stato Cavi"}),(0,i.jsx)(c.BT,{children:"Distribuzione per stato di avanzamento"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cavi Posati"}),(0,i.jsx)(l.E,{variant:"secondary",children:C.content.cavi_posati||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cavi Rimanenti"}),(0,i.jsx)(l.E,{variant:"outline",children:C.content.cavi_rimanenti||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Percentuale Cavi"}),(0,i.jsxs)(l.E,{variant:"default",children:[C.content.percentuale_cavi?.toFixed(1)||0,"%"]})]})]})})]})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(m.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato di avanzamento disponibile"})]})}),(0,i.jsx)(f.av,{value:"boq",className:"space-y-6",children:M?.error?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-4 text-amber-500"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-slate-700 mb-2",children:"API BOQ Temporaneamente Non Disponibile"}),(0,i.jsx)("p",{className:"text-slate-500 mb-4",children:"Il servizio BOQ sta riscontrando problemi di performance."}),(0,i.jsx)("p",{className:"text-sm text-slate-400",children:"Stiamo lavorando per risolvere il problema."})]}):M?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"\uD83D\uDCCB Bill of Quantities - Distinta Materiali"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Riepilogo completo dei materiali per tipologia di cavo"})]}),(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>q("boq","excel"),children:[(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Excel"]})]}),M.content.metri_orfani&&M.content.metri_orfani.metri_orfani_totali>0&&(0,i.jsxs)(c.Zp,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(c.aR,{className:"pb-3",children:(0,i.jsxs)(c.ZB,{className:"text-red-700 flex items-center",children:[(0,i.jsx)(E.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDEA8 METRI POSATI SENZA TRACCIABILIT\xc0 BOBINA"]})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("p",{className:"text-red-800 font-medium mb-2",children:[(0,i.jsxs)("strong",{children:[M.content.metri_orfani.metri_orfani_totali,"m"]})," installati con BOBINA_VUOTA (",M.content.metri_orfani.num_cavi_orfani," cavi)"]}),(0,i.jsx)("div",{className:"text-sm text-red-700 space-y-1",children:Array.isArray(M.content.metri_orfani.dettaglio_per_categoria)?M.content.metri_orfani.dettaglio_per_categoria.map((t,e)=>(0,i.jsxs)("div",{children:["• ",(0,i.jsxs)("strong",{children:[t.tipologia," ",t.formazione]}),": ",t.metri_orfani,"m (",t.num_cavi," cavi)"]},e)):(0,i.jsx)("div",{children:"Dettaglio metri orfani non disponibile"})}),(0,i.jsx)("div",{className:"mt-3 p-3 bg-amber-50 border border-amber-200 rounded",children:(0,i.jsxs)("p",{className:"text-amber-800 text-sm",children:["⚠️ ",(0,i.jsx)("strong",{children:"NOTA:"})," I metri orfani NON sono inclusi nel calcolo acquisti. Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti."]})})]})]}),M.content.riepilogo&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri da Acquistare"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[M.content.riepilogo.totale_metri_mancanti?.toLocaleString()||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"per completamento progetto"})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-green-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Residui"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[M.content.riepilogo.totale_metri_residui?.toLocaleString()||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"disponibili in magazzino"})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[M.content.riepilogo.percentuale_completamento?.toFixed(1)||0,"%"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"progetto completato"})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Categorie"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:M.content.riepilogo.categorie_necessitano_acquisto||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"necessitano acquisto"})]})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Distinta Materiali"}),(0,i.jsx)(c.BT,{children:"Fabbisogno materiali raggruppati per tipologia e formazione"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Cavi"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Teorici"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Posati"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri da Posare"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Bobine"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Mancanti"}),(0,i.jsx)("th",{className:"text-center p-2",children:"Acquisto"})]})}),(0,i.jsx)("tbody",{children:M.content.distinta_materiali?.map((t,e)=>(0,i.jsxs)("tr",{className:`border-b hover:bg-slate-50 ${t.ha_bobina_vuota?"bg-red-50":""}`,children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:t.tipologia}),(0,i.jsx)("td",{className:"p-2",children:t.formazione}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.num_cavi}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_teorici_totali?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_reali_posati?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_da_posare?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.num_bobine}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_residui?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right font-medium",children:t.metri_mancanti>0?(0,i.jsxs)("span",{className:"text-red-600",children:[t.metri_mancanti?.toLocaleString(),"m"]}):(0,i.jsx)("span",{className:"text-green-600",children:"0m"})}),(0,i.jsx)("td",{className:"p-2 text-center",children:t.necessita_acquisto?(0,i.jsx)(l.E,{variant:"destructive",children:"S\xec"}):(0,i.jsx)(l.E,{variant:"secondary",children:"No"})})]},e))})]})})})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Bobine Disponibili"}),(0,i.jsx)(c.BT,{children:"Inventario bobine per tipologia"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Numero Bobine"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Disponibili"})]})}),(0,i.jsx)("tbody",{children:M.content.bobine_per_tipo?.map((t,e)=>(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50",children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:t.tipologia}),(0,i.jsx)("td",{className:"p-2",children:t.formazione}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.num_bobine}),(0,i.jsx)("td",{className:"p-2 text-right",children:(0,i.jsx)("span",{className:t.metri_disponibili>0?"text-green-600":"text-red-600",children:t.metri_disponibili?.toLocaleString()})})]},e))})]})})})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(b.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato BOQ disponibile"})]})}),(0,i.jsx)(f.av,{value:"bobine",className:"space-y-6",children:T?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Utilizzo Bobine"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Stato e utilizzo delle bobine"})]}),(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>q("utilizzo-bobine","excel"),children:[(0,i.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Excel"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Totale Bobine"}),(0,i.jsx)(g.A,{className:"h-4 w-4 text-blue-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:T.content.totale_bobine||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"bobine nel cantiere"})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Bobine Attive"}),(0,i.jsx)(j.A,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:T.content.bobine?.filter(t=>"In uso"===t.stato||"Disponibile"===t.stato).length||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"disponibili/in uso"})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Utilizzo Medio"}),(0,i.jsx)(O.A,{className:"h-4 w-4 text-purple-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[T.content.bobine?.length>0?(T.content.bobine.reduce((t,e)=>t+(e.percentuale_utilizzo||0),0)/T.content.bobine.length).toFixed(1):0,"%"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"utilizzo medio"})]})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Dettaglio Bobine"}),(0,i.jsx)(c.BT,{children:"Stato dettagliato di tutte le bobine"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Codice"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Totali"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Utilizzati"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Utilizzo %"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Stato"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Cavi"})]})}),(0,i.jsx)("tbody",{children:T.content.bobine?.map((t,e)=>(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50",children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:t.codice}),(0,i.jsx)("td",{className:"p-2",children:t.tipologia}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_totali?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_utilizzati?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_residui?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)("span",{children:[t.percentuale_utilizzo?.toFixed(1),"%"]}),(0,i.jsx)(s.k,{value:Math.min(t.percentuale_utilizzo||0,100),className:"w-16 h-2"})]})}),(0,i.jsx)("td",{className:"p-2",children:(0,i.jsx)(l.E,{variant:"Disponibile"===t.stato?"default":"In uso"===t.stato?"secondary":"Terminata"===t.stato?"outline":"Over"===t.stato?"destructive":"outline",children:t.stato})}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.totale_cavi_associati||0})]},e))})]})})})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(g.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato bobine disponibile"})]})}),(0,i.jsx)(f.av,{value:"produttivita",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Produttivit\xe0"}),(0,i.jsx)("p",{children:"Funzionalit\xe0 in fase di sviluppo"}),(0,i.jsx)("p",{className:"text-sm mt-2",children:"Includer\xe0 calcoli IAP, statistiche team e analisi performance"})]})})]})]})})}dC.displayName="CartesianGrid"},7383:(t,e,r)=>{var n=r(67009),i=r(32269),o=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(i(r)&&o(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),i=r(52931),o=r(32269);t.exports=function(t){return o(t)?n(t):i(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),i=r(89624),o=r(47282),a=o&&o.isTypedArray;t.exports=a?i(a):n},10653:(t,e,r)=>{var n=r(21456),i=r(63979),o=r(7651);t.exports=function(t){return n(t,o,i)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11273:(t,e,r)=>{"use strict";r.d(e,{A:()=>a,q:()=>o});var n=r(43210),i=r(60687);function o(t,e){let r=n.createContext(e),o=t=>{let{children:e,...o}=t,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:e})};return o.displayName=t+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==e)return e;throw Error(`\`${i}\` must be used within \`${t}\``)}]}function a(t,e=[]){let r=[],o=()=>{let e=r.map(t=>n.createContext(t));return function(r){let i=r?.[t]||e;return n.useMemo(()=>({[`__scope${t}`]:{...r,[t]:i}}),[r,i])}};return o.scopeName=t,[function(e,o){let a=n.createContext(o),c=r.length;r=[...r,o];let u=e=>{let{scope:r,children:o,...u}=e,l=r?.[t]?.[c]||a,s=n.useMemo(()=>u,Object.values(u));return(0,i.jsx)(l.Provider,{value:s,children:o})};return u.displayName=e+"Provider",[u,function(r,i){let u=i?.[t]?.[c]||a,l=n.useContext(u);if(l)return l;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let r=()=>{let r=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let i=r.reduce((e,{useScope:r,scopeName:n})=>{let i=r(t)[`__scope${n}`];return{...e,...i}},{});return n.useMemo(()=>({[`__scope${e.scopeName}`]:i}),[i])}};return r.scopeName=e.scopeName,r}(o,...e)]}},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),i=r(55048),o=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return a;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},12412:t=>{"use strict";t.exports=require("assert")},13495:(t,e,r)=>{"use strict";r.d(e,{c:()=>i});var n=r(43210);function i(t){let e=n.useRef(t);return n.useEffect(()=>{e.current=t}),n.useMemo(()=>(...t)=>e.current?.(...t),[])}},14163:(t,e,r)=>{"use strict";r.d(e,{hO:()=>u,sG:()=>c});var n=r(43210),i=r(51215),o=r(8730),a=r(60687),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,o.TL)(`Primitive.${e}`),i=n.forwardRef((t,n)=>{let{asChild:i,...o}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:e,{...o,ref:n})});return i.displayName=`Primitive.${e}`,{...t,[e]:i}},{});function u(t,e){t&&i.flushSync(()=>t.dispatchEvent(e))}},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return i(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),i=r(27467);t.exports=function t(e,r,o,a,c){return e===r||(null!=e&&null!=r&&(i(e)||i(r))?n(e,r,o,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),i=r(46063),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},15909:(t,e,r)=>{var n=r(87506),i=r(66930),o=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(o||i),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),i=r(1707),o=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return i(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(o)),c(a(t,function(t,r,i){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),i=r(22),o=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:o(r);return u<0&&(u=a(c+u,0)),n(t,i(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this}},20540:(t,e,r)=>{var n=r(55048),i=r(70151),o=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,o=i();if(g(o))return w(o);p=setTimeout(x,(t=o-h,r=o-d,n=e-t,v?c(n,s-r):n))}function w(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function O(){var t,r=i(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(o(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(i())},O}},20623:(t,e,r)=>{var n=r(15871),i=r(40491),o=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=i(r,t);return void 0===a&&a===e?o(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}},21456:(t,e,r)=>{var n=r(41693),i=r(40542);t.exports=function(t,e,r){var o=e(t);return i(t)?o:n(o,r(t))}},21592:(t,e,r)=>{var n=r(42205),i=r(61837);t.exports=function(t,e){return n(i(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,o,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:i.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(o)var g=u?o(b,m,p,e,t,c):o(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,o,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),i=r(32269),o=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},25541:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27006:(t,e,r)=>{var n=r(46328),i=r(99525),o=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!i(e,function(t,e){if(!o(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},28837:(t,e,r)=>{var n=r(57797),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():i.call(e,r,1),--this.size,!0)}},28947:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},28977:(t,e,r)=>{var n=r(11539),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-i?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),i=r(70222),o=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?i(t):o(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),i=r(658),o=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!i||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),i=r(69619);t.exports=function(t){return null!=t&&i(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),i=r(67619),o=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:i(t,e)?[t]:o(a(t))}},35163:(t,e,r)=>{var n=r(15451),i=r(27467),o=Object.prototype,a=o.hasOwnProperty,c=o.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return i(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),i=r(4999),o=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new i(t),new i(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),i=r(92662);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),i=r(27006),o=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e);x=x==f?h:x,w=w==f?h:w;var O=x==h,j=w==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(t)?i(t,e,r,y,v,m):o(t,e,x,r,y,v,m);if(!(1&r)){var P=O&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,_=A?e.value():e;return m||(m=new n),v(E,_,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),i=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(i,""):t}},38404:(t,e,r)=>{var n=r(29395),i=r(65932),o=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40228:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var i=null==t?void 0:n(t,e);return void 0===i?r:i}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(r=r>i?i:r)<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(i);++n<i;)o[n]=t[n+e];return o}},41547:(t,e,r)=>{var n=r(61548),i=r(90851);t.exports=function(t,e){var r=i(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),i=r(85450);t.exports=function t(e,r,o,a,c){var u=-1,l=e.length;for(o||(o=i),c||(c=[]);++u<l;){var s=e[u];r>0&&o(s)?r>1?t(s,r-1,o,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},44493:(t,e,r)=>{"use strict";r.d(e,{BT:()=>u,Wu:()=>l,ZB:()=>c,Zp:()=>o,aR:()=>a});var n=r(60687);r(43210);var i=r(4780);function o({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...e})}function a({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...e})}function c({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...e})}function u({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...e})}function l({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...e})}},45058:(t,e,r)=>{var n=r(42082),i=r(8852),o=r(67619),a=r(46436);t.exports=function(t){return o(t)?n(a(t)):i(t)}},45180:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),i=r(48088),o=r(88170),a=r.n(o),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75900)),"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},45583:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45603:(t,e,r)=>{var n=r(20540),i=r(55048);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46059:(t,e,r)=>{"use strict";r.d(e,{C:()=>a});var n=r(43210),i=r(98599),o=r(66156),a=t=>{let{present:e,children:r}=t,a=function(t){var e,r;let[i,a]=n.useState(),u=n.useRef(null),l=n.useRef(t),s=n.useRef("none"),[f,p]=(e=t?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((t,e)=>r[t][e]??t,e));return n.useEffect(()=>{let t=c(u.current);s.current="mounted"===f?t:"none"},[f]),(0,o.N)(()=>{let e=u.current,r=l.current;if(r!==t){let n=s.current,i=c(e);t?p("MOUNT"):"none"===i||e?.display==="none"?p("UNMOUNT"):r&&n!==i?p("ANIMATION_OUT"):p("UNMOUNT"),l.current=t}},[t,p]),(0,o.N)(()=>{if(i){let t,e=i.ownerDocument.defaultView??window,r=r=>{let n=c(u.current).includes(r.animationName);if(r.target===i&&n&&(p("ANIMATION_END"),!l.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",t=e.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=t=>{t.target===i&&(s.current=c(u.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{e.clearTimeout(t),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}p("ANIMATION_END")},[i,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(t=>{u.current=t?getComputedStyle(t):null,a(t)},[])}}(e),u="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),l=(0,i.s)(a.ref,function(t){let e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,r=e&&"isReactWarning"in e&&e.isReactWarning;return r?t.ref:(r=(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?t.props.ref:t.props.ref||t.ref}(u));return"function"==typeof r||a.isPresent?n.cloneElement(u,{ref:l}):null};function c(t){return t?.animationName||"none"}a.displayName="Presence"},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),i=r(66354),o=r(11424);t.exports=function(t,e){return o(i(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),i=r(89185),o=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=i,a.prototype.has=o,t.exports=a},46436:(t,e,r)=>{var n=r(49227),i=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-i?"-0":e}},46657:(t,e,r)=>{"use strict";r.d(e,{k:()=>w});var n=r(60687),i=r(43210),o=r(11273),a=r(14163),c="Progress",[u,l]=(0,o.A)(c),[s,f]=u(c),p=i.forwardRef((t,e)=>{var r,i;let{__scopeProgress:o,value:c=null,max:u,getValueLabel:l=y,...f}=t;(u||0===u)&&!b(u)&&console.error((r=`${u}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=b(u)?u:100;null===c||g(c,p)||console.error((i=`${c}`,`Invalid prop \`value\` of value \`${i}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=g(c,p)?c:null,d=m(h)?l(h,p):void 0;return(0,n.jsx)(s,{scope:o,value:h,max:p,children:(0,n.jsx)(a.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":m(h)?h:void 0,"aria-valuetext":d,role:"progressbar","data-state":v(h,p),"data-value":h??void 0,"data-max":p,...f,ref:e})})});p.displayName=c;var h="ProgressIndicator",d=i.forwardRef((t,e)=>{let{__scopeProgress:r,...i}=t,o=f(h,r);return(0,n.jsx)(a.sG.div,{"data-state":v(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...i,ref:e})});function y(t,e){return`${Math.round(t/e*100)}%`}function v(t,e){return null==t?"indeterminate":t===e?"complete":"loading"}function m(t){return"number"==typeof t}function b(t){return m(t)&&!isNaN(t)&&t>0}function g(t,e){return m(t)&&!isNaN(t)&&t<=e&&t>=0}d.displayName=h;var x=r(4780);function w({className:t,value:e,...r}){return(0,n.jsx)(p,{"data-slot":"progress",className:(0,x.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...r,children:(0,n.jsx)(d,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(e||0)}%)`}})})}},47212:(t,e,r)=>{var n=r(87270),i=r(30316),o=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.process,c=function(){try{var t=o&&o.require&&o.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),i=r(91928),o=r(48169);t.exports=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:o},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[i,o,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},49227:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"symbol"==typeof t||i(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(i,function(t,r,n,i){e.push(n?i.replace(o,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var a=t[r];e(a,r,t)&&(o[i++]=a)}return o}},52823:(t,e,r)=>{var n=r(85406),i=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!i&&i in t}},52931:(t,e,r)=>{var n=r(77834),i=r(89605),o=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),i=r(32269);t.exports=function(t,e){var r=-1,o=i(t)?Array(t.length):[];return n(t,function(t,n,i){o[++r]=e(t,n,i)}),o}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,i){if(null==r)return r;if(!n(r))return t(r,i);for(var o=r.length,a=e?o:-1,c=Object(r);(e?a--:++a<o)&&!1!==i(c[a],a,c););return r}}},56770:(t,e,r)=>{"use strict";r.d(e,{tU:()=>H,av:()=>Z,j7:()=>V,Xi:()=>Y});var n=r(60687),i=r(43210),o=r(70569),a=r(11273),c=r(9510),u=r(98599),l=r(96963),s=r(14163),f=r(13495),p=r(65551),h=r(43),d="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,g]=(0,c.N)(v),[x,w]=(0,a.A)(v,[g]),[O,j]=x(v),S=i.forwardRef((t,e)=>(0,n.jsx)(m.Provider,{scope:t.__scopeRovingFocusGroup,children:(0,n.jsx)(m.Slot,{scope:t.__scopeRovingFocusGroup,children:(0,n.jsx)(P,{...t,ref:e})})}));S.displayName=v;var P=i.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:c=!1,dir:l,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:j=!1,...S}=t,P=i.useRef(null),A=(0,u.s)(e,P),E=(0,h.jH)(l),[_,k]=(0,p.i)({prop:m,defaultProp:g??null,onChange:x,caller:v}),[T,N]=i.useState(!1),C=(0,f.c)(w),I=b(r),D=i.useRef(!1),[B,R]=i.useState(0);return i.useEffect(()=>{let t=P.current;if(t)return t.addEventListener(d,C),()=>t.removeEventListener(d,C)},[C]),(0,n.jsx)(O,{scope:r,orientation:a,dir:E,loop:c,currentTabStopId:_,onItemFocus:i.useCallback(t=>k(t),[k]),onItemShiftTab:i.useCallback(()=>N(!0),[]),onFocusableItemAdd:i.useCallback(()=>R(t=>t+1),[]),onFocusableItemRemove:i.useCallback(()=>R(t=>t-1),[]),children:(0,n.jsx)(s.sG.div,{tabIndex:T||0===B?-1:0,"data-orientation":a,...S,ref:A,style:{outline:"none",...t.style},onMouseDown:(0,o.m)(t.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.m)(t.onFocus,t=>{let e=!D.current;if(t.target===t.currentTarget&&e&&!T){let e=new CustomEvent(d,y);if(t.currentTarget.dispatchEvent(e),!e.defaultPrevented){let t=I().filter(t=>t.focusable);M([t.find(t=>t.active),t.find(t=>t.id===_),...t].filter(Boolean).map(t=>t.ref.current),j)}}D.current=!1}),onBlur:(0,o.m)(t.onBlur,()=>N(!1))})})}),A="RovingFocusGroupItem",E=i.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:c=!1,tabStopId:u,children:f,...p}=t,h=(0,l.B)(),d=u||h,y=j(A,r),v=y.currentTabStopId===d,g=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:O}=y;return i.useEffect(()=>{if(a)return x(),()=>w()},[a,x,w]),(0,n.jsx)(m.ItemSlot,{scope:r,id:d,focusable:a,active:c,children:(0,n.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...p,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{a?y.onItemFocus(d):t.preventDefault()}),onFocus:(0,o.m)(t.onFocus,()=>y.onItemFocus(d)),onKeyDown:(0,o.m)(t.onKeyDown,t=>{if("Tab"===t.key&&t.shiftKey)return void y.onItemShiftTab();if(t.target!==t.currentTarget)return;let e=function(t,e,r){var n;let i=(n=t.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===e&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===e&&["ArrowUp","ArrowDown"].includes(i)))return _[i]}(t,y.orientation,y.dir);if(void 0!==e){if(t.metaKey||t.ctrlKey||t.altKey||t.shiftKey)return;t.preventDefault();let r=g().filter(t=>t.focusable).map(t=>t.ref.current);if("last"===e)r.reverse();else if("prev"===e||"next"===e){"prev"===e&&r.reverse();let n=r.indexOf(t.currentTarget);r=y.loop?function(t,e){return t.map((r,n)=>t[(e+n)%t.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>M(r))}}),children:"function"==typeof f?f({isCurrentTabStop:v,hasTabStop:null!=O}):f})})});E.displayName=A;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(t,e=!1){let r=document.activeElement;for(let n of t)if(n===r||(n.focus({preventScroll:e}),document.activeElement!==r))return}var k=r(46059),T="Tabs",[N,C]=(0,a.A)(T,[w]),I=w(),[D,B]=N(T),R=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:i,onValueChange:o,defaultValue:a,orientation:c="horizontal",dir:u,activationMode:f="automatic",...d}=t,y=(0,h.jH)(u),[v,m]=(0,p.i)({prop:i,onChange:o,defaultProp:a??"",caller:T});return(0,n.jsx)(D,{scope:r,baseId:(0,l.B)(),value:v,onValueChange:m,orientation:c,dir:y,activationMode:f,children:(0,n.jsx)(s.sG.div,{dir:y,"data-orientation":c,...d,ref:e})})});R.displayName=T;var L="TabsList",z=i.forwardRef((t,e)=>{let{__scopeTabs:r,loop:i=!0,...o}=t,a=B(L,r),c=I(r);return(0,n.jsx)(S,{asChild:!0,...c,orientation:a.orientation,dir:a.dir,loop:i,children:(0,n.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:e})})});z.displayName=L;var U="TabsTrigger",$=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:i,disabled:a=!1,...c}=t,u=B(U,r),l=I(r),f=W(u.baseId,i),p=X(u.baseId,i),h=i===u.value;return(0,n.jsx)(E,{asChild:!0,...l,focusable:!a,active:h,children:(0,n.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:f,...c,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{a||0!==t.button||!1!==t.ctrlKey?t.preventDefault():u.onValueChange(i)}),onKeyDown:(0,o.m)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&u.onValueChange(i)}),onFocus:(0,o.m)(t.onFocus,()=>{let t="manual"!==u.activationMode;h||a||!t||u.onValueChange(i)})})})});$.displayName=U;var F="TabsContent",q=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:o,forceMount:a,children:c,...u}=t,l=B(F,r),f=W(l.baseId,o),p=X(l.baseId,o),h=o===l.value,d=i.useRef(h);return i.useEffect(()=>{let t=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,n.jsx)(k.C,{present:a||h,children:({present:r})=>(0,n.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:p,tabIndex:0,...u,ref:e,style:{...t.style,animationDuration:d.current?"0s":void 0},children:r&&c})})});function W(t,e){return`${t}-trigger-${e}`}function X(t,e){return`${t}-content-${e}`}q.displayName=F;var G=r(4780);let H=R,V=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(z,{ref:r,className:(0,G.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...e}));V.displayName=z.displayName;let Y=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)($,{ref:r,className:(0,G.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...e}));Y.displayName=$.displayName;let Z=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(q,{ref:r,className:(0,G.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...e}));Z.displayName=q.displayName},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,i=n(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),i=r(35163),o=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(o(t)||i(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),i=r(52823),o=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!o(t)||i(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),i=r(22),o=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),i=r(40542),o=r(27467);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),i=r(6330),o=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return o.call(t,e)})}:i},65551:(t,e,r)=>{"use strict";r.d(e,{i:()=>c});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function c({prop:t,defaultProp:e,onChange:r=()=>{},caller:n}){let[o,c,u]=function({defaultProp:t,onChange:e}){let[r,n]=i.useState(t),o=i.useRef(r),c=i.useRef(e);return a(()=>{c.current=e},[e]),i.useEffect(()=>{o.current!==r&&(c.current?.(r),o.current=r)},[r,o]),[r,n,c]}({defaultProp:e,onChange:r}),l=void 0!==t,s=l?t:o;{let e=i.useRef(void 0!==t);i.useEffect(()=>{let t=e.current;if(t!==l){let e=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${t?"controlled":"uncontrolled"} to ${e}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}e.current=l},[l,n])}return[s,i.useCallback(e=>{if(l){let r="function"==typeof e?e(t):e;r!==t&&u.current?.(r)}else c(e)},[l,t,c,u])]}Symbol("RADIX:SYNC_STATE")},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var i=-1,o=t.criteria,a=e.criteria,c=o.length,u=r.length;++i<c;){var l=n(o[i],a[i]);if(l){if(i>=u)return l;return l*("desc"==r[i]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++i];if(!1===r(o[u],u,o))break}return e}}},66156:(t,e,r)=>{"use strict";r.d(e,{N:()=>i});var n=r(43210),i=globalThis?.document?n.useLayoutEffect:()=>{}},66354:(t,e,r)=>{var n=r(85244),i=Math.max;t.exports=function(t,e,r){return e=i(void 0===e?t.length-1:e,0),function(){for(var o=arguments,a=-1,c=i(o.length-e,0),u=Array(c);++a<c;)u[a]=o[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=o[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var i=e(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),i=r(34117),o=r(48385);t.exports=function(t){return i(t)?o(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),i=r(28837),o=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case i:return e}}}(t)===o}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),i=r(37575),o=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=i,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),i=r(22),o=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),i=r(49227),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||i(t))||a.test(t)||!o.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),i=r(99114),o=r(22);t.exports=function(t,e){var r={};return e=o(e,3),i(t,function(t,i,o){n(r,i,e(t,i,o))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=o.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var i=a.call(t);return n&&(e?t[c]=r:delete t[c]),i}},70440:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var n=r(31658);let i=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},70569:(t,e,r)=>{"use strict";function n(t,e,{checkForDefaultPrevented:r=!0}={}){return function(n){if(t?.(n),!1===r||!n.defaultPrevented)return e?.(n)}}r.d(e,{m:()=>n})},71960:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),i=r(93311),o=r(41132);t.exports=function(t){var e=i(t);return 1==e.length&&e[0][2]?o(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},75900:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx","default")},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78122:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78418:(t,e,r)=>{var n=r(67200),i=r(15871);t.exports=function(t,e,r,o){var a=r.length,c=a,u=!o;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(o)var d=o(f,p,s,t,e,h);if(!(void 0===d?i(p,f,3,o,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},80195:(t,e,r)=>{var n=r(79474),i=r(21367),o=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return i(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),i=r(1944),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,c=a&&a.exports===o?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||i},80458:(t,e,r)=>{var n=r(29395),i=r(69619),o=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return o(t)&&i(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,i=null===t,o=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||i&&c&&l||!r&&l||!o)return 1;if(!i&&!a&&!s&&t<e||s&&r&&o&&!i&&!a||u&&r&&o||!c&&o||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),i=r(35163),o=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=o(t),s=!r&&i(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},83997:t=>{"use strict";t.exports=require("tty")},84031:(t,e,r)=>{"use strict";var n=r(34452);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),i=r(35163),o=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||i(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),i="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||i||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),i=r(17518),o=r(46229),a=r(7383);t.exports=o(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),i(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function i(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=t.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n),r}i.Cache=n,t.exports=i},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),i=r(7383),o=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),i=r(84261),o=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),i=r(99180),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92311:(t,e,r)=>{Promise.resolve().then(r.bind(r,6335))},92662:(t,e,r)=>{var n=r(46328),i=r(80704),o=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=i,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=o;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),i=r(7651);t.exports=function(t){for(var e=i(t),r=e.length;r--;){var o=e[r],a=t[o];e[r]=[o,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),i=r(36959),o=r(2408);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},95746:(t,e,r)=>{var n=r(15909),i=r(29205),o=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),i=r(39774),o=r(74610);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},96834:(t,e,r)=>{"use strict";r.d(e,{E:()=>u});var n=r(60687);r(43210);var i=r(8730),o=r(24224),a=r(4780);let c=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function u({className:t,variant:e,asChild:r=!1,...o}){let u=r?i.DX:"span";return(0,n.jsx)(u,{"data-slot":"badge",className:(0,a.cn)(c({variant:e}),t),...o})}},96963:(t,e,r)=>{"use strict";r.d(e,{B:()=>u});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),c=0;function u(t){let[e,r]=i.useState(a());return(0,o.N)(()=>{t||r(t=>t??String(c++))},[t]),t||(e?`radix-${e}`:"")}},97887:(t,e,r)=>{Promise.resolve().then(r.bind(r,75900))},98451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),u=Array(c);c--;)u[o?c:++a]=t,t+=i;return u}},99114:(t,e,r)=>{var n=r(12344),i=r(7651);t.exports=function(t,e){return t&&n(t,e,i)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,538,658,797,615],()=>r(45180));module.exports=n})();